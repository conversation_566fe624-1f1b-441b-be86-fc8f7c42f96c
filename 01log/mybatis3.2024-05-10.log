[] 2024-05-10 18:35:43.624 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 48925 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-10 18:35:43.635 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-10 18:35:44.964 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tom<PERSON> initialized with port(s): 8081 (http)
[] 2024-05-10 18:35:44.982 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 18:35:44.993 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-10 18:35:44.993 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-10 18:35:45.149 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-10 18:35:45.150 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1369 ms
[] 2024-05-10 18:35:45.476 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 18:35:45.647 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-10 18:35:45.798 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 18:35:45.826 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-10 18:35:45.829 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 2.864 seconds (JVM running for 3.391)
[] 2024-05-10 18:38:02.155 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 18:38:08.590 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 49014 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-10 18:38:08.593 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-10 18:38:09.902 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-10 18:38:09.921 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 18:38:09.935 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-10 18:38:09.936 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-10 18:38:10.046 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-10 18:38:10.046 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1344 ms
[] 2024-05-10 18:38:10.247 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 18:38:10.370 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-10 18:38:10.509 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 18:38:10.543 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-10 18:38:10.548 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 2.676 seconds (JVM running for 3.161)
[] 2024-05-10 18:38:21.830 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-10 18:38:21.831 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-10 18:38:21.840 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 9 ms
[] 2024-05-10 18:38:44.916 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 18:38:49.084 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 49056 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-10 18:38:49.090 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-10 18:38:50.460 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-10 18:38:50.477 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 18:38:50.492 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-10 18:38:50.492 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-10 18:38:50.624 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-10 18:38:50.625 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1431 ms
[] 2024-05-10 18:38:50.897 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 18:38:51.024 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-10 18:38:51.329 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 18:38:51.411 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-10 18:38:51.420 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.589 seconds (JVM running for 4.088)
[] 2024-05-10 18:38:52.995 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-10 18:38:52.996 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-10 18:38:53.008 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 12 ms
[] 2024-05-10 18:44:27.024 [ERROR] [http-nio-8081-exec-4] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:59)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:32)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-10 19:23:01.470 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 19:23:08.000 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 50167 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-10 19:23:08.003 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-10 19:23:09.832 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-10 19:23:09.863 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 19:23:09.882 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-10 19:23:09.883 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-10 19:23:10.012 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-10 19:23:10.013 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1949 ms
[] 2024-05-10 19:23:10.280 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 19:23:10.397 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-10 19:23:10.555 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 19:23:10.614 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-10 19:23:10.619 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.428 seconds (JVM running for 4.23)
[] 2024-05-10 19:23:21.435 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-10 19:23:21.436 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-10 19:23:21.441 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 5 ms
[] 2024-05-10 21:26:03.456 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 21:26:10.723 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 53115 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-10 21:26:10.726 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-10 21:26:12.047 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-10 21:26:12.073 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 21:26:12.089 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-10 21:26:12.090 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-10 21:26:12.218 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-10 21:26:12.218 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1436 ms
[] 2024-05-10 21:26:12.495 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 21:26:12.655 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-10 21:26:12.824 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 21:26:12.870 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-10 21:26:12.875 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 2.979 seconds (JVM running for 3.783)
[] 2024-05-10 21:27:32.152 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-10 21:27:32.155 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-10 21:27:32.199 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 43 ms
[] 2024-05-10 22:18:36.377 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 22:18:47.262 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 54034 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-10 22:18:47.265 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-10 22:18:50.183 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-10 22:18:50.245 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 22:18:50.348 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-10 22:18:50.364 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-10 22:18:56.776 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 54042 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-10 22:18:56.782 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-10 22:18:58.552 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-10 22:18:58.571 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 22:18:58.585 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-10 22:18:58.586 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-10 22:18:58.753 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-10 22:18:58.753 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1891 ms
[] 2024-05-10 22:18:59.069 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 22:18:59.193 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-10 22:18:59.305 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 22:18:59.336 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-10 22:18:59.341 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.369 seconds (JVM running for 4.161)
[] 2024-05-10 22:19:25.818 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-10 22:19:25.820 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-10 22:19:25.843 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 22 ms
[] 2024-05-10 22:22:48.587 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 22:22:56.049 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 54251 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-10 22:22:56.052 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-10 22:22:57.706 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-10 22:22:57.721 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 22:22:57.733 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-10 22:22:57.734 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-10 22:22:57.829 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-10 22:22:57.830 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1672 ms
[] 2024-05-10 22:22:58.089 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 22:22:58.228 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-10 22:22:58.625 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 22:22:58.670 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-10 22:22:58.675 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.347 seconds (JVM running for 4.514)
[] 2024-05-10 22:23:17.664 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-10 22:23:17.667 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-10 22:23:17.694 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 25 ms
[] 2024-05-10 22:24:26.034 [ERROR] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:63)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-10 22:27:05.728 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 22:27:14.128 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 54408 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-10 22:27:14.132 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-10 22:27:15.833 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-10 22:27:15.849 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 22:27:15.862 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-10 22:27:15.862 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-10 22:27:15.971 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-10 22:27:15.972 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1770 ms
[] 2024-05-10 22:27:16.217 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 22:27:16.346 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-10 22:27:16.457 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 22:27:16.494 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-10 22:27:16.499 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.183 seconds (JVM running for 4.17)
[] 2024-05-10 22:27:27.801 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-10 22:27:28.413 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-10 22:27:28.847 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 433 ms
[] 2024-05-10 22:28:41.506 [ERROR] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:63)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-10 22:30:16.186 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 22:30:27.504 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 54550 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-10 22:30:27.533 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-10 22:30:30.418 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-10 22:30:30.435 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 22:30:30.453 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-10 22:30:30.454 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-10 22:30:30.784 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-10 22:30:30.785 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 3153 ms
[] 2024-05-10 22:30:31.901 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-10 22:30:32.229 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-10 22:30:32.698 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-10 22:30:32.770 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-10 22:30:32.783 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 7.434 seconds (JVM running for 8.073)
[] 2024-05-10 22:30:44.934 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-10 22:30:44.935 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-10 22:30:44.947 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 11 ms
