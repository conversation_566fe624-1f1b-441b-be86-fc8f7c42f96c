[] 2023-10-26 20:48:49.240 [DEBUG] [main] [org.springframework.boot.context.logging.ClasspathLoggingApplicationListener] 53 - Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.1.3.RELEASE/spring-boot-starter-web-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.1.3.RELEASE/spring-boot-starter-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.1.3.RELEASE/spring-boot-starter-logging-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.11.2/log4j-to-slf4j-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.11.2/log4j-api-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar, file:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.23/snakeyaml-1.23.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.1.3.RELEASE/spring-boot-starter-json-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.8/jackson-databind-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.8/jackson-core-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.8/jackson-datatype-jdk8-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.8/jackson-datatype-jsr310-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.8/jackson-module-parameter-names-2.9.8.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.1.3.RELEASE/spring-boot-starter-tomcat-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.16/tomcat-embed-core-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.16/tomcat-embed-el-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.16/tomcat-embed-websocket-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.14.Final/hibernate-validator-6.0.14.Final.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.4.0/classmate-1.4.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.1.5.RELEASE/spring-web-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.1.5.RELEASE/spring-beans-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.1.5.RELEASE/spring-webmvc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.1.5.RELEASE/spring-aop-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.1.5.RELEASE/spring-context-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.1.5.RELEASE/spring-expression-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.75/fastjson-1.2.75.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.1.9/druid-spring-boot-starter-1.1.9.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.9/druid-1.1.9.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.1.3.RELEASE/spring-boot-autoconfigure-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.15/mysql-connector-java-8.0.15.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.1.3.RELEASE/spring-boot-starter-jdbc-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.2.0/HikariCP-3.2.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.1.5.RELEASE/spring-jdbc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/statemachine/spring-statemachine-core/2.0.1.RELEASE/spring-statemachine-core-2.0.1.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.1.5.RELEASE/spring-tx-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.1.5.RELEASE/spring-messaging-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.1.5.RELEASE/spring-core-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.1.5.RELEASE/spring-jcl-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.16.22/lombok-1.16.22.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.2.5/hutool-all-5.2.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.19/commons-compress-1.19.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/30.1-jre/guava-30.1-jre.jar, file:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, file:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar, file:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.3.4/error_prone_annotations-2.3.4.jar, file:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/2.1.3.RELEASE/spring-boot-starter-thymeleaf-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring5/3.0.11.RELEASE/thymeleaf-spring5-3.0.11.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.0.11.RELEASE/thymeleaf-3.0.11.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.5.RELEASE/attoparser-2.0.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/extras/thymeleaf-extras-java8time/3.0.3.RELEASE/thymeleaf-extras-java8time-3.0.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/2.2.10/easyexcel-2.2.10.jar, file:/Users/<USER>/.m2/repository/cglib/cglib/3.1/cglib-3.1.jar, file:/Users/<USER>/.m2/repository/org/ow2/asm/asm/4.2/asm-4.2.jar, file:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.6.3/ehcache-3.6.3.jar, file:/Applications/IntelliJ%20IDEA%20CE.app/Contents/lib/idea_rt.jar]
[] 2023-10-26 20:48:49.425 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 50 - Starting MyBatisApplication on tomas.local with PID 17065 (/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes started by tomas in /Users/<USER>/code/github/SpringBoot-Learning)
[] 2023-10-26 20:48:49.426 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 675 - No active profile set, falling back to default profiles: default
[] 2023-10-26 20:48:49.427 [DEBUG] [main] [org.springframework.boot.SpringApplication] 703 - Loading source class com.tomas.mybaties3.MyBatisApplication
[] 2023-10-26 20:48:49.585 [DEBUG] [main] [org.springframework.boot.context.config.ConfigFileApplicationListener] 224 - Loaded config file 'file:/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/application.yml' (classpath:/application.yml)
[] 2023-10-26 20:48:49.586 [DEBUG] [main] [org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext] 594 - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@394df057
[] 2023-10-26 20:48:49.625 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
[] 2023-10-26 20:48:49.653 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
[] 2023-10-26 20:48:49.789 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai1.class]
[] 2023-10-26 20:48:49.791 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai2.class]
[] 2023-10-26 20:48:49.798 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/jdbc/UserJDBCDao.class]
[] 2023-10-26 20:48:49.857 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/HelloController.class]
[] 2023-10-26 20:48:49.859 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/PageController.class]
[] 2023-10-26 20:48:49.865 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/service/impl/VoteServiceImpl.class]
[] 2023-10-26 20:48:50.134 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:50.134 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:50.183 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:50.189 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:50.190 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:50.194 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:50.303 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.condition.BeanTypeRegistry'
[] 2023-10-26 20:48:50.369 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:50.370 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:50.522 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
[] 2023-10-26 20:48:50.539 [DEBUG] [main] [org.springframework.boot.autoconfigure.AutoConfigurationPackages] 206 - @EnableAutoConfiguration was declared on a class in the package 'com.tomas.mybaties3'. Automatic @Repository and @Entity scanning is enabled.
[] 2023-10-26 20:48:50.542 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 224 - Searching for mappers annotated with @Mapper
[] 2023-10-26 20:48:50.544 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 228 - Using auto-configuration base package 'com.tomas.mybaties3'
[] 2023-10-26 20:48:50.744 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.mapper.MapperScannerConfigurer'
[] 2023-10-26 20:48:50.746 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
[] 2023-10-26 20:48:50.770 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/TransitGoodsInfoMapper.class]
[] 2023-10-26 20:48:50.771 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/UserMapper.class]
[] 2023-10-26 20:48:50.775 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'transitGoodsInfoMapper' and 'com.tomas.mybaties3.dao.TransitGoodsInfoMapper' mapperInterface
[] 2023-10-26 20:48:50.777 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'transitGoodsInfoMapper'.
[] 2023-10-26 20:48:50.777 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'userMapper' and 'com.tomas.mybaties3.dao.UserMapper' mapperInterface
[] 2023-10-26 20:48:50.778 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
[] 2023-10-26 20:48:50.961 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
[] 2023-10-26 20:48:50.963 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationBeanFactoryMetadata'
[] 2023-10-26 20:48:50.963 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
[] 2023-10-26 20:48:50.964 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
[] 2023-10-26 20:48:50.965 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
[] 2023-10-26 20:48:50.974 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
[] 2023-10-26 20:48:50.975 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
[] 2023-10-26 20:48:50.979 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
[] 2023-10-26 20:48:50.982 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'methodValidationPostProcessor'
[] 2023-10-26 20:48:51.011 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'methodValidationPostProcessor' via factory method to bean named 'environment'
[] 2023-10-26 20:48:51.025 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSourceInitializerPostProcessor'
[] 2023-10-26 20:48:51.029 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
[] 2023-10-26 20:48:51.031 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
[] 2023-10-26 20:48:51.036 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
[] 2023-10-26 20:48:51.055 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
[] 2023-10-26 20:48:51.055 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
[] 2023-10-26 20:48:51.061 [DEBUG] [main] [org.springframework.ui.context.support.UiApplicationContextUtils] 85 - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@669253b7]
[] 2023-10-26 20:48:51.063 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
[] 2023-10-26 20:48:51.063 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
[] 2023-10-26 20:48:51.067 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
[] 2023-10-26 20:48:51.067 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
[] 2023-10-26 20:48:51.098 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionAttributeSource'
[] 2023-10-26 20:48:51.106 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionInterceptor'
[] 2023-10-26 20:48:51.148 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
[] 2023-10-26 20:48:51.148 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
[] 2023-10-26 20:48:51.159 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
[] 2023-10-26 20:48:51.160 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
[] 2023-10-26 20:48:51.164 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:48:51.195 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:48:51.200 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
[] 2023-10-26 20:48:51.202 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:48:51.205 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatWebServerFactoryCustomizer'
[] 2023-10-26 20:48:51.205 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration'
[] 2023-10-26 20:48:51.209 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'environment'
[] 2023-10-26 20:48:51.210 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:48:51.217 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
[] 2023-10-26 20:48:51.217 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
[] 2023-10-26 20:48:51.218 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration$$EnhancerBySpringCGLIB$$747536a5] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:51.219 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:48:51.222 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:48:51.271 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageCustomizer'
[] 2023-10-26 20:48:51.273 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
[] 2023-10-26 20:48:51.274 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$$EnhancerBySpringCGLIB$$cccc8588] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:51.276 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServletRegistration'
[] 2023-10-26 20:48:51.277 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
[] 2023-10-26 20:48:51.278 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration$$EnhancerBySpringCGLIB$$5327a473] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:51.279 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:48:51.284 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:48:51.286 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartConfigElement'
[] 2023-10-26 20:48:51.287 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
[] 2023-10-26 20:48:51.288 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration$$EnhancerBySpringCGLIB$$e365beb2] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:51.289 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2023-10-26 20:48:51.296 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2023-10-26 20:48:51.305 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServlet'
[] 2023-10-26 20:48:51.305 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
[] 2023-10-26 20:48:51.306 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration$$EnhancerBySpringCGLIB$$f1200fcc] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:51.307 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:48:51.308 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:48:51.334 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
[] 2023-10-26 20:48:51.347 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:48:51.347 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'dispatcherServletRegistration'
[] 2023-10-26 20:48:51.348 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'conventionErrorViewResolver'
[] 2023-10-26 20:48:51.348 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
[] 2023-10-26 20:48:51.349 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration$$EnhancerBySpringCGLIB$$e1615a28] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:51.350 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:48:51.355 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@394df057'
[] 2023-10-26 20:48:51.356 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:48:51.640 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 82 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2023-10-26 20:48:51.641 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 126 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2023-10-26 20:48:51.641 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 151 - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
[] 2023-10-26 20:48:51.677 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2023-10-26 20:48:51.695 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2023-10-26 20:48:51.705 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2023-10-26 20:48:51.706 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.16]
[] 2023-10-26 20:48:51.717 [INFO] [main] [org.apache.catalina.core.AprLifecycleListener] 173 - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
[] 2023-10-26 20:48:51.912 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2023-10-26 20:48:51.913 [DEBUG] [main] [org.springframework.web.context.ContextLoader] 288 - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
[] 2023-10-26 20:48:51.914 [INFO] [main] [org.springframework.web.context.ContextLoader] 296 - Root WebApplicationContext: initialization completed in 2328 ms
[] 2023-10-26 20:48:51.918 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statViewServletRegistrationBean'
[] 2023-10-26 20:48:51.919 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidStatViewServletConfiguration'
[] 2023-10-26 20:48:51.920 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-10-26 20:48:51.922 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'statViewServletRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-10-26 20:48:51.935 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webStatFilterRegistrationBean'
[] 2023-10-26 20:48:51.936 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration'
[] 2023-10-26 20:48:51.938 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'webStatFilterRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-10-26 20:48:51.949 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestContextFilter'
[] 2023-10-26 20:48:51.952 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hiddenHttpMethodFilter'
[] 2023-10-26 20:48:51.952 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
[] 2023-10-26 20:48:51.961 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'formContentFilter'
[] 2023-10-26 20:48:51.967 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'characterEncodingFilter'
[] 2023-10-26 20:48:51.980 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping filters: filterRegistrationBean urls=[/*], characterEncodingFilter urls=[/*], hiddenHttpMethodFilter urls=[/*], formContentFilter urls=[/*], requestContextFilter urls=[/*]
[] 2023-10-26 20:48:51.981 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping servlets: dispatcherServlet urls=[/], statViewServlet urls=[/druid/*]
[] 2023-10-26 20:48:52.016 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter] 241 - Filter 'requestContextFilter' configured for use
[] 2023-10-26 20:48:52.017 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedHiddenHttpMethodFilter] 241 - Filter 'hiddenHttpMethodFilter' configured for use
[] 2023-10-26 20:48:52.017 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter] 241 - Filter 'characterEncodingFilter' configured for use
[] 2023-10-26 20:48:52.018 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedFormContentFilter] 241 - Filter 'formContentFilter' configured for use
[] 2023-10-26 20:48:52.032 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'myBatisApplication'
[] 2023-10-26 20:48:52.033 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai1'
[] 2023-10-26 20:48:52.034 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai2'
[] 2023-10-26 20:48:52.037 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userJDBCDao'
[] 2023-10-26 20:48:52.040 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:52.040 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:48:52.040 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:52.040 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:48:52.041 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'configurationProperties' with value of type Integer
[] 2023-10-26 20:48:52.041 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:48:52.042 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:52.042 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:48:52.063 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'helloController'
[] 2023-10-26 20:48:52.066 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userMapper'
[] 2023-10-26 20:48:52.071 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionFactory'
[] 2023-10-26 20:48:52.071 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration'
[] 2023-10-26 20:48:52.072 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$d3f4d133] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:52.074 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2023-10-26 20:48:52.165 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2023-10-26 20:48:52.166 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@394df057'
[] 2023-10-26 20:48:52.177 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSource'
[] 2023-10-26 20:48:52.178 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure'
[] 2023-10-26 20:48:52.186 [INFO] [main] [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure] 56 - Init DruidDataSource
[] 2023-10-26 20:48:52.284 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-10-26 20:48:52.303 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statFilter'
[] 2023-10-26 20:48:52.304 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration'
[] 2023-10-26 20:48:52.459 [INFO] [main] [com.alibaba.druid.pool.DruidDataSource] 928 - {dataSource-1} inited
[] 2023-10-26 20:48:52.481 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker'
[] 2023-10-26 20:48:52.483 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-10-26 20:48:52.483 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@394df057'
[] 2023-10-26 20:48:52.489 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionFactory' via factory method to bean named 'dataSource'
[] 2023-10-26 20:48:52.756 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionTemplate'
[] 2023-10-26 20:48:52.757 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionTemplate' via factory method to bean named 'sqlSessionFactory'
[] 2023-10-26 20:48:52.775 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transitGoodsInfoMapper'
[] 2023-10-26 20:48:52.780 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'pageController'
[] 2023-10-26 20:48:52.783 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'voteService'
[] 2023-10-26 20:48:52.798 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
[] 2023-10-26 20:48:52.800 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
[] 2023-10-26 20:48:52.803 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
[] 2023-10-26 20:48:52.804 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
[] 2023-10-26 20:48:52.805 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration$$EnhancerBySpringCGLIB$$2cc018d9] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:52.808 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2023-10-26 20:48:52.810 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2023-10-26 20:48:52.812 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskExecutorBuilder'
[] 2023-10-26 20:48:52.822 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration'
[] 2023-10-26 20:48:52.824 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultValidator'
[] 2023-10-26 20:48:52.846 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
[] 2023-10-26 20:48:52.847 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'error'
[] 2023-10-26 20:48:52.850 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameViewResolver'
[] 2023-10-26 20:48:52.852 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorAttributes'
[] 2023-10-26 20:48:52.853 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'basicErrorController'
[] 2023-10-26 20:48:52.854 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
[] 2023-10-26 20:48:52.856 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration'
[] 2023-10-26 20:48:52.856 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration$$EnhancerBySpringCGLIB$$6a4ea3ae] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:52.857 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:48:52.858 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconHandlerMapping'
[] 2023-10-26 20:48:52.864 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconRequestHandler'
[] 2023-10-26 20:48:52.879 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/**/favicon.ico] in 'faviconHandlerMapping'
[] 2023-10-26 20:48:52.880 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
[] 2023-10-26 20:48:52.881 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration$$EnhancerBySpringCGLIB$$c0dd083] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:52.883 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@1a75e76a'
[] 2023-10-26 20:48:52.894 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
[] 2023-10-26 20:48:52.895 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$$EnhancerBySpringCGLIB$$ae119b68] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:52.895 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:48:52.895 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:48:52.896 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@1a75e76a'
[] 2023-10-26 20:48:52.901 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
[] 2023-10-26 20:48:52.921 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcContentNegotiationManager'
[] 2023-10-26 20:48:52.924 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'messageConverters'
[] 2023-10-26 20:48:52.924 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
[] 2023-10-26 20:48:52.925 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$$EnhancerBySpringCGLIB$$bb544b97] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:52.927 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'stringHttpMessageConverter'
[] 2023-10-26 20:48:52.928 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
[] 2023-10-26 20:48:52.928 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration$$EnhancerBySpringCGLIB$$ee621237] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:52.929 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:48:52.936 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
[] 2023-10-26 20:48:52.937 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
[] 2023-10-26 20:48:52.939 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapper'
[] 2023-10-26 20:48:52.939 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
[] 2023-10-26 20:48:52.941 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapperBuilder'
[] 2023-10-26 20:48:52.942 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
[] 2023-10-26 20:48:52.942 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$af00c1ad] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:52.943 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@394df057'
[] 2023-10-26 20:48:52.944 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
[] 2023-10-26 20:48:52.945 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
[] 2023-10-26 20:48:52.947 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2023-10-26 20:48:52.949 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@394df057'
[] 2023-10-26 20:48:52.949 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2023-10-26 20:48:52.954 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'standardJacksonObjectMapperBuilderCustomizer'
[] 2023-10-26 20:48:52.959 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'parameterNamesModule'
[] 2023-10-26 20:48:52.960 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
[] 2023-10-26 20:48:52.967 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jsonComponentModule'
[] 2023-10-26 20:48:52.968 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
[] 2023-10-26 20:48:52.987 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapper' via factory method to bean named 'jacksonObjectMapperBuilder'
[] 2023-10-26 20:48:53.023 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'jacksonObjectMapper'
[] 2023-10-26 20:48:53.043 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcConversionService'
[] 2023-10-26 20:48:53.053 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcValidator'
[] 2023-10-26 20:48:53.062 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'applicationTaskExecutor'
[] 2023-10-26 20:48:53.064 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'applicationTaskExecutor' via factory method to bean named 'taskExecutorBuilder'
[] 2023-10-26 20:48:53.074 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2023-10-26 20:48:53.091 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter] 622 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[] 2023-10-26 20:48:53.168 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerMapping'
[] 2023-10-26 20:48:53.181 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcResourceUrlProvider'
[] 2023-10-26 20:48:53.259 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 350 - 13 mappings in 'requestMappingHandlerMapping'
[] 2023-10-26 20:48:53.262 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcPathMatcher'
[] 2023-10-26 20:48:53.264 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUrlPathHelper'
[] 2023-10-26 20:48:53.268 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewControllerHandlerMapping'
[] 2023-10-26 20:48:53.271 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameHandlerMapping'
[] 2023-10-26 20:48:53.278 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'resourceHandlerMapping'
[] 2023-10-26 20:48:53.291 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
[] 2023-10-26 20:48:53.293 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultServletHandlerMapping'
[] 2023-10-26 20:48:53.296 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUriComponentsContributor'
[] 2023-10-26 20:48:53.301 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
[] 2023-10-26 20:48:53.301 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
[] 2023-10-26 20:48:53.303 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'handlerExceptionResolver'
[] 2023-10-26 20:48:53.317 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver] 302 - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
[] 2023-10-26 20:48:53.322 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcViewResolver'
[] 2023-10-26 20:48:53.331 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultViewResolver'
[] 2023-10-26 20:48:53.347 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewResolver'
[] 2023-10-26 20:48:53.348 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@1a75e76a'
[] 2023-10-26 20:48:53.351 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'thymeleafViewResolver'
[] 2023-10-26 20:48:53.352 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration'
[] 2023-10-26 20:48:53.352 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration$$EnhancerBySpringCGLIB$$34a17ece] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:53.354 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:48:53.362 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'templateEngine'
[] 2023-10-26 20:48:53.363 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration'
[] 2023-10-26 20:48:53.364 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration$$EnhancerBySpringCGLIB$$5fe8c29f] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:53.366 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultTemplateResolver'
[] 2023-10-26 20:48:53.367 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration'
[] 2023-10-26 20:48:53.367 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration$$EnhancerBySpringCGLIB$$1e1947f6] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:53.368 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:48:53.368 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@394df057'
[] 2023-10-26 20:48:53.382 [WARN] [main] [org.thymeleaf.templatemode.TemplateMode] 150 - [THYMELEAF][main] Template Mode 'LEGACYHTML5' is deprecated. Using Template Mode 'HTML' instead.
[] 2023-10-26 20:48:53.385 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:48:53.386 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration' via constructor to bean named 'defaultTemplateResolver'
[] 2023-10-26 20:48:53.412 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'java8TimeDialect'
[] 2023-10-26 20:48:53.413 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafJava8TimeDialect'
[] 2023-10-26 20:48:53.422 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:48:53.422 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration' via constructor to bean named 'templateEngine'
[] 2023-10-26 20:48:53.434 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'welcomePageHandlerMapping'
[] 2023-10-26 20:48:53.435 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@394df057'
[] 2023-10-26 20:48:53.441 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:53.441 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:48:53.442 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 61 - Adding welcome page template: index
[] 2023-10-26 20:48:53.447 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
[] 2023-10-26 20:48:53.449 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
[] 2023-10-26 20:48:53.453 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
[] 2023-10-26 20:48:53.454 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializationConfiguration'
[] 2023-10-26 20:48:53.455 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
[] 2023-10-26 20:48:53.455 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisLanguageDriverAutoConfiguration'
[] 2023-10-26 20:48:53.456 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration'
[] 2023-10-26 20:48:53.456 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 261 - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
[] 2023-10-26 20:48:53.457 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration'
[] 2023-10-26 20:48:53.462 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanExporter'
[] 2023-10-26 20:48:53.464 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'objectNamingStrategy'
[] 2023-10-26 20:48:53.470 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'objectNamingStrategy'
[] 2023-10-26 20:48:53.481 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanServer'
[] 2023-10-26 20:48:53.490 [DEBUG] [main] [org.springframework.jmx.support.JmxUtils] 126 - Found MBeanServer: com.sun.jmx.mbeanserver.JmxMBeanServer@20633a3b
[] 2023-10-26 20:48:53.503 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
[] 2023-10-26 20:48:53.506 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
[] 2023-10-26 20:48:53.508 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
[] 2023-10-26 20:48:53.509 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$LoggingCodecConfiguration'
[] 2023-10-26 20:48:53.510 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'loggingCodecCustomizer'
[] 2023-10-26 20:48:53.511 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'loggingCodecCustomizer' via factory method to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:48:53.516 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$JacksonCodecConfiguration'
[] 2023-10-26 20:48:53.518 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonCodecCustomizer'
[] 2023-10-26 20:48:53.519 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonCodecCustomizer' via factory method to bean named 'jacksonObjectMapper'
[] 2023-10-26 20:48:53.525 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration'
[] 2023-10-26 20:48:53.525 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
[] 2023-10-26 20:48:53.526 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration$$EnhancerBySpringCGLIB$$dd1a8d1c] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:53.528 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2023-10-26 20:48:53.531 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2023-10-26 20:48:53.532 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration'
[] 2023-10-26 20:48:53.534 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration$$EnhancerBySpringCGLIB$$b9abae70] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:53.536 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2023-10-26 20:48:53.538 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'dataSource'
[] 2023-10-26 20:48:53.538 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2023-10-26 20:48:53.539 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jdbcTemplate'
[] 2023-10-26 20:48:53.570 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$NamedParameterJdbcTemplateConfiguration'
[] 2023-10-26 20:48:53.571 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'namedParameterJdbcTemplate'
[] 2023-10-26 20:48:53.572 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'namedParameterJdbcTemplate' via factory method to bean named 'jdbcTemplate'
[] 2023-10-26 20:48:53.578 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
[] 2023-10-26 20:48:53.579 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
[] 2023-10-26 20:48:53.580 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskSchedulerBuilder'
[] 2023-10-26 20:48:53.581 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2023-10-26 20:48:53.582 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'taskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2023-10-26 20:48:53.585 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration'
[] 2023-10-26 20:48:53.585 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration'
[] 2023-10-26 20:48:53.586 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration'
[] 2023-10-26 20:48:53.587 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration$$EnhancerBySpringCGLIB$$4e211872] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:53.587 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration' via constructor to bean named 'dataSource'
[] 2023-10-26 20:48:53.588 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
[] 2023-10-26 20:48:53.588 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
[] 2023-10-26 20:48:53.591 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
[] 2023-10-26 20:48:53.594 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionManager'
[] 2023-10-26 20:48:53.595 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'transactionManager' via factory method to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-10-26 20:48:53.604 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
[] 2023-10-26 20:48:53.605 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration'
[] 2023-10-26 20:48:53.606 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration'
[] 2023-10-26 20:48:53.607 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
[] 2023-10-26 20:48:53.607 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration$$EnhancerBySpringCGLIB$$9faadd45] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:53.608 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration' via constructor to bean named 'transactionManager'
[] 2023-10-26 20:48:53.609 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionTemplate'
[] 2023-10-26 20:48:53.614 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
[] 2023-10-26 20:48:53.614 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration$$EnhancerBySpringCGLIB$$3c367984] - unable to determine constructor/method parameter names
[] 2023-10-26 20:48:53.617 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'restTemplateBuilder'
[] 2023-10-26 20:48:53.627 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration'
[] 2023-10-26 20:48:53.628 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartResolver'
[] 2023-10-26 20:48:53.663 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 433 - Registering beans for JMX exposure on startup
[] 2023-10-26 20:48:53.664 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 540 - Autodetecting user-defined JMX MBeans
[] 2023-10-26 20:48:53.670 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'statFilter' has been autodetected for JMX exposure
[] 2023-10-26 20:48:53.670 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'dataSource' has been autodetected for JMX exposure
[] 2023-10-26 20:48:53.682 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'dataSource': registering with JMX server as MBean [com.alibaba.druid.spring.boot.autoconfigure:name=dataSource,type=DruidDataSourceWrapper]
[] 2023-10-26 20:48:53.685 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'statFilter': registering with JMX server as MBean [com.alibaba.druid.filter.stat:name=statFilter,type=StatFilter]
[] 2023-10-26 20:48:53.732 [DEBUG] [main] [org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener] 132 - 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   CodecsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.codec.CodecConfigurer' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration#jacksonCodecCustomizer matched:
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   DataSourceConfiguration.Generic matched:
      - @ConditionalOnProperty (spring.datasource.type) matched (OnPropertyCondition)

   DataSourceJmxConfiguration matched:
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   DataSourceJmxConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration#transactionManager matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet; types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   DruidDataSourceAutoConfigure matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.pool.DruidDataSource' (OnClassCondition)

   DruidDataSourceAutoConfigure#dataSource matched:
      - @ConditionalOnMissingBean (types: javax.sql.DataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidFilterConfiguration#statFilter matched:
      - @ConditionalOnProperty (spring.datasource.druid.filter.stat.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: com.alibaba.druid.filter.stat.StatFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidStatViewServletConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.stat-view-servlet.enabled=true) matched (OnPropertyCondition)

   DruidWebStatFilterConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.web-stat-filter.enabled=true) matched (OnPropertyCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.DefaultErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnProperty (server.error.whitelabel.enabled) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GenericCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration automatic cache type (CacheCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.http.encoding.enabled) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnProperty (spring.http.converters.preferred-json-mapper=jackson) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   JdbcTemplateAutoConfiguration.JdbcTemplateConfiguration#jdbcTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration.NamedParameterJdbcTemplateConfiguration#namedParameterJdbcTemplate matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a primary bean from beans 'jdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   JmxAutoConfiguration#mbeanExporter matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.MBeanExporter; SearchStrategy: current) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#mbeanServer matched:
      - @ConditionalOnMissingBean (types: javax.management.MBeanServer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#objectNamingStrategy matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.naming.ObjectNamingStrategy; SearchStrategy: current) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'javax.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.servlet.multipart.enabled) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnMissingBean (types: javax.servlet.MultipartConfigElement,org.springframework.web.multipart.commons.CommonsMultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionFactory matched:
      - @ConditionalOnMissingBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionTemplate matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisLanguageDriverAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)

   NoOpCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration automatic cache type (CacheCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnProperty (spring.dao.exceptiontranslation.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SimpleCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration automatic cache type (CacheCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutionAutoConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration#taskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.thymeleaf.templatemode.TemplateMode' (OnClassCondition)

   ThymeleafAutoConfiguration.DefaultTemplateResolverConfiguration matched:
      - @ConditionalOnMissingBean (names: defaultTemplateResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafDefaultConfiguration#templateEngine matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.spring5.SpringTemplateEngine; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect matched:
      - @ConditionalOnClass found required class 'org.thymeleaf.extras.java8time.dialect.Java8TimeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect#java8TimeDialect matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.extras.java8time.dialect.Java8TimeDialect; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.thymeleaf.enabled) matched (OnPropertyCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration.ThymeleafViewResolverConfiguration#thymeleafViewResolver matched:
      - @ConditionalOnMissingBean (names: thymeleafViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found bean 'transactionManager'; @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a primary bean from beans 'transactionManager' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.validation.executable.ExecutableValidator' (OnClassCondition)
      - @ConditionalOnResource found location classpath:META-INF/services/javax.validation.spi.ValidationProvider (OnResourceCondition)

   ValidationAutoConfiguration#defaultValidator matched:
      - @ConditionalOnMissingBean (types: javax.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration#methodValidationPostProcessor matched:
      - @ConditionalOnMissingBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnProperty (spring.mvc.formcontent.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter matched:
      - @ConditionalOnProperty (spring.mvc.hiddenmethod.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.HiddenHttpMethodFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver; types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter.FaviconConfiguration matched:
      - @ConditionalOnProperty (spring.mvc.favicon.enabled) matched (OnPropertyCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'javax.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   AopAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.aspectj.lang.annotation.Aspect' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.cache.interceptor.CacheAspectSupport; SearchStrategy: all) did not find any beans of type org.springframework.cache.interceptor.CacheAspectSupport (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerJpaDependencyConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.github.benmanes.caffeine.cache.Caffeine', 'org.springframework.cache.caffeine.CaffeineCacheManager' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Session' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CloudServiceConnectorsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.config.java.CloudScanConfiguration' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.couchbase.client.java.Bucket', 'com.couchbase.client.spring.cache.CouchbaseCacheManager' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 2 matched 0 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) matched (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Hikari:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) found different value in property 'spring.datasource.type' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration.TomcatDataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSourceProxy' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   DruidFilterConfiguration#commonsLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.commons-log.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#configFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.config.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#encodingConvertFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.encoding.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4j2Filter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j2.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4jFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#slf4jLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.slf4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallConfig:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidSpringAopConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.aop-patterns) did not find property 'spring.datasource.druid.aop-patterns' (OnPropertyCondition)

   EhCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'net.sf.ehcache.Cache', 'org.springframework.cache.ehcache.EhCacheCacheManager' (OnClassCondition)

   ElasticsearchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedMongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.WebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.hazelcast.core.HazelcastInstance', 'com.hazelcast.spring.cache.HazelcastCacheManager' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HibernateJpaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.persistence.EntityManager' (OnClassCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.Resource' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   InfluxDbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.influxdb.InfluxDB' (OnClassCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'javax.cache.Caching', 'org.springframework.cache.jcache.JCacheCacheManager' (OnClassCondition)

   JacksonAutoConfiguration.JodaDateTimeJacksonConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.joda.time.DateTime', 'com.fasterxml.jackson.datatype.joda.ser.DateTimeSerializer', 'com.fasterxml.jackson.datatype.joda.cfg.JacksonJodaDateFormat' (OnClassCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.JdbcConfiguration' (OnClassCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JestAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.searchbox.client.JestClient' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.Message' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JtaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.Transaction' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.mail.internet.MimeMessage' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate did not find required type 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnBeanCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.FreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyVelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.velocity.Driver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.ThymeleafConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.VelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.SessionFactory' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.Neo4jSession' (OnClassCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.jwt.JwtDecoder' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)

   ReactiveSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactorCoreAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)

   RedisReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   RestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClient' (OnClassCondition)

   SecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityFilterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)

   SecurityRequestMatcherProviderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.web.util.matcher.RequestMatcher' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SolrAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.impl.CloudSolrClient' (OnClassCondition)

   SolrRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.SolrClient' (OnClassCondition)

   SpringApplicationAdminJmxAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.application.admin.enabled=true) did not find property 'enabled' (OnPropertyCondition)

   SpringDataWebAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.web.PageableHandlerMethodArgumentResolver' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskScheduler:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   ThymeleafAutoConfiguration.DataAttributeDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.mxab.thymeleaf.extras.dataattribute.dialect.DataAttributeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafReactiveConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafSecurityDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.extras.springsecurity5.dialect.SpringSecurityDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebFluxConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafWebLayoutConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'nz.net.ultraq.thymeleaf.LayoutDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration#resourceUrlEncodingFilter:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)

   WebClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#localeResolver:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.locale) did not find property 'locale' (OnPropertyCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.oxm.Marshaller' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.websocket.jsr356.server.deploy.WebSocketServerContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.TransactionManager' (OnClassCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration



[] 2023-10-26 20:48:53.740 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2023-10-26 20:48:53.789 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 204 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2023-10-26 20:48:53.796 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 59 - Started MyBatisApplication in 5.368 seconds (JVM running for 6.256)
[] 2023-10-26 20:50:50.890 [DEBUG] [Thread-3] [org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext] 1002 - Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@394df057, started on Thu Oct 26 20:48:49 CST 2023
[] 2023-10-26 20:50:50.901 [DEBUG] [Thread-3] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 451 - Unregistering JMX-exposed beans on shutdown
[] 2023-10-26 20:50:50.902 [DEBUG] [Thread-3] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 185 - Unregistering JMX-exposed beans
[] 2023-10-26 20:50:50.903 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2023-10-26 20:50:50.907 [INFO] [Thread-3] [com.alibaba.druid.pool.DruidDataSource] 1823 - {dataSource-1} closed
[] 2023-10-26 20:50:56.571 [DEBUG] [main] [org.springframework.boot.context.logging.ClasspathLoggingApplicationListener] 53 - Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.1.3.RELEASE/spring-boot-starter-web-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.1.3.RELEASE/spring-boot-starter-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.1.3.RELEASE/spring-boot-starter-logging-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.11.2/log4j-to-slf4j-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.11.2/log4j-api-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar, file:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.23/snakeyaml-1.23.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.1.3.RELEASE/spring-boot-starter-json-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.8/jackson-databind-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.8/jackson-core-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.8/jackson-datatype-jdk8-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.8/jackson-datatype-jsr310-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.8/jackson-module-parameter-names-2.9.8.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.1.3.RELEASE/spring-boot-starter-tomcat-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.16/tomcat-embed-core-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.16/tomcat-embed-el-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.16/tomcat-embed-websocket-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.14.Final/hibernate-validator-6.0.14.Final.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.4.0/classmate-1.4.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.1.5.RELEASE/spring-web-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.1.5.RELEASE/spring-beans-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.1.5.RELEASE/spring-webmvc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.1.5.RELEASE/spring-aop-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.1.5.RELEASE/spring-context-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.1.5.RELEASE/spring-expression-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.75/fastjson-1.2.75.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.1.9/druid-spring-boot-starter-1.1.9.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.9/druid-1.1.9.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.1.3.RELEASE/spring-boot-autoconfigure-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.15/mysql-connector-java-8.0.15.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.1.3.RELEASE/spring-boot-starter-jdbc-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.2.0/HikariCP-3.2.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.1.5.RELEASE/spring-jdbc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/statemachine/spring-statemachine-core/2.0.1.RELEASE/spring-statemachine-core-2.0.1.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.1.5.RELEASE/spring-tx-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.1.5.RELEASE/spring-messaging-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.1.5.RELEASE/spring-core-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.1.5.RELEASE/spring-jcl-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.16.22/lombok-1.16.22.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.2.5/hutool-all-5.2.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.19/commons-compress-1.19.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/30.1-jre/guava-30.1-jre.jar, file:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, file:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar, file:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.3.4/error_prone_annotations-2.3.4.jar, file:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/2.1.3.RELEASE/spring-boot-starter-thymeleaf-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring5/3.0.11.RELEASE/thymeleaf-spring5-3.0.11.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.0.11.RELEASE/thymeleaf-3.0.11.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.5.RELEASE/attoparser-2.0.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/extras/thymeleaf-extras-java8time/3.0.3.RELEASE/thymeleaf-extras-java8time-3.0.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/2.2.10/easyexcel-2.2.10.jar, file:/Users/<USER>/.m2/repository/cglib/cglib/3.1/cglib-3.1.jar, file:/Users/<USER>/.m2/repository/org/ow2/asm/asm/4.2/asm-4.2.jar, file:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.6.3/ehcache-3.6.3.jar, file:/Applications/IntelliJ%20IDEA%20CE.app/Contents/lib/idea_rt.jar]
[] 2023-10-26 20:50:56.687 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 50 - Starting MyBatisApplication on tomas.local with PID 17155 (/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes started by tomas in /Users/<USER>/code/github/SpringBoot-Learning)
[] 2023-10-26 20:50:56.688 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 675 - No active profile set, falling back to default profiles: default
[] 2023-10-26 20:50:56.689 [DEBUG] [main] [org.springframework.boot.SpringApplication] 703 - Loading source class com.tomas.mybaties3.MyBatisApplication
[] 2023-10-26 20:50:56.743 [DEBUG] [main] [org.springframework.boot.context.config.ConfigFileApplicationListener] 224 - Loaded config file 'file:/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/application.yml' (classpath:/application.yml)
[] 2023-10-26 20:50:56.744 [DEBUG] [main] [org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext] 594 - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@10683d9d
[] 2023-10-26 20:50:56.824 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
[] 2023-10-26 20:50:56.864 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
[] 2023-10-26 20:50:57.015 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai1.class]
[] 2023-10-26 20:50:57.019 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai2.class]
[] 2023-10-26 20:50:57.038 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/jdbc/UserJDBCDao.class]
[] 2023-10-26 20:50:57.093 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/HelloController.class]
[] 2023-10-26 20:50:57.098 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/PageController.class]
[] 2023-10-26 20:50:57.109 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/service/impl/VoteServiceImpl.class]
[] 2023-10-26 20:50:57.395 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:50:57.396 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:50:57.466 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:50:57.471 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:50:57.472 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:50:57.479 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:50:57.557 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.condition.BeanTypeRegistry'
[] 2023-10-26 20:50:57.599 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:50:57.600 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:50:57.765 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
[] 2023-10-26 20:50:57.774 [DEBUG] [main] [org.springframework.boot.autoconfigure.AutoConfigurationPackages] 206 - @EnableAutoConfiguration was declared on a class in the package 'com.tomas.mybaties3'. Automatic @Repository and @Entity scanning is enabled.
[] 2023-10-26 20:50:57.775 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 224 - Searching for mappers annotated with @Mapper
[] 2023-10-26 20:50:57.776 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 228 - Using auto-configuration base package 'com.tomas.mybaties3'
[] 2023-10-26 20:50:57.914 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.mapper.MapperScannerConfigurer'
[] 2023-10-26 20:50:57.918 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
[] 2023-10-26 20:50:57.949 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/TransitGoodsInfoMapper.class]
[] 2023-10-26 20:50:57.950 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/UserMapper.class]
[] 2023-10-26 20:50:57.956 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'transitGoodsInfoMapper' and 'com.tomas.mybaties3.dao.TransitGoodsInfoMapper' mapperInterface
[] 2023-10-26 20:50:57.957 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'transitGoodsInfoMapper'.
[] 2023-10-26 20:50:57.957 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'userMapper' and 'com.tomas.mybaties3.dao.UserMapper' mapperInterface
[] 2023-10-26 20:50:57.957 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
[] 2023-10-26 20:50:58.260 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
[] 2023-10-26 20:50:58.266 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationBeanFactoryMetadata'
[] 2023-10-26 20:50:58.267 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
[] 2023-10-26 20:50:58.269 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
[] 2023-10-26 20:50:58.270 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
[] 2023-10-26 20:50:58.284 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
[] 2023-10-26 20:50:58.286 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
[] 2023-10-26 20:50:58.291 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
[] 2023-10-26 20:50:58.298 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'methodValidationPostProcessor'
[] 2023-10-26 20:50:58.327 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'methodValidationPostProcessor' via factory method to bean named 'environment'
[] 2023-10-26 20:50:58.344 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSourceInitializerPostProcessor'
[] 2023-10-26 20:50:58.349 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
[] 2023-10-26 20:50:58.351 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
[] 2023-10-26 20:50:58.355 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
[] 2023-10-26 20:50:58.386 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
[] 2023-10-26 20:50:58.388 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
[] 2023-10-26 20:50:58.397 [DEBUG] [main] [org.springframework.ui.context.support.UiApplicationContextUtils] 85 - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@ecf9049]
[] 2023-10-26 20:50:58.401 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
[] 2023-10-26 20:50:58.402 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
[] 2023-10-26 20:50:58.407 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
[] 2023-10-26 20:50:58.408 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
[] 2023-10-26 20:50:58.465 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionAttributeSource'
[] 2023-10-26 20:50:58.476 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionInterceptor'
[] 2023-10-26 20:50:58.538 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
[] 2023-10-26 20:50:58.539 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
[] 2023-10-26 20:50:58.583 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
[] 2023-10-26 20:50:58.584 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
[] 2023-10-26 20:50:58.592 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:50:58.638 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:50:58.642 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
[] 2023-10-26 20:50:58.644 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:50:58.647 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatWebServerFactoryCustomizer'
[] 2023-10-26 20:50:58.648 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration'
[] 2023-10-26 20:50:58.651 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'environment'
[] 2023-10-26 20:50:58.652 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:50:58.657 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
[] 2023-10-26 20:50:58.658 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
[] 2023-10-26 20:50:58.659 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration$$EnhancerBySpringCGLIB$$7e62f957] - unable to determine constructor/method parameter names
[] 2023-10-26 20:50:58.661 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:50:58.666 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:50:58.696 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageCustomizer'
[] 2023-10-26 20:50:58.696 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
[] 2023-10-26 20:50:58.696 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$$EnhancerBySpringCGLIB$$d6ba483a] - unable to determine constructor/method parameter names
[] 2023-10-26 20:50:58.698 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServletRegistration'
[] 2023-10-26 20:50:58.698 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
[] 2023-10-26 20:50:58.698 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration$$EnhancerBySpringCGLIB$$5d156725] - unable to determine constructor/method parameter names
[] 2023-10-26 20:50:58.699 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:50:58.702 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:50:58.703 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartConfigElement'
[] 2023-10-26 20:50:58.703 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
[] 2023-10-26 20:50:58.704 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration$$EnhancerBySpringCGLIB$$ed538164] - unable to determine constructor/method parameter names
[] 2023-10-26 20:50:58.705 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2023-10-26 20:50:58.708 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2023-10-26 20:50:58.721 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServlet'
[] 2023-10-26 20:50:58.722 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
[] 2023-10-26 20:50:58.722 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration$$EnhancerBySpringCGLIB$$fb0dd27e] - unable to determine constructor/method parameter names
[] 2023-10-26 20:50:58.724 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:50:58.724 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:50:58.750 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
[] 2023-10-26 20:50:58.765 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:50:58.765 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'dispatcherServletRegistration'
[] 2023-10-26 20:50:58.766 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'conventionErrorViewResolver'
[] 2023-10-26 20:50:58.767 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
[] 2023-10-26 20:50:58.768 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration$$EnhancerBySpringCGLIB$$eb4f1cda] - unable to determine constructor/method parameter names
[] 2023-10-26 20:50:58.770 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:50:58.775 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@10683d9d'
[] 2023-10-26 20:50:58.776 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:50:58.985 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 82 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2023-10-26 20:50:58.986 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 126 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2023-10-26 20:50:58.986 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 151 - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
[] 2023-10-26 20:50:59.034 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2023-10-26 20:50:59.065 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2023-10-26 20:50:59.087 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2023-10-26 20:50:59.088 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.16]
[] 2023-10-26 20:50:59.100 [INFO] [main] [org.apache.catalina.core.AprLifecycleListener] 173 - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
[] 2023-10-26 20:50:59.379 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2023-10-26 20:50:59.380 [DEBUG] [main] [org.springframework.web.context.ContextLoader] 288 - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
[] 2023-10-26 20:50:59.381 [INFO] [main] [org.springframework.web.context.ContextLoader] 296 - Root WebApplicationContext: initialization completed in 2637 ms
[] 2023-10-26 20:50:59.388 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statViewServletRegistrationBean'
[] 2023-10-26 20:50:59.389 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidStatViewServletConfiguration'
[] 2023-10-26 20:50:59.394 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-10-26 20:50:59.399 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'statViewServletRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-10-26 20:50:59.413 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webStatFilterRegistrationBean'
[] 2023-10-26 20:50:59.414 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration'
[] 2023-10-26 20:50:59.417 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'webStatFilterRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-10-26 20:50:59.431 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestContextFilter'
[] 2023-10-26 20:50:59.437 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hiddenHttpMethodFilter'
[] 2023-10-26 20:50:59.438 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
[] 2023-10-26 20:50:59.446 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'formContentFilter'
[] 2023-10-26 20:50:59.452 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'characterEncodingFilter'
[] 2023-10-26 20:50:59.523 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping filters: filterRegistrationBean urls=[/*], characterEncodingFilter urls=[/*], hiddenHttpMethodFilter urls=[/*], formContentFilter urls=[/*], requestContextFilter urls=[/*]
[] 2023-10-26 20:50:59.523 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping servlets: dispatcherServlet urls=[/], statViewServlet urls=[/druid/*]
[] 2023-10-26 20:50:59.569 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter] 241 - Filter 'requestContextFilter' configured for use
[] 2023-10-26 20:50:59.570 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedHiddenHttpMethodFilter] 241 - Filter 'hiddenHttpMethodFilter' configured for use
[] 2023-10-26 20:50:59.570 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter] 241 - Filter 'characterEncodingFilter' configured for use
[] 2023-10-26 20:50:59.571 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedFormContentFilter] 241 - Filter 'formContentFilter' configured for use
[] 2023-10-26 20:50:59.584 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'myBatisApplication'
[] 2023-10-26 20:50:59.586 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai1'
[] 2023-10-26 20:50:59.588 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai2'
[] 2023-10-26 20:50:59.591 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userJDBCDao'
[] 2023-10-26 20:50:59.600 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:50:59.601 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:50:59.601 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:50:59.601 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:50:59.602 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'configurationProperties' with value of type Integer
[] 2023-10-26 20:50:59.602 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:50:59.603 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:50:59.603 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:50:59.635 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'helloController'
[] 2023-10-26 20:50:59.642 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userMapper'
[] 2023-10-26 20:50:59.650 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionFactory'
[] 2023-10-26 20:50:59.651 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration'
[] 2023-10-26 20:50:59.652 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$dde293e5] - unable to determine constructor/method parameter names
[] 2023-10-26 20:50:59.657 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2023-10-26 20:50:59.755 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2023-10-26 20:50:59.755 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@10683d9d'
[] 2023-10-26 20:50:59.763 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSource'
[] 2023-10-26 20:50:59.764 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure'
[] 2023-10-26 20:50:59.770 [INFO] [main] [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure] 56 - Init DruidDataSource
[] 2023-10-26 20:50:59.841 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-10-26 20:50:59.857 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statFilter'
[] 2023-10-26 20:50:59.858 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration'
[] 2023-10-26 20:51:00.014 [INFO] [main] [com.alibaba.druid.pool.DruidDataSource] 928 - {dataSource-1} inited
[] 2023-10-26 20:51:00.031 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker'
[] 2023-10-26 20:51:00.036 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-10-26 20:51:00.037 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@10683d9d'
[] 2023-10-26 20:51:00.045 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionFactory' via factory method to bean named 'dataSource'
[] 2023-10-26 20:51:00.342 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionTemplate'
[] 2023-10-26 20:51:00.343 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionTemplate' via factory method to bean named 'sqlSessionFactory'
[] 2023-10-26 20:51:00.364 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transitGoodsInfoMapper'
[] 2023-10-26 20:51:00.372 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'pageController'
[] 2023-10-26 20:51:00.374 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'voteService'
[] 2023-10-26 20:51:00.395 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
[] 2023-10-26 20:51:00.399 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
[] 2023-10-26 20:51:00.401 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
[] 2023-10-26 20:51:00.402 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
[] 2023-10-26 20:51:00.403 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration$$EnhancerBySpringCGLIB$$36addb8b] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:00.410 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2023-10-26 20:51:00.413 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2023-10-26 20:51:00.418 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskExecutorBuilder'
[] 2023-10-26 20:51:00.430 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration'
[] 2023-10-26 20:51:00.433 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultValidator'
[] 2023-10-26 20:51:00.471 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
[] 2023-10-26 20:51:00.475 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'error'
[] 2023-10-26 20:51:00.483 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameViewResolver'
[] 2023-10-26 20:51:00.490 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorAttributes'
[] 2023-10-26 20:51:00.494 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'basicErrorController'
[] 2023-10-26 20:51:00.497 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
[] 2023-10-26 20:51:00.500 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration'
[] 2023-10-26 20:51:00.505 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration$$EnhancerBySpringCGLIB$$743c6660] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:00.507 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:51:00.510 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconHandlerMapping'
[] 2023-10-26 20:51:00.523 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconRequestHandler'
[] 2023-10-26 20:51:00.556 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/**/favicon.ico] in 'faviconHandlerMapping'
[] 2023-10-26 20:51:00.559 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
[] 2023-10-26 20:51:00.563 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration$$EnhancerBySpringCGLIB$$15fb9335] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:00.567 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@368247b9'
[] 2023-10-26 20:51:00.587 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
[] 2023-10-26 20:51:00.587 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$$EnhancerBySpringCGLIB$$b7ff5e1a] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:00.588 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:51:00.588 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:51:00.589 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@368247b9'
[] 2023-10-26 20:51:00.597 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
[] 2023-10-26 20:51:00.624 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcContentNegotiationManager'
[] 2023-10-26 20:51:00.630 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'messageConverters'
[] 2023-10-26 20:51:00.632 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
[] 2023-10-26 20:51:00.633 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$$EnhancerBySpringCGLIB$$c5420e49] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:00.639 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'stringHttpMessageConverter'
[] 2023-10-26 20:51:00.639 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
[] 2023-10-26 20:51:00.640 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration$$EnhancerBySpringCGLIB$$f84fd4e9] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:00.641 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:51:00.654 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
[] 2023-10-26 20:51:00.655 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
[] 2023-10-26 20:51:00.659 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapper'
[] 2023-10-26 20:51:00.659 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
[] 2023-10-26 20:51:00.662 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapperBuilder'
[] 2023-10-26 20:51:00.663 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
[] 2023-10-26 20:51:00.664 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$b8ee845f] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:00.665 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@10683d9d'
[] 2023-10-26 20:51:00.668 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
[] 2023-10-26 20:51:00.669 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
[] 2023-10-26 20:51:00.671 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2023-10-26 20:51:00.672 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@10683d9d'
[] 2023-10-26 20:51:00.673 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2023-10-26 20:51:00.676 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'standardJacksonObjectMapperBuilderCustomizer'
[] 2023-10-26 20:51:00.682 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'parameterNamesModule'
[] 2023-10-26 20:51:00.684 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
[] 2023-10-26 20:51:00.698 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jsonComponentModule'
[] 2023-10-26 20:51:00.699 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
[] 2023-10-26 20:51:00.724 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapper' via factory method to bean named 'jacksonObjectMapperBuilder'
[] 2023-10-26 20:51:00.763 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'jacksonObjectMapper'
[] 2023-10-26 20:51:00.786 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcConversionService'
[] 2023-10-26 20:51:00.793 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcValidator'
[] 2023-10-26 20:51:00.799 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'applicationTaskExecutor'
[] 2023-10-26 20:51:00.800 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'applicationTaskExecutor' via factory method to bean named 'taskExecutorBuilder'
[] 2023-10-26 20:51:00.811 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2023-10-26 20:51:00.829 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter] 622 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[] 2023-10-26 20:51:00.883 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerMapping'
[] 2023-10-26 20:51:00.900 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcResourceUrlProvider'
[] 2023-10-26 20:51:00.971 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 350 - 13 mappings in 'requestMappingHandlerMapping'
[] 2023-10-26 20:51:00.973 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcPathMatcher'
[] 2023-10-26 20:51:00.977 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUrlPathHelper'
[] 2023-10-26 20:51:00.979 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewControllerHandlerMapping'
[] 2023-10-26 20:51:00.981 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameHandlerMapping'
[] 2023-10-26 20:51:00.989 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'resourceHandlerMapping'
[] 2023-10-26 20:51:01.004 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
[] 2023-10-26 20:51:01.005 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultServletHandlerMapping'
[] 2023-10-26 20:51:01.007 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUriComponentsContributor'
[] 2023-10-26 20:51:01.013 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
[] 2023-10-26 20:51:01.015 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
[] 2023-10-26 20:51:01.017 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'handlerExceptionResolver'
[] 2023-10-26 20:51:01.028 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver] 302 - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
[] 2023-10-26 20:51:01.034 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcViewResolver'
[] 2023-10-26 20:51:01.037 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultViewResolver'
[] 2023-10-26 20:51:01.058 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewResolver'
[] 2023-10-26 20:51:01.060 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@368247b9'
[] 2023-10-26 20:51:01.065 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'thymeleafViewResolver'
[] 2023-10-26 20:51:01.065 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration'
[] 2023-10-26 20:51:01.066 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration$$EnhancerBySpringCGLIB$$3e8f4180] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:01.067 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:51:01.073 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'templateEngine'
[] 2023-10-26 20:51:01.075 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration'
[] 2023-10-26 20:51:01.077 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration$$EnhancerBySpringCGLIB$$69d68551] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:01.082 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultTemplateResolver'
[] 2023-10-26 20:51:01.083 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration'
[] 2023-10-26 20:51:01.084 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration$$EnhancerBySpringCGLIB$$28070aa8] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:01.085 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:51:01.085 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@10683d9d'
[] 2023-10-26 20:51:01.107 [WARN] [main] [org.thymeleaf.templatemode.TemplateMode] 150 - [THYMELEAF][main] Template Mode 'LEGACYHTML5' is deprecated. Using Template Mode 'HTML' instead.
[] 2023-10-26 20:51:01.115 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:51:01.116 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration' via constructor to bean named 'defaultTemplateResolver'
[] 2023-10-26 20:51:01.157 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'java8TimeDialect'
[] 2023-10-26 20:51:01.158 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafJava8TimeDialect'
[] 2023-10-26 20:51:01.174 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:51:01.175 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration' via constructor to bean named 'templateEngine'
[] 2023-10-26 20:51:01.193 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'welcomePageHandlerMapping'
[] 2023-10-26 20:51:01.194 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@10683d9d'
[] 2023-10-26 20:51:01.202 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:51:01.204 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:51:01.204 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 61 - Adding welcome page template: index
[] 2023-10-26 20:51:01.210 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
[] 2023-10-26 20:51:01.212 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
[] 2023-10-26 20:51:01.220 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
[] 2023-10-26 20:51:01.221 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializationConfiguration'
[] 2023-10-26 20:51:01.222 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
[] 2023-10-26 20:51:01.222 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisLanguageDriverAutoConfiguration'
[] 2023-10-26 20:51:01.224 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration'
[] 2023-10-26 20:51:01.225 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 261 - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
[] 2023-10-26 20:51:01.227 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration'
[] 2023-10-26 20:51:01.236 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanExporter'
[] 2023-10-26 20:51:01.237 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'objectNamingStrategy'
[] 2023-10-26 20:51:01.243 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'objectNamingStrategy'
[] 2023-10-26 20:51:01.256 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanServer'
[] 2023-10-26 20:51:01.265 [DEBUG] [main] [org.springframework.jmx.support.JmxUtils] 126 - Found MBeanServer: com.sun.jmx.mbeanserver.JmxMBeanServer@651dd636
[] 2023-10-26 20:51:01.279 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
[] 2023-10-26 20:51:01.281 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
[] 2023-10-26 20:51:01.283 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
[] 2023-10-26 20:51:01.283 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$LoggingCodecConfiguration'
[] 2023-10-26 20:51:01.284 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'loggingCodecCustomizer'
[] 2023-10-26 20:51:01.285 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'loggingCodecCustomizer' via factory method to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:51:01.289 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$JacksonCodecConfiguration'
[] 2023-10-26 20:51:01.291 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonCodecCustomizer'
[] 2023-10-26 20:51:01.292 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonCodecCustomizer' via factory method to bean named 'jacksonObjectMapper'
[] 2023-10-26 20:51:01.297 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration'
[] 2023-10-26 20:51:01.298 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
[] 2023-10-26 20:51:01.299 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration$$EnhancerBySpringCGLIB$$e7084fce] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:01.300 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2023-10-26 20:51:01.303 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2023-10-26 20:51:01.304 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration'
[] 2023-10-26 20:51:01.305 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration$$EnhancerBySpringCGLIB$$c3997122] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:01.305 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2023-10-26 20:51:01.306 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'dataSource'
[] 2023-10-26 20:51:01.307 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2023-10-26 20:51:01.307 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jdbcTemplate'
[] 2023-10-26 20:51:01.336 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$NamedParameterJdbcTemplateConfiguration'
[] 2023-10-26 20:51:01.337 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'namedParameterJdbcTemplate'
[] 2023-10-26 20:51:01.338 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'namedParameterJdbcTemplate' via factory method to bean named 'jdbcTemplate'
[] 2023-10-26 20:51:01.344 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
[] 2023-10-26 20:51:01.345 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
[] 2023-10-26 20:51:01.348 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskSchedulerBuilder'
[] 2023-10-26 20:51:01.349 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2023-10-26 20:51:01.352 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'taskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2023-10-26 20:51:01.360 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration'
[] 2023-10-26 20:51:01.362 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration'
[] 2023-10-26 20:51:01.364 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration'
[] 2023-10-26 20:51:01.365 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration$$EnhancerBySpringCGLIB$$580edb24] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:01.366 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration' via constructor to bean named 'dataSource'
[] 2023-10-26 20:51:01.368 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
[] 2023-10-26 20:51:01.369 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
[] 2023-10-26 20:51:01.377 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
[] 2023-10-26 20:51:01.388 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionManager'
[] 2023-10-26 20:51:01.389 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'transactionManager' via factory method to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-10-26 20:51:01.414 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
[] 2023-10-26 20:51:01.416 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration'
[] 2023-10-26 20:51:01.418 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration'
[] 2023-10-26 20:51:01.421 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
[] 2023-10-26 20:51:01.422 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration$$EnhancerBySpringCGLIB$$a9989ff7] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:01.424 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration' via constructor to bean named 'transactionManager'
[] 2023-10-26 20:51:01.425 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionTemplate'
[] 2023-10-26 20:51:01.435 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
[] 2023-10-26 20:51:01.435 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration$$EnhancerBySpringCGLIB$$46243c36] - unable to determine constructor/method parameter names
[] 2023-10-26 20:51:01.439 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'restTemplateBuilder'
[] 2023-10-26 20:51:01.453 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration'
[] 2023-10-26 20:51:01.453 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartResolver'
[] 2023-10-26 20:51:01.490 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 433 - Registering beans for JMX exposure on startup
[] 2023-10-26 20:51:01.491 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 540 - Autodetecting user-defined JMX MBeans
[] 2023-10-26 20:51:01.495 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'statFilter' has been autodetected for JMX exposure
[] 2023-10-26 20:51:01.495 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'dataSource' has been autodetected for JMX exposure
[] 2023-10-26 20:51:01.504 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'dataSource': registering with JMX server as MBean [com.alibaba.druid.spring.boot.autoconfigure:name=dataSource,type=DruidDataSourceWrapper]
[] 2023-10-26 20:51:01.506 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'statFilter': registering with JMX server as MBean [com.alibaba.druid.filter.stat:name=statFilter,type=StatFilter]
[] 2023-10-26 20:51:01.566 [DEBUG] [main] [org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener] 132 - 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   CodecsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.codec.CodecConfigurer' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration#jacksonCodecCustomizer matched:
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   DataSourceConfiguration.Generic matched:
      - @ConditionalOnProperty (spring.datasource.type) matched (OnPropertyCondition)

   DataSourceJmxConfiguration matched:
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   DataSourceJmxConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration#transactionManager matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet; types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   DruidDataSourceAutoConfigure matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.pool.DruidDataSource' (OnClassCondition)

   DruidDataSourceAutoConfigure#dataSource matched:
      - @ConditionalOnMissingBean (types: javax.sql.DataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidFilterConfiguration#statFilter matched:
      - @ConditionalOnProperty (spring.datasource.druid.filter.stat.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: com.alibaba.druid.filter.stat.StatFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidStatViewServletConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.stat-view-servlet.enabled=true) matched (OnPropertyCondition)

   DruidWebStatFilterConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.web-stat-filter.enabled=true) matched (OnPropertyCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.DefaultErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnProperty (server.error.whitelabel.enabled) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GenericCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration automatic cache type (CacheCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.http.encoding.enabled) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnProperty (spring.http.converters.preferred-json-mapper=jackson) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   JdbcTemplateAutoConfiguration.JdbcTemplateConfiguration#jdbcTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration.NamedParameterJdbcTemplateConfiguration#namedParameterJdbcTemplate matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a primary bean from beans 'jdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   JmxAutoConfiguration#mbeanExporter matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.MBeanExporter; SearchStrategy: current) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#mbeanServer matched:
      - @ConditionalOnMissingBean (types: javax.management.MBeanServer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#objectNamingStrategy matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.naming.ObjectNamingStrategy; SearchStrategy: current) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'javax.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.servlet.multipart.enabled) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnMissingBean (types: javax.servlet.MultipartConfigElement,org.springframework.web.multipart.commons.CommonsMultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionFactory matched:
      - @ConditionalOnMissingBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionTemplate matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisLanguageDriverAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)

   NoOpCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration automatic cache type (CacheCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnProperty (spring.dao.exceptiontranslation.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SimpleCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration automatic cache type (CacheCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutionAutoConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration#taskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.thymeleaf.templatemode.TemplateMode' (OnClassCondition)

   ThymeleafAutoConfiguration.DefaultTemplateResolverConfiguration matched:
      - @ConditionalOnMissingBean (names: defaultTemplateResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafDefaultConfiguration#templateEngine matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.spring5.SpringTemplateEngine; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect matched:
      - @ConditionalOnClass found required class 'org.thymeleaf.extras.java8time.dialect.Java8TimeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect#java8TimeDialect matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.extras.java8time.dialect.Java8TimeDialect; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.thymeleaf.enabled) matched (OnPropertyCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration.ThymeleafViewResolverConfiguration#thymeleafViewResolver matched:
      - @ConditionalOnMissingBean (names: thymeleafViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found bean 'transactionManager'; @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a primary bean from beans 'transactionManager' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.validation.executable.ExecutableValidator' (OnClassCondition)
      - @ConditionalOnResource found location classpath:META-INF/services/javax.validation.spi.ValidationProvider (OnResourceCondition)

   ValidationAutoConfiguration#defaultValidator matched:
      - @ConditionalOnMissingBean (types: javax.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration#methodValidationPostProcessor matched:
      - @ConditionalOnMissingBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnProperty (spring.mvc.formcontent.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter matched:
      - @ConditionalOnProperty (spring.mvc.hiddenmethod.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.HiddenHttpMethodFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver; types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter.FaviconConfiguration matched:
      - @ConditionalOnProperty (spring.mvc.favicon.enabled) matched (OnPropertyCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'javax.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   AopAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.aspectj.lang.annotation.Aspect' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.cache.interceptor.CacheAspectSupport; SearchStrategy: all) did not find any beans of type org.springframework.cache.interceptor.CacheAspectSupport (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerJpaDependencyConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.github.benmanes.caffeine.cache.Caffeine', 'org.springframework.cache.caffeine.CaffeineCacheManager' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Session' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CloudServiceConnectorsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.config.java.CloudScanConfiguration' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.couchbase.client.java.Bucket', 'com.couchbase.client.spring.cache.CouchbaseCacheManager' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 2 matched 0 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) matched (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Hikari:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) found different value in property 'spring.datasource.type' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration.TomcatDataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSourceProxy' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   DruidFilterConfiguration#commonsLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.commons-log.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#configFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.config.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#encodingConvertFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.encoding.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4j2Filter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j2.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4jFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#slf4jLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.slf4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallConfig:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidSpringAopConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.aop-patterns) did not find property 'spring.datasource.druid.aop-patterns' (OnPropertyCondition)

   EhCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'net.sf.ehcache.Cache', 'org.springframework.cache.ehcache.EhCacheCacheManager' (OnClassCondition)

   ElasticsearchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedMongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.WebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.hazelcast.core.HazelcastInstance', 'com.hazelcast.spring.cache.HazelcastCacheManager' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HibernateJpaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.persistence.EntityManager' (OnClassCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.Resource' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   InfluxDbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.influxdb.InfluxDB' (OnClassCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'javax.cache.Caching', 'org.springframework.cache.jcache.JCacheCacheManager' (OnClassCondition)

   JacksonAutoConfiguration.JodaDateTimeJacksonConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.joda.time.DateTime', 'com.fasterxml.jackson.datatype.joda.ser.DateTimeSerializer', 'com.fasterxml.jackson.datatype.joda.cfg.JacksonJodaDateFormat' (OnClassCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.JdbcConfiguration' (OnClassCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JestAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.searchbox.client.JestClient' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.Message' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JtaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.Transaction' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.mail.internet.MimeMessage' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate did not find required type 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnBeanCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.FreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyVelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.velocity.Driver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.ThymeleafConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.VelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.SessionFactory' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.Neo4jSession' (OnClassCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.jwt.JwtDecoder' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)

   ReactiveSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactorCoreAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)

   RedisReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   RestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClient' (OnClassCondition)

   SecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityFilterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)

   SecurityRequestMatcherProviderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.web.util.matcher.RequestMatcher' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SolrAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.impl.CloudSolrClient' (OnClassCondition)

   SolrRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.SolrClient' (OnClassCondition)

   SpringApplicationAdminJmxAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.application.admin.enabled=true) did not find property 'enabled' (OnPropertyCondition)

   SpringDataWebAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.web.PageableHandlerMethodArgumentResolver' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskScheduler:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   ThymeleafAutoConfiguration.DataAttributeDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.mxab.thymeleaf.extras.dataattribute.dialect.DataAttributeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafReactiveConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafSecurityDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.extras.springsecurity5.dialect.SpringSecurityDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebFluxConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafWebLayoutConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'nz.net.ultraq.thymeleaf.LayoutDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration#resourceUrlEncodingFilter:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)

   WebClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#localeResolver:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.locale) did not find property 'locale' (OnPropertyCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.oxm.Marshaller' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.websocket.jsr356.server.deploy.WebSocketServerContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.TransactionManager' (OnClassCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration



[] 2023-10-26 20:51:01.573 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2023-10-26 20:51:01.622 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 204 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2023-10-26 20:51:01.630 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 59 - Started MyBatisApplication in 5.613 seconds (JVM running for 6.512)
[] 2023-10-26 20:51:18.441 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2023-10-26 20:51:18.441 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2023-10-26 20:51:18.442 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 525 - Detected StandardServletMultipartResolver
[] 2023-10-26 20:51:18.452 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 541 - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
[] 2023-10-26 20:51:18.453 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 12 ms
[] 2023-10-26 20:51:18.468 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 90 - GET "/", parameters={}
[] 2023-10-26 20:51:18.477 [DEBUG] [http-nio-8081-exec-1] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 420 - Mapped to ParameterizableViewController [view="index"]
[] 2023-10-26 20:51:18.488 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.view.ContentNegotiatingViewResolver] 347 - Selected 'text/html' given [text/html, application/xhtml+xml, image/avif, image/webp, image/apng, application/xml;q=0.9, application/signed-exchange;v=b3;q=0.7, */*;q=0.8]
[] 2023-10-26 20:51:18.764 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 1130 - Completed 200 OK
[] 2023-10-26 20:51:19.276 [DEBUG] [http-nio-8081-exec-2] [org.springframework.web.servlet.DispatcherServlet] 90 - GET "/favicon.ico", parameters={}
[] 2023-10-26 20:51:19.278 [DEBUG] [http-nio-8081-exec-2] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 420 - Mapped to ResourceHttpRequestHandler [class path resource [META-INF/resources/], class path resource [resources/], class path resource [static/], class path resource [public/], ServletContext resource [/], class path resource []]
[] 2023-10-26 20:51:19.316 [DEBUG] [http-nio-8081-exec-2] [org.springframework.web.servlet.DispatcherServlet] 1130 - Completed 200 OK
[] 2023-10-26 20:51:29.661 [DEBUG] [http-nio-8081-exec-3] [org.springframework.web.servlet.DispatcherServlet] 90 - POST "/mybatis3/test/import", parameters={}
[] 2023-10-26 20:51:29.668 [DEBUG] [http-nio-8081-exec-3] [org.springframework.web.servlet.DispatcherServlet] 1100 - Failed to complete request: org.springframework.web.multipart.MaxUploadSizeExceededException: Maximum upload size exceeded; nested exception is java.lang.IllegalStateException: org.apache.tomcat.util.http.fileupload.FileUploadBase$FileSizeLimitExceededException: The field file exceeds its maximum permitted size of 1048576 bytes.
[] 2023-10-26 20:51:29.671 [ERROR] [http-nio-8081-exec-3] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed; nested exception is org.springframework.web.multipart.MaxUploadSizeExceededException: Maximum upload size exceeded; nested exception is java.lang.IllegalStateException: org.apache.tomcat.util.http.fileupload.FileUploadBase$FileSizeLimitExceededException: The field file exceeds its maximum permitted size of 1048576 bytes.] with root cause
org.apache.tomcat.util.http.fileupload.FileUploadBase$FileSizeLimitExceededException: The field file exceeds its maximum permitted size of 1048576 bytes.
	at org.apache.tomcat.util.http.fileupload.FileUploadBase$FileItemIteratorImpl$FileItemStreamImpl$1.raiseError(FileUploadBase.java:633)
	at org.apache.tomcat.util.http.fileupload.util.LimitedInputStream.checkLimit(LimitedInputStream.java:76)
	at org.apache.tomcat.util.http.fileupload.util.LimitedInputStream.read(LimitedInputStream.java:135)
	at java.io.FilterInputStream.read(FilterInputStream.java:107)
	at org.apache.tomcat.util.http.fileupload.util.Streams.copy(Streams.java:98)
	at org.apache.tomcat.util.http.fileupload.util.Streams.copy(Streams.java:68)
	at org.apache.tomcat.util.http.fileupload.FileUploadBase.parseRequest(FileUploadBase.java:293)
	at org.apache.catalina.connector.Request.parseParts(Request.java:2846)
	at org.apache.catalina.connector.Request.parseParameters(Request.java:3185)
	at org.apache.catalina.connector.Request.getParameter(Request.java:1116)
	at org.apache.catalina.connector.RequestFacade.getParameter(RequestFacade.java:381)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:84)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:107)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:200)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:834)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1415)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2023-10-26 20:51:29.674 [DEBUG] [http-nio-8081-exec-3] [org.springframework.web.servlet.DispatcherServlet] 90 - "ERROR" dispatch for POST "/error", parameters={}
[] 2023-10-26 20:51:29.675 [DEBUG] [http-nio-8081-exec-3] [org.springframework.web.servlet.DispatcherServlet] 1170 - Multipart resolution previously failed for current request - skipping re-resolution for undisturbed error rendering
[] 2023-10-26 20:51:29.682 [DEBUG] [http-nio-8081-exec-3] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 420 - Mapped to public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
[] 2023-10-26 20:51:29.728 [DEBUG] [http-nio-8081-exec-3] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:51:29.728 [DEBUG] [http-nio-8081-exec-3] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:51:29.730 [DEBUG] [http-nio-8081-exec-3] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:51:29.730 [DEBUG] [http-nio-8081-exec-3] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:51:29.734 [DEBUG] [http-nio-8081-exec-3] [org.springframework.web.servlet.view.ContentNegotiatingViewResolver] 347 - Selected 'text/html' given [text/html, text/html;q=0.8]
[] 2023-10-26 20:51:29.737 [DEBUG] [http-nio-8081-exec-3] [org.springframework.web.servlet.DispatcherServlet] 1126 - Exiting from "ERROR" dispatch, status 500
[] 2023-10-26 20:52:51.795 [DEBUG] [Thread-3] [org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext] 1002 - Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@10683d9d, started on Thu Oct 26 20:50:56 CST 2023
[] 2023-10-26 20:52:51.799 [DEBUG] [Thread-3] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 451 - Unregistering JMX-exposed beans on shutdown
[] 2023-10-26 20:52:51.799 [DEBUG] [Thread-3] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 185 - Unregistering JMX-exposed beans
[] 2023-10-26 20:52:51.800 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2023-10-26 20:52:51.803 [INFO] [Thread-3] [com.alibaba.druid.pool.DruidDataSource] 1823 - {dataSource-1} closed
[] 2023-10-26 20:52:54.586 [DEBUG] [main] [org.springframework.boot.context.logging.ClasspathLoggingApplicationListener] 53 - Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk-1.8.jdk/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.1.3.RELEASE/spring-boot-starter-web-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.1.3.RELEASE/spring-boot-starter-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.1.3.RELEASE/spring-boot-starter-logging-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.11.2/log4j-to-slf4j-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.11.2/log4j-api-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar, file:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.23/snakeyaml-1.23.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.1.3.RELEASE/spring-boot-starter-json-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.8/jackson-databind-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.8/jackson-core-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.8/jackson-datatype-jdk8-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.8/jackson-datatype-jsr310-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.8/jackson-module-parameter-names-2.9.8.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.1.3.RELEASE/spring-boot-starter-tomcat-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.16/tomcat-embed-core-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.16/tomcat-embed-el-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.16/tomcat-embed-websocket-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.14.Final/hibernate-validator-6.0.14.Final.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.4.0/classmate-1.4.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.1.5.RELEASE/spring-web-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.1.5.RELEASE/spring-beans-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.1.5.RELEASE/spring-webmvc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.1.5.RELEASE/spring-aop-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.1.5.RELEASE/spring-context-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.1.5.RELEASE/spring-expression-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.75/fastjson-1.2.75.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.1.9/druid-spring-boot-starter-1.1.9.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.9/druid-1.1.9.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.1.3.RELEASE/spring-boot-autoconfigure-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.15/mysql-connector-java-8.0.15.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.1.3.RELEASE/spring-boot-starter-jdbc-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.2.0/HikariCP-3.2.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.1.5.RELEASE/spring-jdbc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/statemachine/spring-statemachine-core/2.0.1.RELEASE/spring-statemachine-core-2.0.1.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.1.5.RELEASE/spring-tx-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.1.5.RELEASE/spring-messaging-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.1.5.RELEASE/spring-core-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.1.5.RELEASE/spring-jcl-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.16.22/lombok-1.16.22.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.2.5/hutool-all-5.2.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.19/commons-compress-1.19.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/30.1-jre/guava-30.1-jre.jar, file:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, file:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar, file:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.3.4/error_prone_annotations-2.3.4.jar, file:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/2.1.3.RELEASE/spring-boot-starter-thymeleaf-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring5/3.0.11.RELEASE/thymeleaf-spring5-3.0.11.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.0.11.RELEASE/thymeleaf-3.0.11.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.5.RELEASE/attoparser-2.0.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/extras/thymeleaf-extras-java8time/3.0.3.RELEASE/thymeleaf-extras-java8time-3.0.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/2.2.10/easyexcel-2.2.10.jar, file:/Users/<USER>/.m2/repository/cglib/cglib/3.1/cglib-3.1.jar, file:/Users/<USER>/.m2/repository/org/ow2/asm/asm/4.2/asm-4.2.jar, file:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.6.3/ehcache-3.6.3.jar, file:/Applications/IntelliJ%20IDEA%20CE.app/Contents/lib/idea_rt.jar]
[] 2023-10-26 20:52:54.778 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 50 - Starting MyBatisApplication on tomas.local with PID 17235 (/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes started by tomas in /Users/<USER>/code/github/SpringBoot-Learning)
[] 2023-10-26 20:52:54.778 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 675 - No active profile set, falling back to default profiles: default
[] 2023-10-26 20:52:54.779 [DEBUG] [main] [org.springframework.boot.SpringApplication] 703 - Loading source class com.tomas.mybaties3.MyBatisApplication
[] 2023-10-26 20:52:54.895 [DEBUG] [main] [org.springframework.boot.context.config.ConfigFileApplicationListener] 224 - Loaded config file 'file:/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/application.yml' (classpath:/application.yml)
[] 2023-10-26 20:52:54.897 [DEBUG] [main] [org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext] 594 - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3d285d7e
[] 2023-10-26 20:52:54.947 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
[] 2023-10-26 20:52:54.965 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
[] 2023-10-26 20:52:55.067 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai1.class]
[] 2023-10-26 20:52:55.068 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai2.class]
[] 2023-10-26 20:52:55.073 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/jdbc/UserJDBCDao.class]
[] 2023-10-26 20:52:55.114 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/HelloController.class]
[] 2023-10-26 20:52:55.119 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/PageController.class]
[] 2023-10-26 20:52:55.127 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/service/impl/VoteServiceImpl.class]
[] 2023-10-26 20:52:55.387 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:55.388 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:55.460 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:55.463 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:55.465 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:55.470 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:55.546 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.condition.BeanTypeRegistry'
[] 2023-10-26 20:52:55.584 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:55.584 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:55.699 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
[] 2023-10-26 20:52:55.712 [DEBUG] [main] [org.springframework.boot.autoconfigure.AutoConfigurationPackages] 206 - @EnableAutoConfiguration was declared on a class in the package 'com.tomas.mybaties3'. Automatic @Repository and @Entity scanning is enabled.
[] 2023-10-26 20:52:55.713 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 224 - Searching for mappers annotated with @Mapper
[] 2023-10-26 20:52:55.714 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 228 - Using auto-configuration base package 'com.tomas.mybaties3'
[] 2023-10-26 20:52:55.816 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.mapper.MapperScannerConfigurer'
[] 2023-10-26 20:52:55.817 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
[] 2023-10-26 20:52:55.833 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/TransitGoodsInfoMapper.class]
[] 2023-10-26 20:52:55.835 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/UserMapper.class]
[] 2023-10-26 20:52:55.839 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'transitGoodsInfoMapper' and 'com.tomas.mybaties3.dao.TransitGoodsInfoMapper' mapperInterface
[] 2023-10-26 20:52:55.840 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'transitGoodsInfoMapper'.
[] 2023-10-26 20:52:55.840 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'userMapper' and 'com.tomas.mybaties3.dao.UserMapper' mapperInterface
[] 2023-10-26 20:52:55.840 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
[] 2023-10-26 20:52:56.203 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
[] 2023-10-26 20:52:56.204 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationBeanFactoryMetadata'
[] 2023-10-26 20:52:56.205 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
[] 2023-10-26 20:52:56.206 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
[] 2023-10-26 20:52:56.216 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
[] 2023-10-26 20:52:56.222 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
[] 2023-10-26 20:52:56.222 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
[] 2023-10-26 20:52:56.225 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
[] 2023-10-26 20:52:56.227 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'methodValidationPostProcessor'
[] 2023-10-26 20:52:56.242 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'methodValidationPostProcessor' via factory method to bean named 'environment'
[] 2023-10-26 20:52:56.249 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSourceInitializerPostProcessor'
[] 2023-10-26 20:52:56.252 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
[] 2023-10-26 20:52:56.254 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
[] 2023-10-26 20:52:56.256 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
[] 2023-10-26 20:52:56.269 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
[] 2023-10-26 20:52:56.270 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
[] 2023-10-26 20:52:56.273 [DEBUG] [main] [org.springframework.ui.context.support.UiApplicationContextUtils] 85 - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@1a15b789]
[] 2023-10-26 20:52:56.274 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
[] 2023-10-26 20:52:56.274 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
[] 2023-10-26 20:52:56.276 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
[] 2023-10-26 20:52:56.276 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
[] 2023-10-26 20:52:56.293 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionAttributeSource'
[] 2023-10-26 20:52:56.297 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionInterceptor'
[] 2023-10-26 20:52:56.322 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
[] 2023-10-26 20:52:56.323 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
[] 2023-10-26 20:52:56.327 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
[] 2023-10-26 20:52:56.328 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
[] 2023-10-26 20:52:56.331 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:52:56.359 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:52:56.362 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
[] 2023-10-26 20:52:56.363 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:52:56.364 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatWebServerFactoryCustomizer'
[] 2023-10-26 20:52:56.364 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration'
[] 2023-10-26 20:52:56.366 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'environment'
[] 2023-10-26 20:52:56.366 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:52:56.370 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
[] 2023-10-26 20:52:56.370 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
[] 2023-10-26 20:52:56.370 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration$$EnhancerBySpringCGLIB$$ea480f5a] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:56.371 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:52:56.374 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:52:56.400 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageCustomizer'
[] 2023-10-26 20:52:56.401 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
[] 2023-10-26 20:52:56.401 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$$EnhancerBySpringCGLIB$$429f5e3d] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:56.402 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServletRegistration'
[] 2023-10-26 20:52:56.403 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
[] 2023-10-26 20:52:56.403 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration$$EnhancerBySpringCGLIB$$c8fa7d28] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:56.404 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:52:56.407 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:52:56.408 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartConfigElement'
[] 2023-10-26 20:52:56.408 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
[] 2023-10-26 20:52:56.409 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration$$EnhancerBySpringCGLIB$$59389767] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:56.409 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2023-10-26 20:52:56.416 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2023-10-26 20:52:56.423 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServlet'
[] 2023-10-26 20:52:56.423 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
[] 2023-10-26 20:52:56.424 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration$$EnhancerBySpringCGLIB$$66f2e881] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:56.425 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:52:56.425 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:52:56.451 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
[] 2023-10-26 20:52:56.461 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-10-26 20:52:56.462 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'dispatcherServletRegistration'
[] 2023-10-26 20:52:56.464 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'conventionErrorViewResolver'
[] 2023-10-26 20:52:56.465 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
[] 2023-10-26 20:52:56.467 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration$$EnhancerBySpringCGLIB$$573432dd] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:56.469 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:52:56.475 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3d285d7e'
[] 2023-10-26 20:52:56.476 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:52:56.694 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 82 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2023-10-26 20:52:56.694 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 126 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2023-10-26 20:52:56.695 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 151 - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
[] 2023-10-26 20:52:56.735 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2023-10-26 20:52:56.758 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2023-10-26 20:52:56.770 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2023-10-26 20:52:56.770 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.16]
[] 2023-10-26 20:52:56.784 [INFO] [main] [org.apache.catalina.core.AprLifecycleListener] 173 - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
[] 2023-10-26 20:52:57.004 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2023-10-26 20:52:57.005 [DEBUG] [main] [org.springframework.web.context.ContextLoader] 288 - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
[] 2023-10-26 20:52:57.005 [INFO] [main] [org.springframework.web.context.ContextLoader] 296 - Root WebApplicationContext: initialization completed in 2108 ms
[] 2023-10-26 20:52:57.011 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statViewServletRegistrationBean'
[] 2023-10-26 20:52:57.012 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidStatViewServletConfiguration'
[] 2023-10-26 20:52:57.015 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-10-26 20:52:57.019 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'statViewServletRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-10-26 20:52:57.030 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webStatFilterRegistrationBean'
[] 2023-10-26 20:52:57.031 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration'
[] 2023-10-26 20:52:57.033 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'webStatFilterRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-10-26 20:52:57.046 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestContextFilter'
[] 2023-10-26 20:52:57.049 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hiddenHttpMethodFilter'
[] 2023-10-26 20:52:57.050 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
[] 2023-10-26 20:52:57.062 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'formContentFilter'
[] 2023-10-26 20:52:57.071 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'characterEncodingFilter'
[] 2023-10-26 20:52:57.079 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping filters: filterRegistrationBean urls=[/*], characterEncodingFilter urls=[/*], hiddenHttpMethodFilter urls=[/*], formContentFilter urls=[/*], requestContextFilter urls=[/*]
[] 2023-10-26 20:52:57.079 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping servlets: dispatcherServlet urls=[/], statViewServlet urls=[/druid/*]
[] 2023-10-26 20:52:57.100 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter] 241 - Filter 'requestContextFilter' configured for use
[] 2023-10-26 20:52:57.101 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedHiddenHttpMethodFilter] 241 - Filter 'hiddenHttpMethodFilter' configured for use
[] 2023-10-26 20:52:57.101 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter] 241 - Filter 'characterEncodingFilter' configured for use
[] 2023-10-26 20:52:57.101 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedFormContentFilter] 241 - Filter 'formContentFilter' configured for use
[] 2023-10-26 20:52:57.118 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'myBatisApplication'
[] 2023-10-26 20:52:57.120 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai1'
[] 2023-10-26 20:52:57.122 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai2'
[] 2023-10-26 20:52:57.126 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userJDBCDao'
[] 2023-10-26 20:52:57.130 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:57.130 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:52:57.132 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:57.133 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:52:57.134 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'configurationProperties' with value of type Integer
[] 2023-10-26 20:52:57.134 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:52:57.135 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:57.135 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'environmentProperties' with value of type String
[] 2023-10-26 20:52:57.159 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'helloController'
[] 2023-10-26 20:52:57.166 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userMapper'
[] 2023-10-26 20:52:57.172 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionFactory'
[] 2023-10-26 20:52:57.173 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration'
[] 2023-10-26 20:52:57.174 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$49c7a9e8] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:57.177 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2023-10-26 20:52:57.241 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2023-10-26 20:52:57.242 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3d285d7e'
[] 2023-10-26 20:52:57.252 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSource'
[] 2023-10-26 20:52:57.252 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure'
[] 2023-10-26 20:52:57.257 [INFO] [main] [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure] 56 - Init DruidDataSource
[] 2023-10-26 20:52:57.340 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-10-26 20:52:57.354 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statFilter'
[] 2023-10-26 20:52:57.355 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration'
[] 2023-10-26 20:52:57.453 [INFO] [main] [com.alibaba.druid.pool.DruidDataSource] 928 - {dataSource-1} inited
[] 2023-10-26 20:52:57.464 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker'
[] 2023-10-26 20:52:57.466 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-10-26 20:52:57.467 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3d285d7e'
[] 2023-10-26 20:52:57.473 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionFactory' via factory method to bean named 'dataSource'
[] 2023-10-26 20:52:57.723 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionTemplate'
[] 2023-10-26 20:52:57.724 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionTemplate' via factory method to bean named 'sqlSessionFactory'
[] 2023-10-26 20:52:57.740 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transitGoodsInfoMapper'
[] 2023-10-26 20:52:57.745 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'pageController'
[] 2023-10-26 20:52:57.746 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'voteService'
[] 2023-10-26 20:52:57.757 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
[] 2023-10-26 20:52:57.757 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
[] 2023-10-26 20:52:57.758 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
[] 2023-10-26 20:52:57.759 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
[] 2023-10-26 20:52:57.760 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration$$EnhancerBySpringCGLIB$$a292f18e] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:57.763 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2023-10-26 20:52:57.765 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2023-10-26 20:52:57.767 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskExecutorBuilder'
[] 2023-10-26 20:52:57.773 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration'
[] 2023-10-26 20:52:57.774 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultValidator'
[] 2023-10-26 20:52:57.789 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
[] 2023-10-26 20:52:57.790 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'error'
[] 2023-10-26 20:52:57.793 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameViewResolver'
[] 2023-10-26 20:52:57.795 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorAttributes'
[] 2023-10-26 20:52:57.796 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'basicErrorController'
[] 2023-10-26 20:52:57.797 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
[] 2023-10-26 20:52:57.799 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration'
[] 2023-10-26 20:52:57.799 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration$$EnhancerBySpringCGLIB$$e0217c63] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:57.800 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:52:57.800 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconHandlerMapping'
[] 2023-10-26 20:52:57.805 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconRequestHandler'
[] 2023-10-26 20:52:57.817 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/**/favicon.ico] in 'faviconHandlerMapping'
[] 2023-10-26 20:52:57.818 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
[] 2023-10-26 20:52:57.819 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration$$EnhancerBySpringCGLIB$$81e0a938] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:57.821 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@7674f035'
[] 2023-10-26 20:52:57.829 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
[] 2023-10-26 20:52:57.830 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$$EnhancerBySpringCGLIB$$23e4741d] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:57.830 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-10-26 20:52:57.830 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-10-26 20:52:57.831 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@7674f035'
[] 2023-10-26 20:52:57.836 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
[] 2023-10-26 20:52:57.869 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcContentNegotiationManager'
[] 2023-10-26 20:52:57.877 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'messageConverters'
[] 2023-10-26 20:52:57.879 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
[] 2023-10-26 20:52:57.880 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$$EnhancerBySpringCGLIB$$3127244c] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:57.885 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'stringHttpMessageConverter'
[] 2023-10-26 20:52:57.885 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
[] 2023-10-26 20:52:57.886 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration$$EnhancerBySpringCGLIB$$6434eaec] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:57.888 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:52:57.904 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
[] 2023-10-26 20:52:57.905 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
[] 2023-10-26 20:52:57.909 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapper'
[] 2023-10-26 20:52:57.911 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
[] 2023-10-26 20:52:57.915 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapperBuilder'
[] 2023-10-26 20:52:57.916 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
[] 2023-10-26 20:52:57.917 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$24d39a62] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:57.918 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3d285d7e'
[] 2023-10-26 20:52:57.921 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
[] 2023-10-26 20:52:57.922 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
[] 2023-10-26 20:52:57.927 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2023-10-26 20:52:57.932 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3d285d7e'
[] 2023-10-26 20:52:57.932 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2023-10-26 20:52:57.938 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'standardJacksonObjectMapperBuilderCustomizer'
[] 2023-10-26 20:52:57.945 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'parameterNamesModule'
[] 2023-10-26 20:52:57.946 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
[] 2023-10-26 20:52:57.958 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jsonComponentModule'
[] 2023-10-26 20:52:57.958 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
[] 2023-10-26 20:52:57.975 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapper' via factory method to bean named 'jacksonObjectMapperBuilder'
[] 2023-10-26 20:52:58.023 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'jacksonObjectMapper'
[] 2023-10-26 20:52:58.043 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcConversionService'
[] 2023-10-26 20:52:58.053 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcValidator'
[] 2023-10-26 20:52:58.060 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'applicationTaskExecutor'
[] 2023-10-26 20:52:58.062 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'applicationTaskExecutor' via factory method to bean named 'taskExecutorBuilder'
[] 2023-10-26 20:52:58.072 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2023-10-26 20:52:58.092 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter] 622 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[] 2023-10-26 20:52:58.144 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerMapping'
[] 2023-10-26 20:52:58.159 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcResourceUrlProvider'
[] 2023-10-26 20:52:58.226 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 350 - 13 mappings in 'requestMappingHandlerMapping'
[] 2023-10-26 20:52:58.229 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcPathMatcher'
[] 2023-10-26 20:52:58.231 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUrlPathHelper'
[] 2023-10-26 20:52:58.232 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewControllerHandlerMapping'
[] 2023-10-26 20:52:58.234 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameHandlerMapping'
[] 2023-10-26 20:52:58.241 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'resourceHandlerMapping'
[] 2023-10-26 20:52:58.251 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
[] 2023-10-26 20:52:58.251 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultServletHandlerMapping'
[] 2023-10-26 20:52:58.252 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUriComponentsContributor'
[] 2023-10-26 20:52:58.255 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
[] 2023-10-26 20:52:58.257 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
[] 2023-10-26 20:52:58.258 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'handlerExceptionResolver'
[] 2023-10-26 20:52:58.265 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver] 302 - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
[] 2023-10-26 20:52:58.269 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcViewResolver'
[] 2023-10-26 20:52:58.274 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultViewResolver'
[] 2023-10-26 20:52:58.289 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewResolver'
[] 2023-10-26 20:52:58.291 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@7674f035'
[] 2023-10-26 20:52:58.294 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'thymeleafViewResolver'
[] 2023-10-26 20:52:58.295 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration'
[] 2023-10-26 20:52:58.295 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration$$EnhancerBySpringCGLIB$$aa745783] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:58.296 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:52:58.303 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'templateEngine'
[] 2023-10-26 20:52:58.303 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration'
[] 2023-10-26 20:52:58.303 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration$$EnhancerBySpringCGLIB$$d5bb9b54] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:58.305 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultTemplateResolver'
[] 2023-10-26 20:52:58.306 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration'
[] 2023-10-26 20:52:58.306 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration$$EnhancerBySpringCGLIB$$93ec20ab] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:58.306 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:52:58.307 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3d285d7e'
[] 2023-10-26 20:52:58.314 [WARN] [main] [org.thymeleaf.templatemode.TemplateMode] 150 - [THYMELEAF][main] Template Mode 'LEGACYHTML5' is deprecated. Using Template Mode 'HTML' instead.
[] 2023-10-26 20:52:58.317 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:52:58.317 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration' via constructor to bean named 'defaultTemplateResolver'
[] 2023-10-26 20:52:58.328 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'java8TimeDialect'
[] 2023-10-26 20:52:58.329 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafJava8TimeDialect'
[] 2023-10-26 20:52:58.333 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2023-10-26 20:52:58.334 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration' via constructor to bean named 'templateEngine'
[] 2023-10-26 20:52:58.338 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'welcomePageHandlerMapping'
[] 2023-10-26 20:52:58.339 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3d285d7e'
[] 2023-10-26 20:52:58.341 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:58.341 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2023-10-26 20:52:58.342 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 61 - Adding welcome page template: index
[] 2023-10-26 20:52:58.344 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
[] 2023-10-26 20:52:58.345 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
[] 2023-10-26 20:52:58.347 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
[] 2023-10-26 20:52:58.348 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializationConfiguration'
[] 2023-10-26 20:52:58.348 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
[] 2023-10-26 20:52:58.348 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisLanguageDriverAutoConfiguration'
[] 2023-10-26 20:52:58.349 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration'
[] 2023-10-26 20:52:58.349 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 261 - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
[] 2023-10-26 20:52:58.349 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration'
[] 2023-10-26 20:52:58.352 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanExporter'
[] 2023-10-26 20:52:58.353 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'objectNamingStrategy'
[] 2023-10-26 20:52:58.355 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'objectNamingStrategy'
[] 2023-10-26 20:52:58.361 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanServer'
[] 2023-10-26 20:52:58.364 [DEBUG] [main] [org.springframework.jmx.support.JmxUtils] 126 - Found MBeanServer: com.sun.jmx.mbeanserver.JmxMBeanServer@4e35e658
[] 2023-10-26 20:52:58.372 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
[] 2023-10-26 20:52:58.373 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
[] 2023-10-26 20:52:58.373 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
[] 2023-10-26 20:52:58.374 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$LoggingCodecConfiguration'
[] 2023-10-26 20:52:58.375 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'loggingCodecCustomizer'
[] 2023-10-26 20:52:58.376 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'loggingCodecCustomizer' via factory method to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-10-26 20:52:58.380 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$JacksonCodecConfiguration'
[] 2023-10-26 20:52:58.381 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonCodecCustomizer'
[] 2023-10-26 20:52:58.381 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonCodecCustomizer' via factory method to bean named 'jacksonObjectMapper'
[] 2023-10-26 20:52:58.386 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration'
[] 2023-10-26 20:52:58.386 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
[] 2023-10-26 20:52:58.387 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration$$EnhancerBySpringCGLIB$$52ed65d1] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:58.388 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2023-10-26 20:52:58.389 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2023-10-26 20:52:58.389 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration'
[] 2023-10-26 20:52:58.390 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration$$EnhancerBySpringCGLIB$$2f7e8725] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:58.391 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2023-10-26 20:52:58.393 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'dataSource'
[] 2023-10-26 20:52:58.394 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2023-10-26 20:52:58.394 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jdbcTemplate'
[] 2023-10-26 20:52:58.409 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$NamedParameterJdbcTemplateConfiguration'
[] 2023-10-26 20:52:58.411 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'namedParameterJdbcTemplate'
[] 2023-10-26 20:52:58.413 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'namedParameterJdbcTemplate' via factory method to bean named 'jdbcTemplate'
[] 2023-10-26 20:52:58.420 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
[] 2023-10-26 20:52:58.422 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
[] 2023-10-26 20:52:58.423 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskSchedulerBuilder'
[] 2023-10-26 20:52:58.424 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2023-10-26 20:52:58.426 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'taskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2023-10-26 20:52:58.430 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration'
[] 2023-10-26 20:52:58.431 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration'
[] 2023-10-26 20:52:58.432 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration'
[] 2023-10-26 20:52:58.433 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration$$EnhancerBySpringCGLIB$$c3f3f127] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:58.433 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration' via constructor to bean named 'dataSource'
[] 2023-10-26 20:52:58.435 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
[] 2023-10-26 20:52:58.435 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
[] 2023-10-26 20:52:58.441 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
[] 2023-10-26 20:52:58.445 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionManager'
[] 2023-10-26 20:52:58.445 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'transactionManager' via factory method to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-10-26 20:52:58.454 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
[] 2023-10-26 20:52:58.455 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration'
[] 2023-10-26 20:52:58.456 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration'
[] 2023-10-26 20:52:58.457 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
[] 2023-10-26 20:52:58.458 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration$$EnhancerBySpringCGLIB$$157db5fa] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:58.459 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration' via constructor to bean named 'transactionManager'
[] 2023-10-26 20:52:58.459 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionTemplate'
[] 2023-10-26 20:52:58.463 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
[] 2023-10-26 20:52:58.463 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration$$EnhancerBySpringCGLIB$$b2095239] - unable to determine constructor/method parameter names
[] 2023-10-26 20:52:58.465 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'restTemplateBuilder'
[] 2023-10-26 20:52:58.473 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration'
[] 2023-10-26 20:52:58.474 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartResolver'
[] 2023-10-26 20:52:58.502 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 433 - Registering beans for JMX exposure on startup
[] 2023-10-26 20:52:58.502 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 540 - Autodetecting user-defined JMX MBeans
[] 2023-10-26 20:52:58.504 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'statFilter' has been autodetected for JMX exposure
[] 2023-10-26 20:52:58.505 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'dataSource' has been autodetected for JMX exposure
[] 2023-10-26 20:52:58.513 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'dataSource': registering with JMX server as MBean [com.alibaba.druid.spring.boot.autoconfigure:name=dataSource,type=DruidDataSourceWrapper]
[] 2023-10-26 20:52:58.514 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'statFilter': registering with JMX server as MBean [com.alibaba.druid.filter.stat:name=statFilter,type=StatFilter]
[] 2023-10-26 20:52:58.546 [DEBUG] [main] [org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener] 132 - 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   CodecsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.codec.CodecConfigurer' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration#jacksonCodecCustomizer matched:
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   DataSourceConfiguration.Generic matched:
      - @ConditionalOnProperty (spring.datasource.type) matched (OnPropertyCondition)

   DataSourceJmxConfiguration matched:
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   DataSourceJmxConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration#transactionManager matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet; types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   DruidDataSourceAutoConfigure matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.pool.DruidDataSource' (OnClassCondition)

   DruidDataSourceAutoConfigure#dataSource matched:
      - @ConditionalOnMissingBean (types: javax.sql.DataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidFilterConfiguration#statFilter matched:
      - @ConditionalOnProperty (spring.datasource.druid.filter.stat.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: com.alibaba.druid.filter.stat.StatFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidStatViewServletConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.stat-view-servlet.enabled=true) matched (OnPropertyCondition)

   DruidWebStatFilterConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.web-stat-filter.enabled=true) matched (OnPropertyCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.DefaultErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnProperty (server.error.whitelabel.enabled) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GenericCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration automatic cache type (CacheCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.http.encoding.enabled) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnProperty (spring.http.converters.preferred-json-mapper=jackson) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   JdbcTemplateAutoConfiguration.JdbcTemplateConfiguration#jdbcTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration.NamedParameterJdbcTemplateConfiguration#namedParameterJdbcTemplate matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a primary bean from beans 'jdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   JmxAutoConfiguration#mbeanExporter matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.MBeanExporter; SearchStrategy: current) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#mbeanServer matched:
      - @ConditionalOnMissingBean (types: javax.management.MBeanServer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#objectNamingStrategy matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.naming.ObjectNamingStrategy; SearchStrategy: current) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'javax.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.servlet.multipart.enabled) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnMissingBean (types: javax.servlet.MultipartConfigElement,org.springframework.web.multipart.commons.CommonsMultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionFactory matched:
      - @ConditionalOnMissingBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionTemplate matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisLanguageDriverAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)

   NoOpCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration automatic cache type (CacheCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnProperty (spring.dao.exceptiontranslation.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SimpleCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration automatic cache type (CacheCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutionAutoConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration#taskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.thymeleaf.templatemode.TemplateMode' (OnClassCondition)

   ThymeleafAutoConfiguration.DefaultTemplateResolverConfiguration matched:
      - @ConditionalOnMissingBean (names: defaultTemplateResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafDefaultConfiguration#templateEngine matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.spring5.SpringTemplateEngine; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect matched:
      - @ConditionalOnClass found required class 'org.thymeleaf.extras.java8time.dialect.Java8TimeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect#java8TimeDialect matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.extras.java8time.dialect.Java8TimeDialect; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.thymeleaf.enabled) matched (OnPropertyCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration.ThymeleafViewResolverConfiguration#thymeleafViewResolver matched:
      - @ConditionalOnMissingBean (names: thymeleafViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found bean 'transactionManager'; @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a primary bean from beans 'transactionManager' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.validation.executable.ExecutableValidator' (OnClassCondition)
      - @ConditionalOnResource found location classpath:META-INF/services/javax.validation.spi.ValidationProvider (OnResourceCondition)

   ValidationAutoConfiguration#defaultValidator matched:
      - @ConditionalOnMissingBean (types: javax.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration#methodValidationPostProcessor matched:
      - @ConditionalOnMissingBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnProperty (spring.mvc.formcontent.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter matched:
      - @ConditionalOnProperty (spring.mvc.hiddenmethod.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.HiddenHttpMethodFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver; types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter.FaviconConfiguration matched:
      - @ConditionalOnProperty (spring.mvc.favicon.enabled) matched (OnPropertyCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'javax.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   AopAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.aspectj.lang.annotation.Aspect' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.cache.interceptor.CacheAspectSupport; SearchStrategy: all) did not find any beans of type org.springframework.cache.interceptor.CacheAspectSupport (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerJpaDependencyConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.github.benmanes.caffeine.cache.Caffeine', 'org.springframework.cache.caffeine.CaffeineCacheManager' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Session' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CloudServiceConnectorsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.config.java.CloudScanConfiguration' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.couchbase.client.java.Bucket', 'com.couchbase.client.spring.cache.CouchbaseCacheManager' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 2 matched 0 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) matched (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Hikari:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) found different value in property 'spring.datasource.type' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration.TomcatDataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSourceProxy' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   DruidFilterConfiguration#commonsLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.commons-log.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#configFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.config.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#encodingConvertFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.encoding.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4j2Filter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j2.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4jFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#slf4jLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.slf4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallConfig:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidSpringAopConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.aop-patterns) did not find property 'spring.datasource.druid.aop-patterns' (OnPropertyCondition)

   EhCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'net.sf.ehcache.Cache', 'org.springframework.cache.ehcache.EhCacheCacheManager' (OnClassCondition)

   ElasticsearchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedMongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.WebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.hazelcast.core.HazelcastInstance', 'com.hazelcast.spring.cache.HazelcastCacheManager' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HibernateJpaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.persistence.EntityManager' (OnClassCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.Resource' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   InfluxDbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.influxdb.InfluxDB' (OnClassCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'javax.cache.Caching', 'org.springframework.cache.jcache.JCacheCacheManager' (OnClassCondition)

   JacksonAutoConfiguration.JodaDateTimeJacksonConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.joda.time.DateTime', 'com.fasterxml.jackson.datatype.joda.ser.DateTimeSerializer', 'com.fasterxml.jackson.datatype.joda.cfg.JacksonJodaDateFormat' (OnClassCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.JdbcConfiguration' (OnClassCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JestAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.searchbox.client.JestClient' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.Message' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JtaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.Transaction' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.mail.internet.MimeMessage' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate did not find required type 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnBeanCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.FreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyVelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.velocity.Driver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.ThymeleafConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.VelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.SessionFactory' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.Neo4jSession' (OnClassCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.jwt.JwtDecoder' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)

   ReactiveSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactorCoreAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)

   RedisReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   RestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClient' (OnClassCondition)

   SecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityFilterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)

   SecurityRequestMatcherProviderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.web.util.matcher.RequestMatcher' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SolrAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.impl.CloudSolrClient' (OnClassCondition)

   SolrRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.SolrClient' (OnClassCondition)

   SpringApplicationAdminJmxAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.application.admin.enabled=true) did not find property 'enabled' (OnPropertyCondition)

   SpringDataWebAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.web.PageableHandlerMethodArgumentResolver' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskScheduler:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   ThymeleafAutoConfiguration.DataAttributeDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.mxab.thymeleaf.extras.dataattribute.dialect.DataAttributeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafReactiveConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafSecurityDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.extras.springsecurity5.dialect.SpringSecurityDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebFluxConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafWebLayoutConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'nz.net.ultraq.thymeleaf.LayoutDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration#resourceUrlEncodingFilter:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)

   WebClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#localeResolver:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.locale) did not find property 'locale' (OnPropertyCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.oxm.Marshaller' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.websocket.jsr356.server.deploy.WebSocketServerContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.TransactionManager' (OnClassCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration



[] 2023-10-26 20:52:58.549 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2023-10-26 20:52:58.578 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 204 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2023-10-26 20:52:58.581 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 59 - Started MyBatisApplication in 4.474 seconds (JVM running for 5.12)
[] 2023-10-26 20:53:04.553 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2023-10-26 20:53:04.553 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2023-10-26 20:53:04.554 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 525 - Detected StandardServletMultipartResolver
[] 2023-10-26 20:53:04.561 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 541 - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
[] 2023-10-26 20:53:04.561 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 7 ms
[] 2023-10-26 20:53:04.747 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 90 - POST "/mybatis3/test/import", parameters={masked}
[] 2023-10-26 20:53:04.755 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 420 - Mapped to public void com.tomas.mybaties3.rest.HelloController.importExcel(org.springframework.web.multipart.MultipartFile,int,int)
[] 2023-10-26 20:53:04.968 [INFO] [http-nio-8081-exec-1] [com.tomas.mybaties3.rest.HelloController] 202 - 3
[] 2023-10-26 20:53:05.066 [INFO] [http-nio-8081-exec-1] [com.tomas.mybaties3.rest.HelloController] 204 - ["77744974233046427","77748652933026877","77744909843078023"]
[] 2023-10-26 20:53:05.066 [INFO] [http-nio-8081-exec-1] [com.tomas.mybaties3.rest.HelloController] 206 - 138312
[] 2023-10-26 20:53:05.070 [INFO] [http-nio-8081-exec-1] [com.tomas.mybaties3.rest.HelloController] 215 - 时间:2023-10-26 20:53:05 单号:77744974233046427,77748652933026877,77744909843078023 行=3
[] 2023-10-26 20:53:05.697 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 268 - Using 'application/json;q=0.8', given [text/html, application/xhtml+xml, image/avif, image/webp, image/apng, application/xml;q=0.9, application/signed-exchange;v=b3;q=0.7, */*;q=0.8] and supported [application/json, application/*+json, application/json, application/*+json]
[] 2023-10-26 20:53:05.697 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 298 - Nothing to write: null body
[] 2023-10-26 20:53:05.700 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 1130 - Completed 200 OK
[] 2023-10-26 20:54:14.675 [DEBUG] [http-nio-8081-exec-4] [org.springframework.web.servlet.DispatcherServlet] 90 - POST "/mybatis3/test/import", parameters={masked}
[] 2023-10-26 20:54:14.677 [DEBUG] [http-nio-8081-exec-4] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 420 - Mapped to public void com.tomas.mybaties3.rest.HelloController.importExcel(org.springframework.web.multipart.MultipartFile,int,int)
[] 2023-10-26 20:54:14.681 [DEBUG] [http-nio-8081-exec-4] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 268 - Using 'application/json;q=0.8', given [text/html, application/xhtml+xml, image/avif, image/webp, image/apng, application/xml;q=0.9, application/signed-exchange;v=b3;q=0.7, */*;q=0.8] and supported [application/json, application/*+json, application/json, application/*+json]
[] 2023-10-26 20:54:14.681 [DEBUG] [http-nio-8081-exec-4] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 298 - Nothing to write: null body
[] 2023-10-26 20:54:14.681 [DEBUG] [http-nio-8081-exec-4] [org.springframework.web.servlet.DispatcherServlet] 1130 - Completed 200 OK
[] 2023-10-26 20:54:14.815 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 153 - 300
[] 2023-10-26 20:54:14.815 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 157 - 138312
[] 2023-10-26 20:54:14.816 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:14 单号:77744974233046427,77748652933026877,77744909843078023 行=3
[] 2023-10-26 20:54:15.421 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:15 单号:77690561213087530,77744974213094455,77744974283059013 行=6
[] 2023-10-26 20:54:16.027 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:16 单号:77744948913064394,77744974263087856,77744948733073849 行=9
[] 2023-10-26 20:54:16.630 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:16 单号:77744974293059924,77744974243001339,77744949043047275 行=12
[] 2023-10-26 20:54:17.236 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:17 单号:77744909713014074,77744909633050871,77748652753042930 行=15
[] 2023-10-26 20:54:17.839 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:17 单号:77744974823062019,77690560943035027,77744910153083618 行=18
[] 2023-10-26 20:54:18.443 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:18 单号:77744910163011782,77744975103067440,77744948723019412 行=21
[] 2023-10-26 20:54:19.046 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:19 单号:77748652923078650,77744974703009882,77744974543092758 行=24
[] 2023-10-26 20:54:19.650 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:19 单号:77690561243067440,77690561143053026,77744948863090045 行=27
[] 2023-10-26 20:54:20.255 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:20 单号:77690561323092758,77748653253012856,77744909993062124 行=30
[] 2023-10-26 20:54:20.857 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:20 单号:77744948663003346,77748653263090045,77748653273004153 行=33
[] 2023-10-26 20:54:21.461 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:21 单号:77744949053054868,77744910003024299,77690560423058254 行=36
[] 2023-10-26 20:54:22.063 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:22 单号:77748653283014748,77748652763015642,77744974563058813 行=39
[] 2023-10-26 20:54:22.667 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:22 单号:77744949063059384,77744974453058813,77690560673032229 行=42
[] 2023-10-26 20:54:23.272 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:23 单号:77744974843092281,77690561253089466,77744948973087837 行=45
[] 2023-10-26 20:54:23.876 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:23 单号:77690561053094889,77744948673011283,77744974553032473 行=48
[] 2023-10-26 20:54:24.478 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:24 单号:77690560923009882,77744974713084032,77744974333055899 行=51
[] 2023-10-26 20:54:25.079 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:25 单号:77690560903031297,77744910433069083,77744974853058845 行=54
[] 2023-10-26 20:54:25.684 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:25 单号:77744975113057835,77744948743043042,77744974833050882 行=57
[] 2023-10-26 20:54:26.289 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:26 单号:77690561063024889,77744974323051865,77744949163046449 行=60
[] 2023-10-26 20:54:26.893 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:26 单号:77744910513005878,77744974303016855,77744948763053273 行=63
[] 2023-10-26 20:54:27.496 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:27 单号:77748653073074975,77744974863068086,77744948983009882 行=66
[] 2023-10-26 20:54:28.098 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:28 单号:77748652963028437,77744974343068830,77744910013085283 行=69
[] 2023-10-26 20:54:28.699 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:28 单号:77748652643002490,77744909643002650,77748652993050090 行=72
[] 2023-10-26 20:54:29.304 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:29 单号:77744974723074612,77744910443006071,77744949093002490 行=75
[] 2023-10-26 20:54:29.907 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:29 单号:77748653293045015,77690560973071346,77690561193089417 行=78
[] 2023-10-26 20:54:30.509 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:30 单号:77744948683024615,77744949103059831,77748652983091446 行=81
[] 2023-10-26 20:54:31.113 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:31 单号:77748652773008895,77690561303062972,77748653013057883 行=84
[] 2023-10-26 20:54:31.718 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:31 单号:77744974883073849,77748652683032261,77748653003084032 行=87
[] 2023-10-26 20:54:32.324 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:32 单号:77748652663053013,77748652653071367,77744974313072035 行=90
[] 2023-10-26 20:54:32.926 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:32 单号:77744974873062972,77744910023094668,77690560743024617 行=93
[] 2023-10-26 20:54:33.529 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:33 单号:77744974733051052,77748653083018916,77744948753057611 行=96
[] 2023-10-26 20:54:34.134 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:34 单号:77744948873008775,77744910263018916,77744948993032495 行=99
[] 2023-10-26 20:54:34.739 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:34 单号:77744949173018916 行=100
[] 2023-10-26 20:54:35.344 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:35 单号:77744910453008889,77748653103086847 行=102
[] 2023-10-26 20:54:35.947 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:35 单号:77744948933053990,77744974573065047,77690561343018916 行=105
[] 2023-10-26 20:54:36.551 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:36 单号:77744948923050581,77748653303076798,77744948773050581 行=108
[] 2023-10-26 20:54:37.154 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:37 单号:77744949183078881,77744974753018916,77744974463044417 行=111
[] 2023-10-26 20:54:37.755 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:37 单号:77690560713001438,77748652673015434,77744910273086847 行=114
[] 2023-10-26 20:54:38.357 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:38 单号:77690560793052283,77744949003008994,77744974353019428 行=117
[] 2023-10-26 20:54:38.963 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:38 单号:77748652693075877,77744910283067762,77748652783008994 行=120
[] 2023-10-26 20:54:39.566 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:39 单号:77748652823054419,77690560893056887,77744974893050046 行=123
[] 2023-10-26 20:54:40.170 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:40 单号:77744948693056077,77744909863013665,77744910033039465 行=126
[] 2023-10-26 20:54:40.770 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:40 单号:77748652793040685,77748653323046904,77744974583037610 行=129
[] 2023-10-26 20:54:41.375 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:41 单号:77744974473004173,77748652803015248,77744948943004893 行=132
[] 2023-10-26 20:54:41.976 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:41 单号:77744974483021658,77748652813037610,77744910463088775 行=135
[] 2023-10-26 20:54:42.580 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:42 单号:77744974363084666,77744975003022071,77690561163021658 行=138
[] 2023-10-26 20:54:43.183 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:43 单号:77748653333037610,77690561023026415,77744910043063488 行=141
[] 2023-10-26 20:54:43.787 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:43 单号:77744948783015495,77744974593015495,77690561133048348 行=144
[] 2023-10-26 20:54:44.391 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:44 单号:77690561353079297,77744909733020239,77748653123092673 行=147
[] 2023-10-26 20:54:44.993 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:44 单号:77690561203003697,77744975013022554,77748653023041022 行=150
[] 2023-10-26 20:54:45.598 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:45 单号:77744910173036331,77690560883085165,77690561083045795 行=153
[] 2023-10-26 20:54:46.203 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:46 单号:77744948793046033,77744910303003697,77748652833092894 行=156
[] 2023-10-26 20:54:46.806 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:46 单号:77690560843085165,77744949133000240,77744949123069444 行=159
[] 2023-10-26 20:54:47.407 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:47 单号:77744948803086443,77744909943050886,77744974603032701 行=162
[] 2023-10-26 20:54:48.007 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:48 单号:77744948823077244,77744974763021095,77744948883084533 行=165
[] 2023-10-26 20:54:48.613 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:48 单号:77690560963006194,77744974613089639,77748653143047263 行=168
[] 2023-10-26 20:54:49.215 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:49 单号:77744910183099046,77690560763084320,77744974623084320 行=171
[] 2023-10-26 20:54:49.820 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:49 单号:77744910313090662,77744910293076319,77748652703043828 行=174
[] 2023-10-26 20:54:50.420 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:50 单号:77748653053006672,77744975153066570,77748653033032701 行=177
[] 2023-10-26 20:54:51.023 [INFO] [Thread-4] [com.tomas.mybaties3.rest.HelloController] 166 - 时间:2023-10-26 20:54:51 单号:77744974633081869,77744974383084497,77744975163064632 行=180
[] 2023-10-26 20:54:51.485 [DEBUG] [Thread-3] [org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext] 1002 - Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@3d285d7e, started on Thu Oct 26 20:52:54 CST 2023
[] 2023-10-26 20:54:51.487 [DEBUG] [Thread-3] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 451 - Unregistering JMX-exposed beans on shutdown
[] 2023-10-26 20:54:51.488 [DEBUG] [Thread-3] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 185 - Unregistering JMX-exposed beans
[] 2023-10-26 20:54:51.489 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2023-10-26 20:54:51.491 [INFO] [Thread-3] [com.alibaba.druid.pool.DruidDataSource] 1823 - {dataSource-1} closed
