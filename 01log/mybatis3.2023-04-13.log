[] 2023-04-13 18:58:54.515 [DEBUG] [main] [org.springframework.boot.context.logging.ClasspathLoggingApplicationListener] 53 - Application started with classpath: [file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/charsets.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/deploy.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/ext/dnsns.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/ext/jaccess.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/ext/localedata.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/ext/nashorn.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/ext/sunec.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/ext/zipfs.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/javaws.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/jce.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/jfr.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/jfxswt.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/jsse.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/management-agent.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/plugin.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/resources.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/jre/lib/rt.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/lib/ant-javafx.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/lib/dt.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/lib/javafx-mx.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/lib/jconsole.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/lib/packager.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/lib/sa-jdi.jar, file:/Library/Java/JavaVirtualMachines/jdk1.8.0_131.jdk/Contents/Home/lib/tools.jar, file:/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.1.3.RELEASE/spring-boot-starter-web-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.1.3.RELEASE/spring-boot-starter-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.1.3.RELEASE/spring-boot-starter-logging-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.11.2/log4j-to-slf4j-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.11.2/log4j-api-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar, file:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.23/snakeyaml-1.23.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.1.3.RELEASE/spring-boot-starter-json-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.8/jackson-databind-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.8/jackson-core-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.8/jackson-datatype-jdk8-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.8/jackson-datatype-jsr310-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.8/jackson-module-parameter-names-2.9.8.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.1.3.RELEASE/spring-boot-starter-tomcat-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.16/tomcat-embed-core-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.16/tomcat-embed-el-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.16/tomcat-embed-websocket-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.14.Final/hibernate-validator-6.0.14.Final.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.4.0/classmate-1.4.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.1.5.RELEASE/spring-web-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.1.5.RELEASE/spring-beans-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.1.5.RELEASE/spring-webmvc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.1.5.RELEASE/spring-aop-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.1.5.RELEASE/spring-context-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.1.5.RELEASE/spring-expression-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.75/fastjson-1.2.75.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.1.9/druid-spring-boot-starter-1.1.9.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.9/druid-1.1.9.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.1.3.RELEASE/spring-boot-autoconfigure-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.15/mysql-connector-java-8.0.15.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.1.3.RELEASE/spring-boot-starter-jdbc-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.2.0/HikariCP-3.2.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.1.5.RELEASE/spring-jdbc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.1.5.RELEASE/spring-tx-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.1.5.RELEASE/spring-core-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.1.5.RELEASE/spring-jcl-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.16.22/lombok-1.16.22.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.7/commons-lang3-3.7.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.2.5/hutool-all-5.2.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.19/commons-compress-1.19.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar, file:/Applications/IntelliJ%20IDEA.app/Contents/lib/idea_rt.jar, file:/Users/<USER>/Library/Caches/IntelliJIdea2019.3/captureAgent/debugger-agent.jar]
[] 2023-04-13 18:58:54.654 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 50 - Starting MyBatisApplication on tomas.local with PID 82941 (/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes started by tomas in /Users/<USER>/code/github/SpringBoot-Learning)
[] 2023-04-13 18:58:54.655 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 675 - No active profile set, falling back to default profiles: default
[] 2023-04-13 18:58:54.655 [DEBUG] [main] [org.springframework.boot.SpringApplication] 703 - Loading source class com.tomas.mybaties3.MyBatisApplication
[] 2023-04-13 18:58:54.850 [DEBUG] [main] [org.springframework.boot.context.config.ConfigFileApplicationListener] 224 - Loaded config file 'file:/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/application.yml' (classpath:/application.yml)
[] 2023-04-13 18:58:54.852 [DEBUG] [main] [org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext] 594 - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6d025197
[] 2023-04-13 18:58:54.911 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
[] 2023-04-13 18:58:54.944 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
[] 2023-04-13 18:58:55.066 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai1.class]
[] 2023-04-13 18:58:55.067 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai2.class]
[] 2023-04-13 18:58:55.075 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/jdbc/UserJDBCDao.class]
[] 2023-04-13 18:58:55.120 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/HelloController.class]
[] 2023-04-13 18:58:55.128 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/service/impl/VoteServiceImpl.class]
[] 2023-04-13 18:58:55.480 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:55.483 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:55.485 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:55.486 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:55.492 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:55.507 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:55.509 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:55.578 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.condition.BeanTypeRegistry'
[] 2023-04-13 18:58:55.711 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
[] 2023-04-13 18:58:55.723 [DEBUG] [main] [org.springframework.boot.autoconfigure.AutoConfigurationPackages] 206 - @EnableAutoConfiguration was declared on a class in the package 'com.tomas.mybaties3'. Automatic @Repository and @Entity scanning is enabled.
[] 2023-04-13 18:58:55.724 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 224 - Searching for mappers annotated with @Mapper
[] 2023-04-13 18:58:55.725 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 228 - Using auto-configuration base package 'com.tomas.mybaties3'
[] 2023-04-13 18:58:55.752 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.jmx.enabled' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:55.760 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.application.admin.enabled' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:55.867 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.mapper.MapperScannerConfigurer'
[] 2023-04-13 18:58:55.870 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
[] 2023-04-13 18:58:55.889 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/TransitGoodsInfoMapper.class]
[] 2023-04-13 18:58:55.890 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/UserMapper.class]
[] 2023-04-13 18:58:55.891 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'transitGoodsInfoMapper' and 'com.tomas.mybaties3.dao.TransitGoodsInfoMapper' mapperInterface
[] 2023-04-13 18:58:55.892 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'transitGoodsInfoMapper'.
[] 2023-04-13 18:58:55.892 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'userMapper' and 'com.tomas.mybaties3.dao.UserMapper' mapperInterface
[] 2023-04-13 18:58:55.892 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
[] 2023-04-13 18:58:56.086 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
[] 2023-04-13 18:58:56.087 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationBeanFactoryMetadata'
[] 2023-04-13 18:58:56.087 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
[] 2023-04-13 18:58:56.089 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
[] 2023-04-13 18:58:56.089 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
[] 2023-04-13 18:58:56.096 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
[] 2023-04-13 18:58:56.098 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
[] 2023-04-13 18:58:56.100 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
[] 2023-04-13 18:58:56.103 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'methodValidationPostProcessor'
[] 2023-04-13 18:58:56.120 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'methodValidationPostProcessor' via factory method to bean named 'environment'
[] 2023-04-13 18:58:56.129 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSourceInitializerPostProcessor'
[] 2023-04-13 18:58:56.131 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
[] 2023-04-13 18:58:56.132 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
[] 2023-04-13 18:58:56.135 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
[] 2023-04-13 18:58:56.155 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
[] 2023-04-13 18:58:56.156 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
[] 2023-04-13 18:58:56.161 [DEBUG] [main] [org.springframework.ui.context.support.UiApplicationContextUtils] 85 - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@2babf189]
[] 2023-04-13 18:58:56.162 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
[] 2023-04-13 18:58:56.163 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
[] 2023-04-13 18:58:56.165 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
[] 2023-04-13 18:58:56.165 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
[] 2023-04-13 18:58:56.189 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionAttributeSource'
[] 2023-04-13 18:58:56.195 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionInterceptor'
[] 2023-04-13 18:58:56.230 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
[] 2023-04-13 18:58:56.231 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
[] 2023-04-13 18:58:56.235 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
[] 2023-04-13 18:58:56.235 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
[] 2023-04-13 18:58:56.238 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-04-13 18:58:56.263 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-04-13 18:58:56.267 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
[] 2023-04-13 18:58:56.268 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-04-13 18:58:56.269 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatWebServerFactoryCustomizer'
[] 2023-04-13 18:58:56.269 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration'
[] 2023-04-13 18:58:56.272 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'environment'
[] 2023-04-13 18:58:56.272 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-04-13 18:58:56.276 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
[] 2023-04-13 18:58:56.276 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
[] 2023-04-13 18:58:56.277 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration$$EnhancerBySpringCGLIB$$b1b45f30] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:56.278 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-04-13 18:58:56.279 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-04-13 18:58:56.318 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageCustomizer'
[] 2023-04-13 18:58:56.319 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
[] 2023-04-13 18:58:56.320 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$$EnhancerBySpringCGLIB$$a0bae13] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:56.322 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServletRegistration'
[] 2023-04-13 18:58:56.322 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
[] 2023-04-13 18:58:56.323 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration$$EnhancerBySpringCGLIB$$9066ccfe] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:56.324 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-04-13 18:58:56.329 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-04-13 18:58:56.330 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartConfigElement'
[] 2023-04-13 18:58:56.331 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
[] 2023-04-13 18:58:56.333 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration$$EnhancerBySpringCGLIB$$20a4e73d] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:56.335 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2023-04-13 18:58:56.341 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2023-04-13 18:58:56.357 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServlet'
[] 2023-04-13 18:58:56.358 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
[] 2023-04-13 18:58:56.360 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration$$EnhancerBySpringCGLIB$$2e5f3857] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:56.363 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-04-13 18:58:56.364 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-04-13 18:58:56.401 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
[] 2023-04-13 18:58:56.413 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2023-04-13 18:58:56.414 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'dispatcherServletRegistration'
[] 2023-04-13 18:58:56.418 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'conventionErrorViewResolver'
[] 2023-04-13 18:58:56.419 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
[] 2023-04-13 18:58:56.420 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration$$EnhancerBySpringCGLIB$$1ea082b3] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:56.425 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-04-13 18:58:56.432 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6d025197'
[] 2023-04-13 18:58:56.432 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-04-13 18:58:56.659 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 82 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2023-04-13 18:58:56.660 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 126 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2023-04-13 18:58:56.660 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 151 - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
[] 2023-04-13 18:58:56.692 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2023-04-13 18:58:56.709 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2023-04-13 18:58:56.737 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2023-04-13 18:58:56.737 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.16]
[] 2023-04-13 18:58:56.745 [INFO] [main] [org.apache.catalina.core.AprLifecycleListener] 173 - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
[] 2023-04-13 18:58:56.842 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2023-04-13 18:58:56.842 [DEBUG] [main] [org.springframework.web.context.ContextLoader] 288 - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
[] 2023-04-13 18:58:56.842 [INFO] [main] [org.springframework.web.context.ContextLoader] 296 - Root WebApplicationContext: initialization completed in 1990 ms
[] 2023-04-13 18:58:56.846 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statViewServletRegistrationBean'
[] 2023-04-13 18:58:56.847 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidStatViewServletConfiguration'
[] 2023-04-13 18:58:56.849 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-04-13 18:58:56.851 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'statViewServletRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-04-13 18:58:56.859 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webStatFilterRegistrationBean'
[] 2023-04-13 18:58:56.859 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration'
[] 2023-04-13 18:58:56.860 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'webStatFilterRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2023-04-13 18:58:56.875 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestContextFilter'
[] 2023-04-13 18:58:56.879 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hiddenHttpMethodFilter'
[] 2023-04-13 18:58:56.880 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
[] 2023-04-13 18:58:56.896 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'formContentFilter'
[] 2023-04-13 18:58:56.907 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'characterEncodingFilter'
[] 2023-04-13 18:58:56.938 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping filters: filterRegistrationBean urls=[/*], characterEncodingFilter urls=[/*], hiddenHttpMethodFilter urls=[/*], formContentFilter urls=[/*], requestContextFilter urls=[/*]
[] 2023-04-13 18:58:56.939 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping servlets: dispatcherServlet urls=[/], statViewServlet urls=[/druid/*]
[] 2023-04-13 18:58:56.982 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter] 241 - Filter 'requestContextFilter' configured for use
[] 2023-04-13 18:58:56.984 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedHiddenHttpMethodFilter] 241 - Filter 'hiddenHttpMethodFilter' configured for use
[] 2023-04-13 18:58:56.984 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter] 241 - Filter 'characterEncodingFilter' configured for use
[] 2023-04-13 18:58:56.985 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedFormContentFilter] 241 - Filter 'formContentFilter' configured for use
[] 2023-04-13 18:58:57.015 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'myBatisApplication'
[] 2023-04-13 18:58:57.018 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai1'
[] 2023-04-13 18:58:57.020 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai2'
[] 2023-04-13 18:58:57.022 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userJDBCDao'
[] 2023-04-13 18:58:57.024 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:57.025 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'environmentProperties' with value of type String
[] 2023-04-13 18:58:57.025 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:57.025 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'environmentProperties' with value of type String
[] 2023-04-13 18:58:57.026 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'configurationProperties' with value of type Integer
[] 2023-04-13 18:58:57.026 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'environmentProperties' with value of type String
[] 2023-04-13 18:58:57.026 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'configurationProperties' with value of type String
[] 2023-04-13 18:58:57.026 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'environmentProperties' with value of type String
[] 2023-04-13 18:58:57.045 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'helloController'
[] 2023-04-13 18:58:57.049 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userMapper'
[] 2023-04-13 18:58:57.055 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionFactory'
[] 2023-04-13 18:58:57.055 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration'
[] 2023-04-13 18:58:57.056 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$1133f9be] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:57.058 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2023-04-13 18:58:57.208 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2023-04-13 18:58:57.209 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6d025197'
[] 2023-04-13 18:58:57.224 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSource'
[] 2023-04-13 18:58:57.225 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure'
[] 2023-04-13 18:58:57.234 [INFO] [main] [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure] 56 - Init DruidDataSource
[] 2023-04-13 18:58:57.354 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-04-13 18:58:57.373 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statFilter'
[] 2023-04-13 18:58:57.374 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration'
[] 2023-04-13 18:58:57.658 [INFO] [main] [com.alibaba.druid.pool.DruidDataSource] 928 - {dataSource-1} inited
[] 2023-04-13 18:58:57.668 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker'
[] 2023-04-13 18:58:57.670 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-04-13 18:58:57.670 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6d025197'
[] 2023-04-13 18:58:57.674 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionFactory' via factory method to bean named 'dataSource'
[] 2023-04-13 18:58:57.972 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionTemplate'
[] 2023-04-13 18:58:57.972 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionTemplate' via factory method to bean named 'sqlSessionFactory'
[] 2023-04-13 18:58:57.984 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transitGoodsInfoMapper'
[] 2023-04-13 18:58:57.987 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'voteService'
[] 2023-04-13 18:58:57.992 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
[] 2023-04-13 18:58:57.993 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
[] 2023-04-13 18:58:57.993 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
[] 2023-04-13 18:58:57.994 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
[] 2023-04-13 18:58:57.994 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration$$EnhancerBySpringCGLIB$$69ff4164] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:57.995 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2023-04-13 18:58:57.996 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2023-04-13 18:58:57.998 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskExecutorBuilder'
[] 2023-04-13 18:58:58.003 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration'
[] 2023-04-13 18:58:58.004 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultValidator'
[] 2023-04-13 18:58:58.018 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
[] 2023-04-13 18:58:58.019 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'error'
[] 2023-04-13 18:58:58.021 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameViewResolver'
[] 2023-04-13 18:58:58.022 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorAttributes'
[] 2023-04-13 18:58:58.023 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'basicErrorController'
[] 2023-04-13 18:58:58.024 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
[] 2023-04-13 18:58:58.025 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration'
[] 2023-04-13 18:58:58.026 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration$$EnhancerBySpringCGLIB$$a78dcc39] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.026 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-04-13 18:58:58.027 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconHandlerMapping'
[] 2023-04-13 18:58:58.031 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconRequestHandler'
[] 2023-04-13 18:58:58.043 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/**/favicon.ico] in 'faviconHandlerMapping'
[] 2023-04-13 18:58:58.044 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
[] 2023-04-13 18:58:58.045 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration$$EnhancerBySpringCGLIB$$494cf90e] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.047 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@24d4d7c9'
[] 2023-04-13 18:58:58.057 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
[] 2023-04-13 18:58:58.057 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$$EnhancerBySpringCGLIB$$eb50c3f3] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.058 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2023-04-13 18:58:58.058 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2023-04-13 18:58:58.058 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@24d4d7c9'
[] 2023-04-13 18:58:58.061 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
[] 2023-04-13 18:58:58.073 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcContentNegotiationManager'
[] 2023-04-13 18:58:58.076 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'messageConverters'
[] 2023-04-13 18:58:58.077 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
[] 2023-04-13 18:58:58.077 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$$EnhancerBySpringCGLIB$$f8937422] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.079 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'stringHttpMessageConverter'
[] 2023-04-13 18:58:58.080 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
[] 2023-04-13 18:58:58.080 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration$$EnhancerBySpringCGLIB$$2ba13ac2] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.081 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-04-13 18:58:58.087 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
[] 2023-04-13 18:58:58.087 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
[] 2023-04-13 18:58:58.088 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapper'
[] 2023-04-13 18:58:58.089 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
[] 2023-04-13 18:58:58.090 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapperBuilder'
[] 2023-04-13 18:58:58.090 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
[] 2023-04-13 18:58:58.090 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$ec3fea38] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.091 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6d025197'
[] 2023-04-13 18:58:58.092 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
[] 2023-04-13 18:58:58.092 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
[] 2023-04-13 18:58:58.094 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2023-04-13 18:58:58.095 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6d025197'
[] 2023-04-13 18:58:58.095 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2023-04-13 18:58:58.097 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'standardJacksonObjectMapperBuilderCustomizer'
[] 2023-04-13 18:58:58.100 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'parameterNamesModule'
[] 2023-04-13 18:58:58.101 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
[] 2023-04-13 18:58:58.108 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jsonComponentModule'
[] 2023-04-13 18:58:58.108 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
[] 2023-04-13 18:58:58.120 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapper' via factory method to bean named 'jacksonObjectMapperBuilder'
[] 2023-04-13 18:58:58.150 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'jacksonObjectMapper'
[] 2023-04-13 18:58:58.169 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcConversionService'
[] 2023-04-13 18:58:58.177 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcValidator'
[] 2023-04-13 18:58:58.186 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'applicationTaskExecutor'
[] 2023-04-13 18:58:58.187 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'applicationTaskExecutor' via factory method to bean named 'taskExecutorBuilder'
[] 2023-04-13 18:58:58.196 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2023-04-13 18:58:58.237 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter] 622 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[] 2023-04-13 18:58:58.312 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerMapping'
[] 2023-04-13 18:58:58.330 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcResourceUrlProvider'
[] 2023-04-13 18:58:58.406 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 350 - 11 mappings in 'requestMappingHandlerMapping'
[] 2023-04-13 18:58:58.409 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcPathMatcher'
[] 2023-04-13 18:58:58.411 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUrlPathHelper'
[] 2023-04-13 18:58:58.413 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewControllerHandlerMapping'
[] 2023-04-13 18:58:58.414 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameHandlerMapping'
[] 2023-04-13 18:58:58.420 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'resourceHandlerMapping'
[] 2023-04-13 18:58:58.435 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
[] 2023-04-13 18:58:58.437 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultServletHandlerMapping'
[] 2023-04-13 18:58:58.439 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUriComponentsContributor'
[] 2023-04-13 18:58:58.445 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
[] 2023-04-13 18:58:58.447 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
[] 2023-04-13 18:58:58.448 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'handlerExceptionResolver'
[] 2023-04-13 18:58:58.461 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver] 302 - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
[] 2023-04-13 18:58:58.468 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcViewResolver'
[] 2023-04-13 18:58:58.473 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultViewResolver'
[] 2023-04-13 18:58:58.486 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewResolver'
[] 2023-04-13 18:58:58.488 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@24d4d7c9'
[] 2023-04-13 18:58:58.493 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'welcomePageHandlerMapping'
[] 2023-04-13 18:58:58.494 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@6d025197'
[] 2023-04-13 18:58:58.507 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
[] 2023-04-13 18:58:58.509 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
[] 2023-04-13 18:58:58.516 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
[] 2023-04-13 18:58:58.517 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializationConfiguration'
[] 2023-04-13 18:58:58.519 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
[] 2023-04-13 18:58:58.522 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisLanguageDriverAutoConfiguration'
[] 2023-04-13 18:58:58.524 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration'
[] 2023-04-13 18:58:58.525 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 261 - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
[] 2023-04-13 18:58:58.526 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration'
[] 2023-04-13 18:58:58.532 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanExporter'
[] 2023-04-13 18:58:58.535 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'objectNamingStrategy'
[] 2023-04-13 18:58:58.542 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'objectNamingStrategy'
[] 2023-04-13 18:58:58.553 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanServer'
[] 2023-04-13 18:58:58.558 [DEBUG] [main] [org.springframework.jmx.support.JmxUtils] 126 - Found MBeanServer: com.sun.jmx.mbeanserver.JmxMBeanServer@61a485d2
[] 2023-04-13 18:58:58.572 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration'
[] 2023-04-13 18:58:58.573 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration$$EnhancerBySpringCGLIB$$710c495c] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.575 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.admin.SpringApplicationAdminJmxAutoConfiguration' via constructor to bean named 'environment'
[] 2023-04-13 18:58:58.576 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'springApplicationAdminRegistrar'
[] 2023-04-13 18:58:58.588 [DEBUG] [main] [org.springframework.boot.admin.SpringApplicationAdminMXBeanRegistrar$SpringApplicationAdmin] 133 - Application Admin MBean registered with name 'org.springframework.boot:type=Admin,name=SpringApplication'
[] 2023-04-13 18:58:58.589 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
[] 2023-04-13 18:58:58.591 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
[] 2023-04-13 18:58:58.592 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
[] 2023-04-13 18:58:58.593 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$LoggingCodecConfiguration'
[] 2023-04-13 18:58:58.594 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'loggingCodecCustomizer'
[] 2023-04-13 18:58:58.595 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'loggingCodecCustomizer' via factory method to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2023-04-13 18:58:58.601 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$JacksonCodecConfiguration'
[] 2023-04-13 18:58:58.601 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonCodecCustomizer'
[] 2023-04-13 18:58:58.602 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonCodecCustomizer' via factory method to bean named 'jacksonObjectMapper'
[] 2023-04-13 18:58:58.605 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration'
[] 2023-04-13 18:58:58.606 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
[] 2023-04-13 18:58:58.606 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration$$EnhancerBySpringCGLIB$$1a59b5a7] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.607 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2023-04-13 18:58:58.608 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2023-04-13 18:58:58.609 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration'
[] 2023-04-13 18:58:58.609 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration$$EnhancerBySpringCGLIB$$f6ead6fb] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.610 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2023-04-13 18:58:58.610 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'dataSource'
[] 2023-04-13 18:58:58.611 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2023-04-13 18:58:58.611 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jdbcTemplate'
[] 2023-04-13 18:58:58.641 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$NamedParameterJdbcTemplateConfiguration'
[] 2023-04-13 18:58:58.643 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'namedParameterJdbcTemplate'
[] 2023-04-13 18:58:58.645 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'namedParameterJdbcTemplate' via factory method to bean named 'jdbcTemplate'
[] 2023-04-13 18:58:58.658 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
[] 2023-04-13 18:58:58.659 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
[] 2023-04-13 18:58:58.661 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskSchedulerBuilder'
[] 2023-04-13 18:58:58.664 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2023-04-13 18:58:58.667 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'taskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2023-04-13 18:58:58.672 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration'
[] 2023-04-13 18:58:58.673 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration$$EnhancerBySpringCGLIB$$8b6040fd] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.675 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration' via constructor to bean named 'dataSource'
[] 2023-04-13 18:58:58.676 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
[] 2023-04-13 18:58:58.677 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
[] 2023-04-13 18:58:58.684 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
[] 2023-04-13 18:58:58.692 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionManager'
[] 2023-04-13 18:58:58.692 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'transactionManager' via factory method to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2023-04-13 18:58:58.701 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
[] 2023-04-13 18:58:58.704 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration'
[] 2023-04-13 18:58:58.705 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration'
[] 2023-04-13 18:58:58.706 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
[] 2023-04-13 18:58:58.707 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration$$EnhancerBySpringCGLIB$$dcea05d0] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.708 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration' via constructor to bean named 'transactionManager'
[] 2023-04-13 18:58:58.709 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionTemplate'
[] 2023-04-13 18:58:58.715 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
[] 2023-04-13 18:58:58.716 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration$$EnhancerBySpringCGLIB$$7975a20f] - unable to determine constructor/method parameter names
[] 2023-04-13 18:58:58.720 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'restTemplateBuilder'
[] 2023-04-13 18:58:58.731 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration'
[] 2023-04-13 18:58:58.733 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartResolver'
[] 2023-04-13 18:58:58.782 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 433 - Registering beans for JMX exposure on startup
[] 2023-04-13 18:58:58.783 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 540 - Autodetecting user-defined JMX MBeans
[] 2023-04-13 18:58:58.786 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'statFilter' has been autodetected for JMX exposure
[] 2023-04-13 18:58:58.786 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'dataSource' has been autodetected for JMX exposure
[] 2023-04-13 18:58:58.799 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'dataSource': registering with JMX server as MBean [com.alibaba.druid.spring.boot.autoconfigure:name=dataSource,type=DruidDataSourceWrapper]
[] 2023-04-13 18:58:58.801 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'statFilter': registering with JMX server as MBean [com.alibaba.druid.filter.stat:name=statFilter,type=StatFilter]
[] 2023-04-13 18:58:58.859 [DEBUG] [main] [org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener] 132 - 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   CodecsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.codec.CodecConfigurer' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration#jacksonCodecCustomizer matched:
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   DataSourceConfiguration.Generic matched:
      - @ConditionalOnProperty (spring.datasource.type) matched (OnPropertyCondition)

   DataSourceJmxConfiguration matched:
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   DataSourceJmxConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration#transactionManager matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet; types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   DruidDataSourceAutoConfigure matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.pool.DruidDataSource' (OnClassCondition)

   DruidDataSourceAutoConfigure#dataSource matched:
      - @ConditionalOnMissingBean (types: javax.sql.DataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidFilterConfiguration#statFilter matched:
      - @ConditionalOnProperty (spring.datasource.druid.filter.stat.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: com.alibaba.druid.filter.stat.StatFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidStatViewServletConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.stat-view-servlet.enabled=true) matched (OnPropertyCondition)

   DruidWebStatFilterConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.web-stat-filter.enabled=true) matched (OnPropertyCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.DefaultErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnProperty (server.error.whitelabel.enabled) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GenericCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration automatic cache type (CacheCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.http.encoding.enabled) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnProperty (spring.http.converters.preferred-json-mapper=jackson) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   JdbcTemplateAutoConfiguration.JdbcTemplateConfiguration#jdbcTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration.NamedParameterJdbcTemplateConfiguration#namedParameterJdbcTemplate matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a primary bean from beans 'jdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   JmxAutoConfiguration#mbeanExporter matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.MBeanExporter; SearchStrategy: current) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#mbeanServer matched:
      - @ConditionalOnMissingBean (types: javax.management.MBeanServer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#objectNamingStrategy matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.naming.ObjectNamingStrategy; SearchStrategy: current) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'javax.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.servlet.multipart.enabled) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnMissingBean (types: javax.servlet.MultipartConfigElement,org.springframework.web.multipart.commons.CommonsMultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionFactory matched:
      - @ConditionalOnMissingBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionTemplate matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisLanguageDriverAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)

   NoOpCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration automatic cache type (CacheCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnProperty (spring.dao.exceptiontranslation.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SimpleCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration automatic cache type (CacheCondition)

   SpringApplicationAdminJmxAutoConfiguration matched:
      - @ConditionalOnProperty (spring.application.admin.enabled=true) matched (OnPropertyCondition)

   SpringApplicationAdminJmxAutoConfiguration#springApplicationAdminRegistrar matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.admin.SpringApplicationAdminMXBeanRegistrar; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutionAutoConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration#taskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found bean 'transactionManager'; @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a primary bean from beans 'transactionManager' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.validation.executable.ExecutableValidator' (OnClassCondition)
      - @ConditionalOnResource found location classpath:META-INF/services/javax.validation.spi.ValidationProvider (OnResourceCondition)

   ValidationAutoConfiguration#defaultValidator matched:
      - @ConditionalOnMissingBean (types: javax.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration#methodValidationPostProcessor matched:
      - @ConditionalOnMissingBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnProperty (spring.mvc.formcontent.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter matched:
      - @ConditionalOnProperty (spring.mvc.hiddenmethod.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.HiddenHttpMethodFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver; types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter.FaviconConfiguration matched:
      - @ConditionalOnProperty (spring.mvc.favicon.enabled) matched (OnPropertyCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'javax.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   AopAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.aspectj.lang.annotation.Aspect' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.cache.interceptor.CacheAspectSupport; SearchStrategy: all) did not find any beans of type org.springframework.cache.interceptor.CacheAspectSupport (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerJpaDependencyConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.github.benmanes.caffeine.cache.Caffeine', 'org.springframework.cache.caffeine.CaffeineCacheManager' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Session' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CloudServiceConnectorsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.config.java.CloudScanConfiguration' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.couchbase.client.java.Bucket', 'com.couchbase.client.spring.cache.CouchbaseCacheManager' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 2 matched 0 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) matched (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Hikari:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) found different value in property 'spring.datasource.type' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration.TomcatDataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSourceProxy' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   DruidFilterConfiguration#commonsLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.commons-log.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#configFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.config.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#encodingConvertFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.encoding.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4j2Filter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j2.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4jFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#slf4jLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.slf4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallConfig:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidSpringAopConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.aop-patterns) did not find property 'spring.datasource.druid.aop-patterns' (OnPropertyCondition)

   EhCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'net.sf.ehcache.Cache', 'org.springframework.cache.ehcache.EhCacheCacheManager' (OnClassCondition)

   ElasticsearchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedMongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.WebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.hazelcast.core.HazelcastInstance', 'com.hazelcast.spring.cache.HazelcastCacheManager' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HibernateJpaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.persistence.EntityManager' (OnClassCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.Resource' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   InfluxDbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.influxdb.InfluxDB' (OnClassCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'javax.cache.Caching', 'org.springframework.cache.jcache.JCacheCacheManager' (OnClassCondition)

   JacksonAutoConfiguration.JodaDateTimeJacksonConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.joda.time.DateTime', 'com.fasterxml.jackson.datatype.joda.ser.DateTimeSerializer', 'com.fasterxml.jackson.datatype.joda.cfg.JacksonJodaDateFormat' (OnClassCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.JdbcConfiguration' (OnClassCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JestAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.searchbox.client.JestClient' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.Message' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JtaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.Transaction' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.mail.internet.MimeMessage' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate did not find required type 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnBeanCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.FreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyVelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.velocity.Driver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.ThymeleafConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.VelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.SessionFactory' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.Neo4jSession' (OnClassCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.jwt.JwtDecoder' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)

   ReactiveSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactorCoreAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)

   RedisReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   RestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClient' (OnClassCondition)

   SecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityFilterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)

   SecurityRequestMatcherProviderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.web.util.matcher.RequestMatcher' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SolrAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.impl.CloudSolrClient' (OnClassCondition)

   SolrRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.SolrClient' (OnClassCondition)

   SpringDataWebAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.web.PageableHandlerMethodArgumentResolver' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskScheduler:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   ThymeleafAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.templatemode.TemplateMode' (OnClassCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)

   WebClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#localeResolver:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.locale) did not find property 'locale' (OnPropertyCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.oxm.Marshaller' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.websocket.jsr356.server.deploy.WebSocketServerContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.TransactionManager' (OnClassCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration



[] 2023-04-13 18:58:58.868 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.liveBeansView.mbeanDomain' in PropertySource 'systemProperties' with value of type String
[] 2023-04-13 18:58:58.870 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2023-04-13 18:58:58.961 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 204 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2023-04-13 18:58:58.972 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 59 - Started MyBatisApplication in 5.137 seconds (JVM running for 7.423)
[] 2023-04-13 18:58:59.365 [DEBUG] [RMI TCP Connection(1)-127.0.0.1] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'local.server.port' in PropertySource 'server.ports' with value of type Integer
[] 2023-04-13 18:59:31.330 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2023-04-13 18:59:31.331 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2023-04-13 18:59:31.332 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 525 - Detected StandardServletMultipartResolver
[] 2023-04-13 18:59:31.356 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 541 - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
[] 2023-04-13 18:59:31.357 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 25 ms
[] 2023-04-13 18:59:31.419 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 90 - POST "/mybatis3/test/import", parameters={}
[] 2023-04-13 18:59:31.435 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 420 - Mapped to public void com.tomas.mybaties3.rest.HelloController.importExcel(org.springframework.web.multipart.MultipartFile) throws java.io.IOException
[] 2023-04-13 18:59:32.219 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 268 - Using 'application/json', given [*/*] and supported [application/json, application/*+json, application/json, application/*+json]
[] 2023-04-13 18:59:32.220 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 298 - Nothing to write: null body
[] 2023-04-13 18:59:32.222 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 1130 - Completed 200 OK
