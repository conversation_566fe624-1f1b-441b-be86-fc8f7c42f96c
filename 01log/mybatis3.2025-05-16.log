[] 2025-05-16 18:16:25.190 [DEBUG] [main] [org.springframework.boot.context.logging.ClasspathLoggingApplicationListener] 53 - Application started with classpath: [file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/charsets.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/dnsns.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/jaccess.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/localedata.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/nashorn.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/sunec.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/zipfs.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/jce.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/jfr.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/jfxswt.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/jsse.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/management-agent.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/resources.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.1.3.RELEASE/spring-boot-starter-web-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.1.3.RELEASE/spring-boot-starter-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.1.3.RELEASE/spring-boot-starter-logging-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.11.2/log4j-to-slf4j-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.11.2/log4j-api-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar, file:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.23/snakeyaml-1.23.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.1.3.RELEASE/spring-boot-starter-json-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.8/jackson-databind-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.8/jackson-core-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.8/jackson-datatype-jdk8-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.8/jackson-datatype-jsr310-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.8/jackson-module-parameter-names-2.9.8.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.1.3.RELEASE/spring-boot-starter-tomcat-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.16/tomcat-embed-core-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.16/tomcat-embed-el-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.16/tomcat-embed-websocket-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.14.Final/hibernate-validator-6.0.14.Final.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.4.0/classmate-1.4.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.1.5.RELEASE/spring-web-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.1.5.RELEASE/spring-beans-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.1.5.RELEASE/spring-webmvc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.1.5.RELEASE/spring-aop-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.1.5.RELEASE/spring-context-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.1.5.RELEASE/spring-expression-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.2/httpclient-4.5.2.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.11/httpcore-4.4.11.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.75/fastjson-1.2.75.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.1.9/druid-spring-boot-starter-1.1.9.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.9/druid-1.1.9.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.1.3.RELEASE/spring-boot-autoconfigure-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.15/mysql-connector-java-8.0.15.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.1.3.RELEASE/spring-boot-starter-jdbc-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.2.0/HikariCP-3.2.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.1.5.RELEASE/spring-jdbc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/statemachine/spring-statemachine-core/2.0.1.RELEASE/spring-statemachine-core-2.0.1.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.1.5.RELEASE/spring-tx-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.1.5.RELEASE/spring-messaging-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.1.5.RELEASE/spring-core-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.1.5.RELEASE/spring-jcl-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.16.22/lombok-1.16.22.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.2.5/hutool-all-5.2.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.19/commons-compress-1.19.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/30.1-jre/guava-30.1-jre.jar, file:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, file:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar, file:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.3.4/error_prone_annotations-2.3.4.jar, file:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/2.1.3.RELEASE/spring-boot-starter-thymeleaf-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring5/3.0.11.RELEASE/thymeleaf-spring5-3.0.11.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.0.11.RELEASE/thymeleaf-3.0.11.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.5.RELEASE/attoparser-2.0.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/extras/thymeleaf-extras-java8time/3.0.3.RELEASE/thymeleaf-extras-java8time-3.0.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/2.2.10/easyexcel-2.2.10.jar, file:/Users/<USER>/.m2/repository/cglib/cglib/3.1/cglib-3.1.jar, file:/Users/<USER>/.m2/repository/org/ow2/asm/asm/4.2/asm-4.2.jar, file:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.6.3/ehcache-3.6.3.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.4/swagger-annotations-1.6.4.jar, file:/Applications/IntelliJ%20IDEA%20CE.app/Contents/lib/idea_rt.jar]
[] 2025-05-16 18:16:25.337 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 50 - Starting MyBatisApplication on MacBook-Pro-3.local with PID 26052 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2025-05-16 18:16:25.389 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 675 - No active profile set, falling back to default profiles: default
[] 2025-05-16 18:16:25.391 [DEBUG] [main] [org.springframework.boot.SpringApplication] 703 - Loading source class com.tomas.mybaties3.MyBatisApplication
[] 2025-05-16 18:16:25.500 [DEBUG] [main] [org.springframework.boot.context.config.ConfigFileApplicationListener] 224 - Loaded config file 'file:/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/application.yml' (classpath:/application.yml)
[] 2025-05-16 18:16:25.501 [DEBUG] [main] [org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext] 594 - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24313fcc
[] 2025-05-16 18:16:25.518 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
[] 2025-05-16 18:16:25.527 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
[] 2025-05-16 18:16:25.651 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/AppConfig.class]
[] 2025-05-16 18:16:25.654 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai1.class]
[] 2025-05-16 18:16:25.655 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai2.class]
[] 2025-05-16 18:16:25.664 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/jdbc/UserJDBCDao.class]
[] 2025-05-16 18:16:25.715 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/HanaController.class]
[] 2025-05-16 18:16:25.731 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/HelloController.class]
[] 2025-05-16 18:16:25.735 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/PageController.class]
[] 2025-05-16 18:16:25.743 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/service/impl/VoteServiceImpl.class]
[] 2025-05-16 18:16:25.986 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:25.987 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:26.055 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:26.062 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:26.065 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:26.078 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:26.208 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.condition.BeanTypeRegistry'
[] 2025-05-16 18:16:26.273 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:26.274 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:26.359 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
[] 2025-05-16 18:16:26.365 [DEBUG] [main] [org.springframework.boot.autoconfigure.AutoConfigurationPackages] 206 - @EnableAutoConfiguration was declared on a class in the package 'com.tomas.mybaties3'. Automatic @Repository and @Entity scanning is enabled.
[] 2025-05-16 18:16:26.367 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 224 - Searching for mappers annotated with @Mapper
[] 2025-05-16 18:16:26.367 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 228 - Using auto-configuration base package 'com.tomas.mybaties3'
[] 2025-05-16 18:16:26.496 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.mapper.MapperScannerConfigurer'
[] 2025-05-16 18:16:26.503 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
[] 2025-05-16 18:16:26.535 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/TransitGoodsInfoMapper.class]
[] 2025-05-16 18:16:26.535 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/UserMapper.class]
[] 2025-05-16 18:16:26.545 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'transitGoodsInfoMapper' and 'com.tomas.mybaties3.dao.TransitGoodsInfoMapper' mapperInterface
[] 2025-05-16 18:16:26.546 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'transitGoodsInfoMapper'.
[] 2025-05-16 18:16:26.546 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'userMapper' and 'com.tomas.mybaties3.dao.UserMapper' mapperInterface
[] 2025-05-16 18:16:26.546 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
[] 2025-05-16 18:16:26.784 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
[] 2025-05-16 18:16:26.785 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationBeanFactoryMetadata'
[] 2025-05-16 18:16:26.785 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
[] 2025-05-16 18:16:26.787 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
[] 2025-05-16 18:16:26.787 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
[] 2025-05-16 18:16:26.806 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
[] 2025-05-16 18:16:26.808 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
[] 2025-05-16 18:16:26.810 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
[] 2025-05-16 18:16:26.814 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'methodValidationPostProcessor'
[] 2025-05-16 18:16:26.840 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'methodValidationPostProcessor' via factory method to bean named 'environment'
[] 2025-05-16 18:16:26.850 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSourceInitializerPostProcessor'
[] 2025-05-16 18:16:26.858 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
[] 2025-05-16 18:16:26.860 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
[] 2025-05-16 18:16:26.863 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
[] 2025-05-16 18:16:26.879 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
[] 2025-05-16 18:16:26.879 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
[] 2025-05-16 18:16:26.881 [DEBUG] [main] [org.springframework.ui.context.support.UiApplicationContextUtils] 85 - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@4e9658b5]
[] 2025-05-16 18:16:26.883 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
[] 2025-05-16 18:16:26.884 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
[] 2025-05-16 18:16:26.890 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
[] 2025-05-16 18:16:26.890 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
[] 2025-05-16 18:16:26.925 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionAttributeSource'
[] 2025-05-16 18:16:26.931 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionInterceptor'
[] 2025-05-16 18:16:26.975 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
[] 2025-05-16 18:16:26.976 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
[] 2025-05-16 18:16:26.984 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
[] 2025-05-16 18:16:26.986 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
[] 2025-05-16 18:16:26.991 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2025-05-16 18:16:27.020 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2025-05-16 18:16:27.030 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
[] 2025-05-16 18:16:27.031 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2025-05-16 18:16:27.032 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatWebServerFactoryCustomizer'
[] 2025-05-16 18:16:27.032 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration'
[] 2025-05-16 18:16:27.034 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'environment'
[] 2025-05-16 18:16:27.034 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2025-05-16 18:16:27.037 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
[] 2025-05-16 18:16:27.037 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
[] 2025-05-16 18:16:27.038 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration$$EnhancerBySpringCGLIB$$4b6c79b2] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:27.046 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2025-05-16 18:16:27.051 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2025-05-16 18:16:27.099 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageCustomizer'
[] 2025-05-16 18:16:27.100 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
[] 2025-05-16 18:16:27.100 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$$EnhancerBySpringCGLIB$$a3c3c895] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:27.101 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServletRegistration'
[] 2025-05-16 18:16:27.101 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
[] 2025-05-16 18:16:27.102 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration$$EnhancerBySpringCGLIB$$2a1ee780] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:27.103 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2025-05-16 18:16:27.108 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2025-05-16 18:16:27.109 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartConfigElement'
[] 2025-05-16 18:16:27.109 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
[] 2025-05-16 18:16:27.110 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration$$EnhancerBySpringCGLIB$$ba5d01bf] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:27.110 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2025-05-16 18:16:27.126 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2025-05-16 18:16:27.136 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServlet'
[] 2025-05-16 18:16:27.136 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
[] 2025-05-16 18:16:27.136 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration$$EnhancerBySpringCGLIB$$c81752d9] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:27.137 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2025-05-16 18:16:27.137 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2025-05-16 18:16:27.162 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
[] 2025-05-16 18:16:27.190 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2025-05-16 18:16:27.192 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'dispatcherServletRegistration'
[] 2025-05-16 18:16:27.197 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'conventionErrorViewResolver'
[] 2025-05-16 18:16:27.198 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
[] 2025-05-16 18:16:27.200 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration$$EnhancerBySpringCGLIB$$b8589d35] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:27.202 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2025-05-16 18:16:27.206 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24313fcc'
[] 2025-05-16 18:16:27.206 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2025-05-16 18:16:27.354 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 82 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2025-05-16 18:16:27.355 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 126 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2025-05-16 18:16:27.356 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 151 - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
[] 2025-05-16 18:16:27.376 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2025-05-16 18:16:27.398 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2025-05-16 18:16:27.406 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2025-05-16 18:16:27.407 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.16]
[] 2025-05-16 18:16:27.419 [INFO] [main] [org.apache.catalina.core.AprLifecycleListener] 173 - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
[] 2025-05-16 18:16:27.627 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2025-05-16 18:16:27.629 [DEBUG] [main] [org.springframework.web.context.ContextLoader] 288 - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
[] 2025-05-16 18:16:27.629 [INFO] [main] [org.springframework.web.context.ContextLoader] 296 - Root WebApplicationContext: initialization completed in 2128 ms
[] 2025-05-16 18:16:27.637 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statViewServletRegistrationBean'
[] 2025-05-16 18:16:27.639 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidStatViewServletConfiguration'
[] 2025-05-16 18:16:27.663 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2025-05-16 18:16:27.674 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'statViewServletRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2025-05-16 18:16:27.683 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webStatFilterRegistrationBean'
[] 2025-05-16 18:16:27.683 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration'
[] 2025-05-16 18:16:27.685 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'webStatFilterRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2025-05-16 18:16:27.693 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestContextFilter'
[] 2025-05-16 18:16:27.698 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hiddenHttpMethodFilter'
[] 2025-05-16 18:16:27.699 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
[] 2025-05-16 18:16:27.725 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'formContentFilter'
[] 2025-05-16 18:16:27.732 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'characterEncodingFilter'
[] 2025-05-16 18:16:27.747 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping filters: filterRegistrationBean urls=[/*], characterEncodingFilter urls=[/*], hiddenHttpMethodFilter urls=[/*], formContentFilter urls=[/*], requestContextFilter urls=[/*]
[] 2025-05-16 18:16:27.749 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping servlets: dispatcherServlet urls=[/], statViewServlet urls=[/druid/*]
[] 2025-05-16 18:16:27.781 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter] 241 - Filter 'requestContextFilter' configured for use
[] 2025-05-16 18:16:27.782 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedHiddenHttpMethodFilter] 241 - Filter 'hiddenHttpMethodFilter' configured for use
[] 2025-05-16 18:16:27.783 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter] 241 - Filter 'characterEncodingFilter' configured for use
[] 2025-05-16 18:16:27.783 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedFormContentFilter] 241 - Filter 'formContentFilter' configured for use
[] 2025-05-16 18:16:27.801 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'myBatisApplication'
[] 2025-05-16 18:16:27.810 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'appConfig'
[] 2025-05-16 18:16:27.815 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai1'
[] 2025-05-16 18:16:27.820 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai2'
[] 2025-05-16 18:16:27.829 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userJDBCDao'
[] 2025-05-16 18:16:27.837 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:27.838 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'environmentProperties' with value of type String
[] 2025-05-16 18:16:27.840 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:27.840 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'environmentProperties' with value of type String
[] 2025-05-16 18:16:27.842 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'configurationProperties' with value of type Integer
[] 2025-05-16 18:16:27.842 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'environmentProperties' with value of type String
[] 2025-05-16 18:16:27.843 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:27.843 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'environmentProperties' with value of type String
[] 2025-05-16 18:16:27.888 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hanaController'
[] 2025-05-16 18:16:27.891 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'restTemplate'
[] 2025-05-16 18:16:27.960 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'helloController'
[] 2025-05-16 18:16:27.991 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userMapper'
[] 2025-05-16 18:16:28.005 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionFactory'
[] 2025-05-16 18:16:28.006 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration'
[] 2025-05-16 18:16:28.006 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$aaec1440] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:28.011 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2025-05-16 18:16:28.080 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2025-05-16 18:16:28.082 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24313fcc'
[] 2025-05-16 18:16:28.100 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSource'
[] 2025-05-16 18:16:28.102 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure'
[] 2025-05-16 18:16:28.111 [INFO] [main] [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure] 56 - Init DruidDataSource
[] 2025-05-16 18:16:28.178 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2025-05-16 18:16:28.199 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statFilter'
[] 2025-05-16 18:16:28.199 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration'
[] 2025-05-16 18:16:28.301 [INFO] [main] [com.alibaba.druid.pool.DruidDataSource] 928 - {dataSource-1} inited
[] 2025-05-16 18:16:28.318 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker'
[] 2025-05-16 18:16:28.323 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2025-05-16 18:16:28.324 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24313fcc'
[] 2025-05-16 18:16:28.332 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionFactory' via factory method to bean named 'dataSource'
[] 2025-05-16 18:16:28.549 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionTemplate'
[] 2025-05-16 18:16:28.551 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionTemplate' via factory method to bean named 'sqlSessionFactory'
[] 2025-05-16 18:16:28.570 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transitGoodsInfoMapper'
[] 2025-05-16 18:16:28.575 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'pageController'
[] 2025-05-16 18:16:28.577 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'voteService'
[] 2025-05-16 18:16:28.595 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
[] 2025-05-16 18:16:28.598 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
[] 2025-05-16 18:16:28.599 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
[] 2025-05-16 18:16:28.599 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
[] 2025-05-16 18:16:28.600 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration$$EnhancerBySpringCGLIB$$3b75be6] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:28.603 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2025-05-16 18:16:28.606 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2025-05-16 18:16:28.609 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskExecutorBuilder'
[] 2025-05-16 18:16:28.620 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration'
[] 2025-05-16 18:16:28.623 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultValidator'
[] 2025-05-16 18:16:28.648 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
[] 2025-05-16 18:16:28.651 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'error'
[] 2025-05-16 18:16:28.660 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameViewResolver'
[] 2025-05-16 18:16:28.663 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorAttributes'
[] 2025-05-16 18:16:28.666 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'basicErrorController'
[] 2025-05-16 18:16:28.668 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
[] 2025-05-16 18:16:28.669 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration'
[] 2025-05-16 18:16:28.669 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration$$EnhancerBySpringCGLIB$$4145e6bb] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:28.670 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2025-05-16 18:16:28.670 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconHandlerMapping'
[] 2025-05-16 18:16:28.676 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconRequestHandler'
[] 2025-05-16 18:16:28.697 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/**/favicon.ico] in 'faviconHandlerMapping'
[] 2025-05-16 18:16:28.699 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
[] 2025-05-16 18:16:28.699 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration$$EnhancerBySpringCGLIB$$e3051390] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:28.701 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@53fb3dab'
[] 2025-05-16 18:16:28.722 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
[] 2025-05-16 18:16:28.723 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$$EnhancerBySpringCGLIB$$8508de75] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:28.724 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2025-05-16 18:16:28.725 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2025-05-16 18:16:28.725 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@53fb3dab'
[] 2025-05-16 18:16:28.737 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
[] 2025-05-16 18:16:28.782 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcContentNegotiationManager'
[] 2025-05-16 18:16:28.805 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'messageConverters'
[] 2025-05-16 18:16:28.806 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
[] 2025-05-16 18:16:28.807 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$$EnhancerBySpringCGLIB$$924b8ea4] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:28.816 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'stringHttpMessageConverter'
[] 2025-05-16 18:16:28.816 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
[] 2025-05-16 18:16:28.817 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration$$EnhancerBySpringCGLIB$$c5595544] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:28.817 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2025-05-16 18:16:28.834 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
[] 2025-05-16 18:16:28.834 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
[] 2025-05-16 18:16:28.836 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapper'
[] 2025-05-16 18:16:28.836 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
[] 2025-05-16 18:16:28.838 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapperBuilder'
[] 2025-05-16 18:16:28.838 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
[] 2025-05-16 18:16:28.838 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$85f804ba] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:28.839 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24313fcc'
[] 2025-05-16 18:16:28.840 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
[] 2025-05-16 18:16:28.841 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
[] 2025-05-16 18:16:28.842 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2025-05-16 18:16:28.846 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24313fcc'
[] 2025-05-16 18:16:28.846 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2025-05-16 18:16:28.852 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'standardJacksonObjectMapperBuilderCustomizer'
[] 2025-05-16 18:16:28.864 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'parameterNamesModule'
[] 2025-05-16 18:16:28.865 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
[] 2025-05-16 18:16:28.873 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jsonComponentModule'
[] 2025-05-16 18:16:28.874 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
[] 2025-05-16 18:16:28.887 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapper' via factory method to bean named 'jacksonObjectMapperBuilder'
[] 2025-05-16 18:16:28.903 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'jacksonObjectMapper'
[] 2025-05-16 18:16:28.912 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcConversionService'
[] 2025-05-16 18:16:28.919 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcValidator'
[] 2025-05-16 18:16:28.922 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'applicationTaskExecutor'
[] 2025-05-16 18:16:28.923 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'applicationTaskExecutor' via factory method to bean named 'taskExecutorBuilder'
[] 2025-05-16 18:16:28.935 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2025-05-16 18:16:28.949 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter] 622 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[] 2025-05-16 18:16:28.976 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerMapping'
[] 2025-05-16 18:16:28.994 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcResourceUrlProvider'
[] 2025-05-16 18:16:29.086 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 350 - 15 mappings in 'requestMappingHandlerMapping'
[] 2025-05-16 18:16:29.092 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcPathMatcher'
[] 2025-05-16 18:16:29.099 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUrlPathHelper'
[] 2025-05-16 18:16:29.105 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewControllerHandlerMapping'
[] 2025-05-16 18:16:29.109 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameHandlerMapping'
[] 2025-05-16 18:16:29.112 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'resourceHandlerMapping'
[] 2025-05-16 18:16:29.124 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
[] 2025-05-16 18:16:29.124 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultServletHandlerMapping'
[] 2025-05-16 18:16:29.125 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUriComponentsContributor'
[] 2025-05-16 18:16:29.127 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
[] 2025-05-16 18:16:29.128 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
[] 2025-05-16 18:16:29.128 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'handlerExceptionResolver'
[] 2025-05-16 18:16:29.135 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver] 302 - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
[] 2025-05-16 18:16:29.139 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcViewResolver'
[] 2025-05-16 18:16:29.142 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultViewResolver'
[] 2025-05-16 18:16:29.157 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewResolver'
[] 2025-05-16 18:16:29.159 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@53fb3dab'
[] 2025-05-16 18:16:29.161 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'thymeleafViewResolver'
[] 2025-05-16 18:16:29.163 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration'
[] 2025-05-16 18:16:29.166 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration$$EnhancerBySpringCGLIB$$b98c1db] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:29.168 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2025-05-16 18:16:29.181 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'templateEngine'
[] 2025-05-16 18:16:29.181 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration'
[] 2025-05-16 18:16:29.182 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration$$EnhancerBySpringCGLIB$$36e005ac] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:29.185 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultTemplateResolver'
[] 2025-05-16 18:16:29.185 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration'
[] 2025-05-16 18:16:29.185 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration$$EnhancerBySpringCGLIB$$f5108b03] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:29.185 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2025-05-16 18:16:29.186 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24313fcc'
[] 2025-05-16 18:16:29.194 [WARN] [main] [org.thymeleaf.templatemode.TemplateMode] 150 - [THYMELEAF][main] Template Mode 'LEGACYHTML5' is deprecated. Using Template Mode 'HTML' instead.
[] 2025-05-16 18:16:29.199 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2025-05-16 18:16:29.199 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration' via constructor to bean named 'defaultTemplateResolver'
[] 2025-05-16 18:16:29.215 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'java8TimeDialect'
[] 2025-05-16 18:16:29.215 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafJava8TimeDialect'
[] 2025-05-16 18:16:29.221 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2025-05-16 18:16:29.221 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration' via constructor to bean named 'templateEngine'
[] 2025-05-16 18:16:29.226 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'welcomePageHandlerMapping'
[] 2025-05-16 18:16:29.227 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24313fcc'
[] 2025-05-16 18:16:29.230 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:29.230 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:16:29.231 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 61 - Adding welcome page template: index
[] 2025-05-16 18:16:29.235 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
[] 2025-05-16 18:16:29.239 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
[] 2025-05-16 18:16:29.245 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
[] 2025-05-16 18:16:29.247 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializationConfiguration'
[] 2025-05-16 18:16:29.247 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
[] 2025-05-16 18:16:29.248 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisLanguageDriverAutoConfiguration'
[] 2025-05-16 18:16:29.249 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration'
[] 2025-05-16 18:16:29.250 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 261 - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
[] 2025-05-16 18:16:29.250 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration'
[] 2025-05-16 18:16:29.252 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanExporter'
[] 2025-05-16 18:16:29.252 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'objectNamingStrategy'
[] 2025-05-16 18:16:29.254 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'objectNamingStrategy'
[] 2025-05-16 18:16:29.257 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanServer'
[] 2025-05-16 18:16:29.259 [DEBUG] [main] [org.springframework.jmx.support.JmxUtils] 126 - Found MBeanServer: com.sun.jmx.mbeanserver.JmxMBeanServer@246d3faf
[] 2025-05-16 18:16:29.273 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
[] 2025-05-16 18:16:29.274 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
[] 2025-05-16 18:16:29.277 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
[] 2025-05-16 18:16:29.277 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$LoggingCodecConfiguration'
[] 2025-05-16 18:16:29.279 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'loggingCodecCustomizer'
[] 2025-05-16 18:16:29.280 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'loggingCodecCustomizer' via factory method to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2025-05-16 18:16:29.284 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$JacksonCodecConfiguration'
[] 2025-05-16 18:16:29.284 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonCodecCustomizer'
[] 2025-05-16 18:16:29.284 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonCodecCustomizer' via factory method to bean named 'jacksonObjectMapper'
[] 2025-05-16 18:16:29.285 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration'
[] 2025-05-16 18:16:29.286 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
[] 2025-05-16 18:16:29.286 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration$$EnhancerBySpringCGLIB$$b411d029] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:29.287 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2025-05-16 18:16:29.288 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2025-05-16 18:16:29.289 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration'
[] 2025-05-16 18:16:29.289 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration$$EnhancerBySpringCGLIB$$90a2f17d] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:29.290 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2025-05-16 18:16:29.290 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'dataSource'
[] 2025-05-16 18:16:29.290 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2025-05-16 18:16:29.291 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jdbcTemplate'
[] 2025-05-16 18:16:29.331 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$NamedParameterJdbcTemplateConfiguration'
[] 2025-05-16 18:16:29.332 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'namedParameterJdbcTemplate'
[] 2025-05-16 18:16:29.333 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'namedParameterJdbcTemplate' via factory method to bean named 'jdbcTemplate'
[] 2025-05-16 18:16:29.336 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
[] 2025-05-16 18:16:29.337 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
[] 2025-05-16 18:16:29.338 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskSchedulerBuilder'
[] 2025-05-16 18:16:29.339 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2025-05-16 18:16:29.341 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'taskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2025-05-16 18:16:29.343 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration'
[] 2025-05-16 18:16:29.343 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration'
[] 2025-05-16 18:16:29.344 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration'
[] 2025-05-16 18:16:29.345 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration$$EnhancerBySpringCGLIB$$25185b7f] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:29.346 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration' via constructor to bean named 'dataSource'
[] 2025-05-16 18:16:29.346 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
[] 2025-05-16 18:16:29.346 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
[] 2025-05-16 18:16:29.349 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
[] 2025-05-16 18:16:29.351 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionManager'
[] 2025-05-16 18:16:29.351 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'transactionManager' via factory method to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2025-05-16 18:16:29.355 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
[] 2025-05-16 18:16:29.355 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration'
[] 2025-05-16 18:16:29.355 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration'
[] 2025-05-16 18:16:29.355 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
[] 2025-05-16 18:16:29.356 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration$$EnhancerBySpringCGLIB$$76a22052] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:29.356 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration' via constructor to bean named 'transactionManager'
[] 2025-05-16 18:16:29.357 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionTemplate'
[] 2025-05-16 18:16:29.359 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
[] 2025-05-16 18:16:29.359 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration$$EnhancerBySpringCGLIB$$132dbc91] - unable to determine constructor/method parameter names
[] 2025-05-16 18:16:29.360 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'restTemplateBuilder'
[] 2025-05-16 18:16:29.364 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration'
[] 2025-05-16 18:16:29.364 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartResolver'
[] 2025-05-16 18:16:29.381 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 433 - Registering beans for JMX exposure on startup
[] 2025-05-16 18:16:29.381 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 540 - Autodetecting user-defined JMX MBeans
[] 2025-05-16 18:16:29.383 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'statFilter' has been autodetected for JMX exposure
[] 2025-05-16 18:16:29.383 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'dataSource' has been autodetected for JMX exposure
[] 2025-05-16 18:16:29.392 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'dataSource': registering with JMX server as MBean [com.alibaba.druid.spring.boot.autoconfigure:name=dataSource,type=DruidDataSourceWrapper]
[] 2025-05-16 18:16:29.392 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'statFilter': registering with JMX server as MBean [com.alibaba.druid.filter.stat:name=statFilter,type=StatFilter]
[] 2025-05-16 18:16:29.428 [DEBUG] [main] [org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener] 132 - 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   CodecsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.codec.CodecConfigurer' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration#jacksonCodecCustomizer matched:
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   DataSourceConfiguration.Generic matched:
      - @ConditionalOnProperty (spring.datasource.type) matched (OnPropertyCondition)

   DataSourceJmxConfiguration matched:
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   DataSourceJmxConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration#transactionManager matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet; types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   DruidDataSourceAutoConfigure matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.pool.DruidDataSource' (OnClassCondition)

   DruidDataSourceAutoConfigure#dataSource matched:
      - @ConditionalOnMissingBean (types: javax.sql.DataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidFilterConfiguration#statFilter matched:
      - @ConditionalOnProperty (spring.datasource.druid.filter.stat.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: com.alibaba.druid.filter.stat.StatFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidStatViewServletConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.stat-view-servlet.enabled=true) matched (OnPropertyCondition)

   DruidWebStatFilterConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.web-stat-filter.enabled=true) matched (OnPropertyCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.DefaultErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnProperty (server.error.whitelabel.enabled) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GenericCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration automatic cache type (CacheCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.http.encoding.enabled) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnProperty (spring.http.converters.preferred-json-mapper=jackson) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   JdbcTemplateAutoConfiguration.JdbcTemplateConfiguration#jdbcTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration.NamedParameterJdbcTemplateConfiguration#namedParameterJdbcTemplate matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a primary bean from beans 'jdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   JmxAutoConfiguration#mbeanExporter matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.MBeanExporter; SearchStrategy: current) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#mbeanServer matched:
      - @ConditionalOnMissingBean (types: javax.management.MBeanServer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#objectNamingStrategy matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.naming.ObjectNamingStrategy; SearchStrategy: current) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'javax.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.servlet.multipart.enabled) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnMissingBean (types: javax.servlet.MultipartConfigElement,org.springframework.web.multipart.commons.CommonsMultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionFactory matched:
      - @ConditionalOnMissingBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionTemplate matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisLanguageDriverAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)

   NoOpCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration automatic cache type (CacheCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnProperty (spring.dao.exceptiontranslation.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SimpleCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration automatic cache type (CacheCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutionAutoConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration#taskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.thymeleaf.templatemode.TemplateMode' (OnClassCondition)

   ThymeleafAutoConfiguration.DefaultTemplateResolverConfiguration matched:
      - @ConditionalOnMissingBean (names: defaultTemplateResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafDefaultConfiguration#templateEngine matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.spring5.SpringTemplateEngine; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect matched:
      - @ConditionalOnClass found required class 'org.thymeleaf.extras.java8time.dialect.Java8TimeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect#java8TimeDialect matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.extras.java8time.dialect.Java8TimeDialect; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.thymeleaf.enabled) matched (OnPropertyCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration.ThymeleafViewResolverConfiguration#thymeleafViewResolver matched:
      - @ConditionalOnMissingBean (names: thymeleafViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found bean 'transactionManager'; @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a primary bean from beans 'transactionManager' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.validation.executable.ExecutableValidator' (OnClassCondition)
      - @ConditionalOnResource found location classpath:META-INF/services/javax.validation.spi.ValidationProvider (OnResourceCondition)

   ValidationAutoConfiguration#defaultValidator matched:
      - @ConditionalOnMissingBean (types: javax.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration#methodValidationPostProcessor matched:
      - @ConditionalOnMissingBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnProperty (spring.mvc.formcontent.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter matched:
      - @ConditionalOnProperty (spring.mvc.hiddenmethod.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.HiddenHttpMethodFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver; types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter.FaviconConfiguration matched:
      - @ConditionalOnProperty (spring.mvc.favicon.enabled) matched (OnPropertyCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'javax.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   AopAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.aspectj.lang.annotation.Aspect' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.cache.interceptor.CacheAspectSupport; SearchStrategy: all) did not find any beans of type org.springframework.cache.interceptor.CacheAspectSupport (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerJpaDependencyConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.github.benmanes.caffeine.cache.Caffeine', 'org.springframework.cache.caffeine.CaffeineCacheManager' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Session' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CloudServiceConnectorsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.config.java.CloudScanConfiguration' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.couchbase.client.java.Bucket', 'com.couchbase.client.spring.cache.CouchbaseCacheManager' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 2 matched 0 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) matched (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Hikari:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) found different value in property 'spring.datasource.type' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration.TomcatDataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSourceProxy' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   DruidFilterConfiguration#commonsLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.commons-log.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#configFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.config.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#encodingConvertFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.encoding.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4j2Filter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j2.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4jFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#slf4jLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.slf4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallConfig:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidSpringAopConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.aop-patterns) did not find property 'spring.datasource.druid.aop-patterns' (OnPropertyCondition)

   EhCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'net.sf.ehcache.Cache', 'org.springframework.cache.ehcache.EhCacheCacheManager' (OnClassCondition)

   ElasticsearchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedMongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.WebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.hazelcast.core.HazelcastInstance', 'com.hazelcast.spring.cache.HazelcastCacheManager' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HibernateJpaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.persistence.EntityManager' (OnClassCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.Resource' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   InfluxDbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.influxdb.InfluxDB' (OnClassCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'javax.cache.Caching', 'org.springframework.cache.jcache.JCacheCacheManager' (OnClassCondition)

   JacksonAutoConfiguration.JodaDateTimeJacksonConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.joda.time.DateTime', 'com.fasterxml.jackson.datatype.joda.ser.DateTimeSerializer', 'com.fasterxml.jackson.datatype.joda.cfg.JacksonJodaDateFormat' (OnClassCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.JdbcConfiguration' (OnClassCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JestAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.searchbox.client.JestClient' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.Message' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JtaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.Transaction' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.mail.internet.MimeMessage' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate did not find required type 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnBeanCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.FreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyVelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.velocity.Driver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.ThymeleafConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.VelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.SessionFactory' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.Neo4jSession' (OnClassCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.jwt.JwtDecoder' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)

   ReactiveSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactorCoreAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)

   RedisReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   RestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClient' (OnClassCondition)

   SecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityFilterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)

   SecurityRequestMatcherProviderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.web.util.matcher.RequestMatcher' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SolrAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.impl.CloudSolrClient' (OnClassCondition)

   SolrRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.SolrClient' (OnClassCondition)

   SpringApplicationAdminJmxAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.application.admin.enabled=true) did not find property 'enabled' (OnPropertyCondition)

   SpringDataWebAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.web.PageableHandlerMethodArgumentResolver' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskScheduler:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   ThymeleafAutoConfiguration.DataAttributeDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.mxab.thymeleaf.extras.dataattribute.dialect.DataAttributeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafReactiveConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafSecurityDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.extras.springsecurity5.dialect.SpringSecurityDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebFluxConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafWebLayoutConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'nz.net.ultraq.thymeleaf.LayoutDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration#resourceUrlEncodingFilter:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)

   WebClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#localeResolver:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.locale) did not find property 'locale' (OnPropertyCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.oxm.Marshaller' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.websocket.jsr356.server.deploy.WebSocketServerContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.TransactionManager' (OnClassCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration



[] 2025-05-16 18:16:29.431 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2025-05-16 18:16:29.460 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 204 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2025-05-16 18:16:29.462 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 59 - Started MyBatisApplication in 4.738 seconds (JVM running for 5.158)
[] 2025-05-16 18:17:05.628 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2025-05-16 18:17:05.629 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2025-05-16 18:17:05.629 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 525 - Detected StandardServletMultipartResolver
[] 2025-05-16 18:17:05.655 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 541 - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
[] 2025-05-16 18:17:05.656 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 27 ms
[] 2025-05-16 18:17:05.671 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 90 - GET "/hana/getOne", parameters={}
[] 2025-05-16 18:17:05.677 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 420 - Mapped to public java.lang.String com.tomas.mybaties3.rest.HanaController.updateOne(java.lang.String)
[] 2025-05-16 18:17:05.708 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 268 - Using 'text/html', given [text/html, application/xhtml+xml, image/avif, image/webp, image/apng, application/xml;q=0.9, application/signed-exchange;v=b3;q=0.7, */*;q=0.8] and supported [text/plain, */*, text/plain, */*, application/json, application/*+json, application/json, application/*+json]
[] 2025-05-16 18:17:05.710 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 90 - Writing ["false"]
[] 2025-05-16 18:17:05.720 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 1130 - Completed 200 OK
[] 2025-05-16 18:17:06.058 [DEBUG] [http-nio-8081-exec-2] [org.springframework.web.servlet.DispatcherServlet] 90 - GET "/favicon.ico", parameters={}
[] 2025-05-16 18:17:06.060 [DEBUG] [http-nio-8081-exec-2] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 420 - Mapped to ResourceHttpRequestHandler [class path resource [META-INF/resources/], class path resource [resources/], class path resource [static/], class path resource [public/], ServletContext resource [/], class path resource []]
[] 2025-05-16 18:17:06.099 [DEBUG] [http-nio-8081-exec-2] [org.springframework.web.servlet.DispatcherServlet] 1130 - Completed 200 OK
[] 2025-05-16 18:18:37.828 [DEBUG] [Thread-3] [org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext] 1002 - Closing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24313fcc, started on Fri May 16 18:16:25 CST 2025
[] 2025-05-16 18:18:37.851 [DEBUG] [Thread-3] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 451 - Unregistering JMX-exposed beans on shutdown
[] 2025-05-16 18:18:37.852 [DEBUG] [Thread-3] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 185 - Unregistering JMX-exposed beans
[] 2025-05-16 18:18:37.853 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2025-05-16 18:18:37.860 [INFO] [Thread-3] [com.alibaba.druid.pool.DruidDataSource] 1823 - {dataSource-1} closed
[] 2025-05-16 18:18:42.601 [DEBUG] [main] [org.springframework.boot.context.logging.ClasspathLoggingApplicationListener] 53 - Application started with classpath: [file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/charsets.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/cldrdata.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/dnsns.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/jaccess.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/jfxrt.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/localedata.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/nashorn.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/sunec.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/sunjce_provider.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/sunpkcs11.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/ext/zipfs.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/jce.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/jfr.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/jfxswt.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/jsse.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/management-agent.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/resources.jar, file:/Users/<USER>/Library/Java/JavaVirtualMachines/corretto-1.8.0_392/Contents/Home/jre/lib/rt.jar, file:/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-web/2.1.3.RELEASE/spring-boot-starter-web-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter/2.1.3.RELEASE/spring-boot-starter-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-logging/2.1.3.RELEASE/spring-boot-starter-logging-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-classic/1.2.3/logback-classic-1.2.3.jar, file:/Users/<USER>/.m2/repository/ch/qos/logback/logback-core/1.2.3/logback-core-1.2.3.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-to-slf4j/2.11.2/log4j-to-slf4j-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.11.2/log4j-api-2.11.2.jar, file:/Users/<USER>/.m2/repository/org/slf4j/jul-to-slf4j/1.7.25/jul-to-slf4j-1.7.25.jar, file:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar, file:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/1.23/snakeyaml-1.23.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-json/2.1.3.RELEASE/spring-boot-starter-json-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.9.8/jackson-databind-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.9.0/jackson-annotations-2.9.0.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.9.8/jackson-core-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jdk8/2.9.8/jackson-datatype-jdk8-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.9.8/jackson-datatype-jsr310-2.9.8.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-parameter-names/2.9.8/jackson-module-parameter-names-2.9.8.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-tomcat/2.1.3.RELEASE/spring-boot-starter-tomcat-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-core/9.0.16/tomcat-embed-core-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-el/9.0.16/tomcat-embed-el-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/apache/tomcat/embed/tomcat-embed-websocket/9.0.16/tomcat-embed-websocket-9.0.16.jar, file:/Users/<USER>/.m2/repository/org/hibernate/validator/hibernate-validator/6.0.14.Final/hibernate-validator-6.0.14.Final.jar, file:/Users/<USER>/.m2/repository/javax/validation/validation-api/2.0.1.Final/validation-api-2.0.1.Final.jar, file:/Users/<USER>/.m2/repository/org/jboss/logging/jboss-logging/3.3.2.Final/jboss-logging-3.3.2.Final.jar, file:/Users/<USER>/.m2/repository/com/fasterxml/classmate/1.4.0/classmate-1.4.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-web/5.1.5.RELEASE/spring-web-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-beans/5.1.5.RELEASE/spring-beans-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-webmvc/5.1.5.RELEASE/spring-webmvc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-aop/5.1.5.RELEASE/spring-aop-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-context/5.1.5.RELEASE/spring-context-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-expression/5.1.5.RELEASE/spring-expression-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-starter/2.1.1/mybatis-spring-boot-starter-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/spring/boot/mybatis-spring-boot-autoconfigure/2.1.1/mybatis-spring-boot-autoconfigure-2.1.1.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis/3.5.3/mybatis-3.5.3.jar, file:/Users/<USER>/.m2/repository/org/mybatis/mybatis-spring/2.0.3/mybatis-spring-2.0.3.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.2/httpclient-4.5.2.jar, file:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.11/httpcore-4.4.11.jar, file:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.11/commons-codec-1.11.jar, file:/Users/<USER>/.m2/repository/com/alibaba/fastjson/1.2.75/fastjson-1.2.75.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid-spring-boot-starter/1.1.9/druid-spring-boot-starter-1.1.9.jar, file:/Users/<USER>/.m2/repository/com/alibaba/druid/1.1.9/druid-1.1.9.jar, file:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.25/slf4j-api-1.7.25.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-autoconfigure/2.1.3.RELEASE/spring-boot-autoconfigure-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/8.0.15/mysql-connector-java-8.0.15.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-jdbc/2.1.3.RELEASE/spring-boot-starter-jdbc-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/HikariCP/3.2.0/HikariCP-3.2.0.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/5.1.5.RELEASE/spring-jdbc-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/statemachine/spring-statemachine-core/2.0.1.RELEASE/spring-statemachine-core-2.0.1.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-tx/5.1.5.RELEASE/spring-tx-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-messaging/5.1.5.RELEASE/spring-messaging-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-core/5.1.5.RELEASE/spring-core-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/springframework/spring-jcl/5.1.5.RELEASE/spring-jcl-5.1.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.16.22/lombok-1.16.22.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.12.0/commons-lang3-3.12.0.jar, file:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.2.5/hutool-all-5.2.5.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi/4.1.2/poi-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.4/commons-collections4-4.4.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar, file:/Users/<USER>/.m2/repository/com/zaxxer/SparseBitSet/1.2/SparseBitSet-1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/4.1.2/poi-ooxml-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/4.1.2/poi-ooxml-schemas-4.1.2.jar, file:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/3.1.0/xmlbeans-3.1.0.jar, file:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.19/commons-compress-1.19.jar, file:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.06/curvesapi-1.06.jar, file:/Users/<USER>/.m2/repository/com/google/guava/guava/30.1-jre/guava-30.1-jre.jar, file:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.1/failureaccess-1.0.1.jar, file:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, file:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar, file:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.5.0/checker-qual-3.5.0.jar, file:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.3.4/error_prone_annotations-2.3.4.jar, file:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/1.3/j2objc-annotations-1.3.jar, file:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar, file:/Users/<USER>/.m2/repository/org/springframework/boot/spring-boot-starter-thymeleaf/2.1.3.RELEASE/spring-boot-starter-thymeleaf-2.1.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf-spring5/3.0.11.RELEASE/thymeleaf-spring5-3.0.11.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/thymeleaf/3.0.11.RELEASE/thymeleaf-3.0.11.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/attoparser/attoparser/2.0.5.RELEASE/attoparser-2.0.5.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/unbescape/unbescape/1.1.6.RELEASE/unbescape-1.1.6.RELEASE.jar, file:/Users/<USER>/.m2/repository/org/thymeleaf/extras/thymeleaf-extras-java8time/3.0.3.RELEASE/thymeleaf-extras-java8time-3.0.3.RELEASE.jar, file:/Users/<USER>/.m2/repository/com/alibaba/easyexcel/2.2.10/easyexcel-2.2.10.jar, file:/Users/<USER>/.m2/repository/cglib/cglib/3.1/cglib-3.1.jar, file:/Users/<USER>/.m2/repository/org/ow2/asm/asm/4.2/asm-4.2.jar, file:/Users/<USER>/.m2/repository/org/ehcache/ehcache/3.6.3/ehcache-3.6.3.jar, file:/Users/<USER>/.m2/repository/io/swagger/swagger-annotations/1.6.4/swagger-annotations-1.6.4.jar, file:/Applications/IntelliJ%20IDEA%20CE.app/Contents/lib/idea_rt.jar]
[] 2025-05-16 18:18:42.730 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 50 - Starting MyBatisApplication on MacBook-Pro-3.local with PID 39764 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2025-05-16 18:18:42.731 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 675 - No active profile set, falling back to default profiles: default
[] 2025-05-16 18:18:42.732 [DEBUG] [main] [org.springframework.boot.SpringApplication] 703 - Loading source class com.tomas.mybaties3.MyBatisApplication
[] 2025-05-16 18:18:42.882 [DEBUG] [main] [org.springframework.boot.context.config.ConfigFileApplicationListener] 224 - Loaded config file 'file:/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/application.yml' (classpath:/application.yml)
[] 2025-05-16 18:18:42.883 [DEBUG] [main] [org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext] 594 - Refreshing org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24aed80c
[] 2025-05-16 18:18:42.904 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalConfigurationAnnotationProcessor'
[] 2025-05-16 18:18:42.915 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.internalCachingMetadataReaderFactory'
[] 2025-05-16 18:18:43.081 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/AppConfig.class]
[] 2025-05-16 18:18:43.088 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai1.class]
[] 2025-05-16 18:18:43.089 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/config/Louzai2.class]
[] 2025-05-16 18:18:43.106 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/jdbc/UserJDBCDao.class]
[] 2025-05-16 18:18:43.182 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/HanaController.class]
[] 2025-05-16 18:18:43.209 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/HelloController.class]
[] 2025-05-16 18:18:43.211 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/rest/PageController.class]
[] 2025-05-16 18:18:43.219 [DEBUG] [main] [org.springframework.context.annotation.ClassPathBeanDefinitionScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/service/impl/VoteServiceImpl.class]
[] 2025-05-16 18:18:43.453 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:43.454 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:43.523 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:43.526 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:43.527 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:43.539 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.type' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:43.675 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.condition.BeanTypeRegistry'
[] 2025-05-16 18:18:43.743 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:43.743 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:43.823 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.AutoConfigurationPackages'
[] 2025-05-16 18:18:43.830 [DEBUG] [main] [org.springframework.boot.autoconfigure.AutoConfigurationPackages] 206 - @EnableAutoConfiguration was declared on a class in the package 'com.tomas.mybaties3'. Automatic @Repository and @Entity scanning is enabled.
[] 2025-05-16 18:18:43.834 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 224 - Searching for mappers annotated with @Mapper
[] 2025-05-16 18:18:43.836 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 228 - Using auto-configuration base package 'com.tomas.mybaties3'
[] 2025-05-16 18:18:43.967 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.mapper.MapperScannerConfigurer'
[] 2025-05-16 18:18:43.972 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'propertySourcesPlaceholderConfigurer'
[] 2025-05-16 18:18:43.991 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/TransitGoodsInfoMapper.class]
[] 2025-05-16 18:18:43.991 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 437 - Identified candidate component class: file [/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/target/classes/com/tomas/mybaties3/dao/UserMapper.class]
[] 2025-05-16 18:18:43.997 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'transitGoodsInfoMapper' and 'com.tomas.mybaties3.dao.TransitGoodsInfoMapper' mapperInterface
[] 2025-05-16 18:18:43.997 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'transitGoodsInfoMapper'.
[] 2025-05-16 18:18:43.997 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Creating MapperFactoryBean with name 'userMapper' and 'com.tomas.mybaties3.dao.UserMapper' mapperInterface
[] 2025-05-16 18:18:43.997 [DEBUG] [main] [org.mybatis.spring.mapper.ClassPathMapperScanner] 49 - Enabling autowire by type for MapperFactoryBean with name 'userMapper'.
[] 2025-05-16 18:18:44.195 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerProcessor'
[] 2025-05-16 18:18:44.197 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationBeanFactoryMetadata'
[] 2025-05-16 18:18:44.197 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'preserveErrorControllerTargetClassPostProcessor'
[] 2025-05-16 18:18:44.199 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.event.internalEventListenerFactory'
[] 2025-05-16 18:18:44.199 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionalEventListenerFactory'
[] 2025-05-16 18:18:44.223 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalAutowiredAnnotationProcessor'
[] 2025-05-16 18:18:44.226 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.context.annotation.internalCommonAnnotationProcessor'
[] 2025-05-16 18:18:44.230 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.context.properties.ConfigurationPropertiesBindingPostProcessor'
[] 2025-05-16 18:18:44.233 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'methodValidationPostProcessor'
[] 2025-05-16 18:18:44.261 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'methodValidationPostProcessor' via factory method to bean named 'environment'
[] 2025-05-16 18:18:44.269 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSourceInitializerPostProcessor'
[] 2025-05-16 18:18:44.278 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'persistenceExceptionTranslationPostProcessor'
[] 2025-05-16 18:18:44.280 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'persistenceExceptionTranslationPostProcessor' via factory method to bean named 'environment'
[] 2025-05-16 18:18:44.282 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.aop.config.internalAutoProxyCreator'
[] 2025-05-16 18:18:44.298 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webServerFactoryCustomizerBeanPostProcessor'
[] 2025-05-16 18:18:44.299 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageRegistrarBeanPostProcessor'
[] 2025-05-16 18:18:44.301 [DEBUG] [main] [org.springframework.ui.context.support.UiApplicationContextUtils] 85 - Unable to locate ThemeSource with name 'themeSource': using default [org.springframework.ui.context.support.ResourceBundleThemeSource@82c57b3]
[] 2025-05-16 18:18:44.303 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactory'
[] 2025-05-16 18:18:44.303 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryConfiguration$EmbeddedTomcat'
[] 2025-05-16 18:18:44.309 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.config.internalTransactionAdvisor'
[] 2025-05-16 18:18:44.309 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.transaction.annotation.ProxyTransactionManagementConfiguration'
[] 2025-05-16 18:18:44.338 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionAttributeSource'
[] 2025-05-16 18:18:44.344 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionInterceptor'
[] 2025-05-16 18:18:44.368 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'websocketServletWebServerCustomizer'
[] 2025-05-16 18:18:44.368 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration$TomcatWebSocketConfiguration'
[] 2025-05-16 18:18:44.374 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'servletWebServerFactoryCustomizer'
[] 2025-05-16 18:18:44.374 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.ServletWebServerFactoryAutoConfiguration'
[] 2025-05-16 18:18:44.378 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2025-05-16 18:18:44.409 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'servletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2025-05-16 18:18:44.420 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatServletWebServerFactoryCustomizer'
[] 2025-05-16 18:18:44.424 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatServletWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2025-05-16 18:18:44.425 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'tomcatWebServerFactoryCustomizer'
[] 2025-05-16 18:18:44.426 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration$TomcatWebServerFactoryCustomizerConfiguration'
[] 2025-05-16 18:18:44.429 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'environment'
[] 2025-05-16 18:18:44.429 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'tomcatWebServerFactoryCustomizer' via factory method to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2025-05-16 18:18:44.434 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'localeCharsetMappingsCustomizer'
[] 2025-05-16 18:18:44.435 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration'
[] 2025-05-16 18:18:44.435 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration$$EnhancerBySpringCGLIB$$677bad77] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:44.443 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2025-05-16 18:18:44.446 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.HttpEncodingAutoConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2025-05-16 18:18:44.495 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorPageCustomizer'
[] 2025-05-16 18:18:44.496 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration'
[] 2025-05-16 18:18:44.497 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$$EnhancerBySpringCGLIB$$bfd2fc5a] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:44.498 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServletRegistration'
[] 2025-05-16 18:18:44.499 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration'
[] 2025-05-16 18:18:44.499 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration$$EnhancerBySpringCGLIB$$462e1b45] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:44.499 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2025-05-16 18:18:44.504 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletRegistrationConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2025-05-16 18:18:44.504 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartConfigElement'
[] 2025-05-16 18:18:44.505 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration'
[] 2025-05-16 18:18:44.505 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration$$EnhancerBySpringCGLIB$$d66c3584] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:44.505 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2025-05-16 18:18:44.516 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.MultipartAutoConfiguration' via constructor to bean named 'spring.servlet.multipart-org.springframework.boot.autoconfigure.web.servlet.MultipartProperties'
[] 2025-05-16 18:18:44.526 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dispatcherServlet'
[] 2025-05-16 18:18:44.526 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration'
[] 2025-05-16 18:18:44.527 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration$$EnhancerBySpringCGLIB$$e426869e] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:44.528 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2025-05-16 18:18:44.528 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration$DispatcherServletConfiguration' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2025-05-16 18:18:44.550 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'dispatcherServletRegistration' via factory method to bean named 'dispatcherServlet'
[] 2025-05-16 18:18:44.574 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'server-org.springframework.boot.autoconfigure.web.ServerProperties'
[] 2025-05-16 18:18:44.575 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration' via constructor to bean named 'dispatcherServletRegistration'
[] 2025-05-16 18:18:44.577 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'conventionErrorViewResolver'
[] 2025-05-16 18:18:44.578 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration'
[] 2025-05-16 18:18:44.578 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration$$EnhancerBySpringCGLIB$$d467d0fa] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:44.579 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2025-05-16 18:18:44.584 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24aed80c'
[] 2025-05-16 18:18:44.584 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$DefaultErrorViewResolverConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2025-05-16 18:18:44.718 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 82 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2025-05-16 18:18:44.718 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 126 - Code archive: /Users/<USER>/.m2/repository/org/springframework/boot/spring-boot/2.1.3.RELEASE/spring-boot-2.1.3.RELEASE.jar
[] 2025-05-16 18:18:44.719 [DEBUG] [main] [org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory] 151 - None of the document roots [src/main/webapp, public, static] point to a directory and will be ignored.
[] 2025-05-16 18:18:44.737 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2025-05-16 18:18:44.756 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2025-05-16 18:18:44.764 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2025-05-16 18:18:44.765 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.16]
[] 2025-05-16 18:18:44.773 [INFO] [main] [org.apache.catalina.core.AprLifecycleListener] 173 - The APR based Apache Tomcat Native library which allows optimal performance in production environments was not found on the java.library.path: [/Users/<USER>/Library/Java/Extensions:/Library/Java/Extensions:/Network/Library/Java/Extensions:/System/Library/Java/Extensions:/usr/lib/java:.]
[] 2025-05-16 18:18:44.973 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2025-05-16 18:18:44.974 [DEBUG] [main] [org.springframework.web.context.ContextLoader] 288 - Published root WebApplicationContext as ServletContext attribute with name [org.springframework.web.context.WebApplicationContext.ROOT]
[] 2025-05-16 18:18:44.974 [INFO] [main] [org.springframework.web.context.ContextLoader] 296 - Root WebApplicationContext: initialization completed in 2091 ms
[] 2025-05-16 18:18:44.981 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statViewServletRegistrationBean'
[] 2025-05-16 18:18:44.983 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidStatViewServletConfiguration'
[] 2025-05-16 18:18:44.998 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2025-05-16 18:18:45.004 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'statViewServletRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2025-05-16 18:18:45.012 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'webStatFilterRegistrationBean'
[] 2025-05-16 18:18:45.013 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidWebStatFilterConfiguration'
[] 2025-05-16 18:18:45.014 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'webStatFilterRegistrationBean' via factory method to bean named 'spring.datasource.druid-com.alibaba.druid.spring.boot.autoconfigure.properties.DruidStatProperties'
[] 2025-05-16 18:18:45.020 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestContextFilter'
[] 2025-05-16 18:18:45.024 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hiddenHttpMethodFilter'
[] 2025-05-16 18:18:45.024 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration'
[] 2025-05-16 18:18:45.054 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'formContentFilter'
[] 2025-05-16 18:18:45.059 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'characterEncodingFilter'
[] 2025-05-16 18:18:45.068 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping filters: filterRegistrationBean urls=[/*], characterEncodingFilter urls=[/*], hiddenHttpMethodFilter urls=[/*], formContentFilter urls=[/*], requestContextFilter urls=[/*]
[] 2025-05-16 18:18:45.069 [DEBUG] [main] [org.springframework.boot.web.servlet.ServletContextInitializerBeans] 267 - Mapping servlets: dispatcherServlet urls=[/], statViewServlet urls=[/druid/*]
[] 2025-05-16 18:18:45.087 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedRequestContextFilter] 241 - Filter 'requestContextFilter' configured for use
[] 2025-05-16 18:18:45.088 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedHiddenHttpMethodFilter] 241 - Filter 'hiddenHttpMethodFilter' configured for use
[] 2025-05-16 18:18:45.089 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedCharacterEncodingFilter] 241 - Filter 'characterEncodingFilter' configured for use
[] 2025-05-16 18:18:45.089 [DEBUG] [main] [org.springframework.boot.web.servlet.filter.OrderedFormContentFilter] 241 - Filter 'formContentFilter' configured for use
[] 2025-05-16 18:18:45.099 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'myBatisApplication'
[] 2025-05-16 18:18:45.101 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'appConfig'
[] 2025-05-16 18:18:45.103 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai1'
[] 2025-05-16 18:18:45.104 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'louzai2'
[] 2025-05-16 18:18:45.107 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userJDBCDao'
[] 2025-05-16 18:18:45.111 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:45.111 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.url' in PropertySource 'environmentProperties' with value of type String
[] 2025-05-16 18:18:45.113 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:45.113 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.username' in PropertySource 'environmentProperties' with value of type String
[] 2025-05-16 18:18:45.115 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'configurationProperties' with value of type Integer
[] 2025-05-16 18:18:45.116 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.password' in PropertySource 'environmentProperties' with value of type String
[] 2025-05-16 18:18:45.116 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:45.116 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.datasource.driver-class-name' in PropertySource 'environmentProperties' with value of type String
[] 2025-05-16 18:18:45.151 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hanaController'
[] 2025-05-16 18:18:45.153 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'restTemplate'
[] 2025-05-16 18:18:45.240 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'helloController'
[] 2025-05-16 18:18:45.265 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'userMapper'
[] 2025-05-16 18:18:45.282 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionFactory'
[] 2025-05-16 18:18:45.284 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration'
[] 2025-05-16 18:18:45.286 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$$EnhancerBySpringCGLIB$$c6fb4805] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:45.291 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2025-05-16 18:18:45.374 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'mybatis-org.mybatis.spring.boot.autoconfigure.MybatisProperties'
[] 2025-05-16 18:18:45.374 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24aed80c'
[] 2025-05-16 18:18:45.398 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'dataSource'
[] 2025-05-16 18:18:45.399 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure'
[] 2025-05-16 18:18:45.418 [INFO] [main] [com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure] 56 - Init DruidDataSource
[] 2025-05-16 18:18:45.505 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2025-05-16 18:18:45.533 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'statFilter'
[] 2025-05-16 18:18:45.533 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'com.alibaba.druid.spring.boot.autoconfigure.stat.DruidFilterConfiguration'
[] 2025-05-16 18:18:45.631 [INFO] [main] [com.alibaba.druid.pool.DruidDataSource] 928 - {dataSource-1} inited
[] 2025-05-16 18:18:45.651 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker'
[] 2025-05-16 18:18:45.655 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2025-05-16 18:18:45.655 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializerInvoker' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24aed80c'
[] 2025-05-16 18:18:45.662 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionFactory' via factory method to bean named 'dataSource'
[] 2025-05-16 18:18:45.868 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'sqlSessionTemplate'
[] 2025-05-16 18:18:45.869 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'sqlSessionTemplate' via factory method to bean named 'sqlSessionFactory'
[] 2025-05-16 18:18:45.890 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transitGoodsInfoMapper'
[] 2025-05-16 18:18:45.893 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'pageController'
[] 2025-05-16 18:18:45.894 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'voteService'
[] 2025-05-16 18:18:45.912 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration'
[] 2025-05-16 18:18:45.915 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.websocket.servlet.WebSocketServletAutoConfiguration'
[] 2025-05-16 18:18:45.916 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.DispatcherServletAutoConfiguration'
[] 2025-05-16 18:18:45.917 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration'
[] 2025-05-16 18:18:45.917 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration$$EnhancerBySpringCGLIB$$1fc68fab] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:45.921 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2025-05-16 18:18:45.925 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.task.TaskExecutionAutoConfiguration' via constructor to bean named 'spring.task.execution-org.springframework.boot.autoconfigure.task.TaskExecutionProperties'
[] 2025-05-16 18:18:45.927 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskExecutorBuilder'
[] 2025-05-16 18:18:45.936 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.validation.ValidationAutoConfiguration'
[] 2025-05-16 18:18:45.938 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultValidator'
[] 2025-05-16 18:18:45.955 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.error.ErrorMvcAutoConfiguration$WhitelabelErrorViewConfiguration'
[] 2025-05-16 18:18:45.959 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'error'
[] 2025-05-16 18:18:45.963 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameViewResolver'
[] 2025-05-16 18:18:45.965 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'errorAttributes'
[] 2025-05-16 18:18:45.966 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'basicErrorController'
[] 2025-05-16 18:18:45.969 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'basicErrorController' via factory method to bean named 'errorAttributes'
[] 2025-05-16 18:18:45.970 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration'
[] 2025-05-16 18:18:45.971 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration$$EnhancerBySpringCGLIB$$5d551a80] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:45.971 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$FaviconConfiguration' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2025-05-16 18:18:45.971 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconHandlerMapping'
[] 2025-05-16 18:18:45.980 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'faviconRequestHandler'
[] 2025-05-16 18:18:46.004 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/**/favicon.ico] in 'faviconHandlerMapping'
[] 2025-05-16 18:18:46.008 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration'
[] 2025-05-16 18:18:46.008 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration$$EnhancerBySpringCGLIB$$ff144755] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.009 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$EnableWebMvcConfiguration' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@563f38c4'
[] 2025-05-16 18:18:46.031 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter'
[] 2025-05-16 18:18:46.033 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter$$EnhancerBySpringCGLIB$$a118123a] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.034 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.resources-org.springframework.boot.autoconfigure.web.ResourceProperties'
[] 2025-05-16 18:18:46.034 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'spring.mvc-org.springframework.boot.autoconfigure.web.servlet.WebMvcProperties'
[] 2025-05-16 18:18:46.034 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration$WebMvcAutoConfigurationAdapter' via constructor to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@563f38c4'
[] 2025-05-16 18:18:46.047 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerAdapter'
[] 2025-05-16 18:18:46.083 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcContentNegotiationManager'
[] 2025-05-16 18:18:46.104 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'messageConverters'
[] 2025-05-16 18:18:46.106 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration'
[] 2025-05-16 18:18:46.107 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$$EnhancerBySpringCGLIB$$ae5ac269] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.117 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'stringHttpMessageConverter'
[] 2025-05-16 18:18:46.118 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration'
[] 2025-05-16 18:18:46.118 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration$$EnhancerBySpringCGLIB$$e1688909] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.119 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.http.HttpMessageConvertersAutoConfiguration$StringHttpMessageConverterConfiguration' via constructor to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2025-05-16 18:18:46.141 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mappingJackson2HttpMessageConverter'
[] 2025-05-16 18:18:46.144 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration$MappingJackson2HttpMessageConverterConfiguration'
[] 2025-05-16 18:18:46.149 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapper'
[] 2025-05-16 18:18:46.149 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperConfiguration'
[] 2025-05-16 18:18:46.150 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonObjectMapperBuilder'
[] 2025-05-16 18:18:46.150 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration'
[] 2025-05-16 18:18:46.151 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration$$EnhancerBySpringCGLIB$$a207387f] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.151 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$JacksonObjectMapperBuilderConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24aed80c'
[] 2025-05-16 18:18:46.152 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'standardJacksonObjectMapperBuilderCustomizer'
[] 2025-05-16 18:18:46.152 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$Jackson2ObjectMapperBuilderCustomizerConfiguration'
[] 2025-05-16 18:18:46.153 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2025-05-16 18:18:46.158 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24aed80c'
[] 2025-05-16 18:18:46.158 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'standardJacksonObjectMapperBuilderCustomizer' via factory method to bean named 'spring.jackson-org.springframework.boot.autoconfigure.jackson.JacksonProperties'
[] 2025-05-16 18:18:46.164 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapperBuilder' via factory method to bean named 'standardJacksonObjectMapperBuilderCustomizer'
[] 2025-05-16 18:18:46.172 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'parameterNamesModule'
[] 2025-05-16 18:18:46.174 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration$ParameterNamesModuleConfiguration'
[] 2025-05-16 18:18:46.181 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jsonComponentModule'
[] 2025-05-16 18:18:46.181 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration'
[] 2025-05-16 18:18:46.194 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonObjectMapper' via factory method to bean named 'jacksonObjectMapperBuilder'
[] 2025-05-16 18:18:46.212 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mappingJackson2HttpMessageConverter' via factory method to bean named 'jacksonObjectMapper'
[] 2025-05-16 18:18:46.231 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcConversionService'
[] 2025-05-16 18:18:46.237 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcValidator'
[] 2025-05-16 18:18:46.239 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'applicationTaskExecutor'
[] 2025-05-16 18:18:46.239 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'applicationTaskExecutor' via factory method to bean named 'taskExecutorBuilder'
[] 2025-05-16 18:18:46.251 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2025-05-16 18:18:46.262 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter] 622 - ControllerAdvice beans: 0 @ModelAttribute, 0 @InitBinder, 1 RequestBodyAdvice, 1 ResponseBodyAdvice
[] 2025-05-16 18:18:46.292 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'requestMappingHandlerMapping'
[] 2025-05-16 18:18:46.313 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcResourceUrlProvider'
[] 2025-05-16 18:18:46.412 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 350 - 15 mappings in 'requestMappingHandlerMapping'
[] 2025-05-16 18:18:46.418 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcPathMatcher'
[] 2025-05-16 18:18:46.424 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUrlPathHelper'
[] 2025-05-16 18:18:46.428 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewControllerHandlerMapping'
[] 2025-05-16 18:18:46.430 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'beanNameHandlerMapping'
[] 2025-05-16 18:18:46.433 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'resourceHandlerMapping'
[] 2025-05-16 18:18:46.444 [DEBUG] [main] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 139 - Patterns [/webjars/**, /**] in 'resourceHandlerMapping'
[] 2025-05-16 18:18:46.446 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultServletHandlerMapping'
[] 2025-05-16 18:18:46.449 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcUriComponentsContributor'
[] 2025-05-16 18:18:46.452 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'httpRequestHandlerAdapter'
[] 2025-05-16 18:18:46.454 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'simpleControllerHandlerAdapter'
[] 2025-05-16 18:18:46.454 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'handlerExceptionResolver'
[] 2025-05-16 18:18:46.466 [DEBUG] [main] [org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver] 302 - ControllerAdvice beans: 0 @ExceptionHandler, 1 ResponseBodyAdvice
[] 2025-05-16 18:18:46.476 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mvcViewResolver'
[] 2025-05-16 18:18:46.484 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultViewResolver'
[] 2025-05-16 18:18:46.503 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'viewResolver'
[] 2025-05-16 18:18:46.504 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'viewResolver' via factory method to bean named 'org.springframework.beans.factory.support.DefaultListableBeanFactory@563f38c4'
[] 2025-05-16 18:18:46.506 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'thymeleafViewResolver'
[] 2025-05-16 18:18:46.509 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration'
[] 2025-05-16 18:18:46.511 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration$$EnhancerBySpringCGLIB$$27a7f5a0] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.513 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2025-05-16 18:18:46.522 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'templateEngine'
[] 2025-05-16 18:18:46.522 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration'
[] 2025-05-16 18:18:46.523 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration$$EnhancerBySpringCGLIB$$52ef3971] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.526 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'defaultTemplateResolver'
[] 2025-05-16 18:18:46.526 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration'
[] 2025-05-16 18:18:46.527 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration$$EnhancerBySpringCGLIB$$111fbec8] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.527 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2025-05-16 18:18:46.527 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$DefaultTemplateResolverConfiguration' via constructor to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24aed80c'
[] 2025-05-16 18:18:46.535 [WARN] [main] [org.thymeleaf.templatemode.TemplateMode] 150 - [THYMELEAF][main] Template Mode 'LEGACYHTML5' is deprecated. Using Template Mode 'HTML' instead.
[] 2025-05-16 18:18:46.539 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2025-05-16 18:18:46.539 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafDefaultConfiguration' via constructor to bean named 'defaultTemplateResolver'
[] 2025-05-16 18:18:46.554 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'java8TimeDialect'
[] 2025-05-16 18:18:46.555 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafJava8TimeDialect'
[] 2025-05-16 18:18:46.566 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration' via constructor to bean named 'spring.thymeleaf-org.springframework.boot.autoconfigure.thymeleaf.ThymeleafProperties'
[] 2025-05-16 18:18:46.566 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration$ThymeleafViewResolverConfiguration' via constructor to bean named 'templateEngine'
[] 2025-05-16 18:18:46.574 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'welcomePageHandlerMapping'
[] 2025-05-16 18:18:46.575 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'welcomePageHandlerMapping' via factory method to bean named 'org.springframework.boot.web.servlet.context.AnnotationConfigServletWebServerApplicationContext@24aed80c'
[] 2025-05-16 18:18:46.579 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:46.579 [DEBUG] [main] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:46.579 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 61 - Adding welcome page template: index
[] 2025-05-16 18:18:46.582 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration$HikariPoolDataSourceMetadataProviderConfiguration'
[] 2025-05-16 18:18:46.584 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'hikariPoolDataSourceMetadataProvider'
[] 2025-05-16 18:18:46.589 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.metadata.DataSourcePoolMetadataProvidersConfiguration'
[] 2025-05-16 18:18:46.590 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceInitializationConfiguration'
[] 2025-05-16 18:18:46.591 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration'
[] 2025-05-16 18:18:46.592 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisLanguageDriverAutoConfiguration'
[] 2025-05-16 18:18:46.592 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration$MapperScannerRegistrarNotFoundConfiguration'
[] 2025-05-16 18:18:46.593 [DEBUG] [main] [org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration] 261 - Not found configuration for registering mapper bean using @MapperScan, MapperFactoryBean and MapperScannerConfigurer.
[] 2025-05-16 18:18:46.593 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jmx.JmxAutoConfiguration'
[] 2025-05-16 18:18:46.595 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanExporter'
[] 2025-05-16 18:18:46.596 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'objectNamingStrategy'
[] 2025-05-16 18:18:46.598 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'mbeanExporter' via factory method to bean named 'objectNamingStrategy'
[] 2025-05-16 18:18:46.601 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'mbeanServer'
[] 2025-05-16 18:18:46.603 [DEBUG] [main] [org.springframework.jmx.support.JmxUtils] 126 - Found MBeanServer: com.sun.jmx.mbeanserver.JmxMBeanServer@6224af11
[] 2025-05-16 18:18:46.615 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration'
[] 2025-05-16 18:18:46.617 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.dao.PersistenceExceptionTranslationAutoConfiguration'
[] 2025-05-16 18:18:46.619 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.JacksonHttpMessageConvertersConfiguration'
[] 2025-05-16 18:18:46.620 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$LoggingCodecConfiguration'
[] 2025-05-16 18:18:46.621 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'loggingCodecCustomizer'
[] 2025-05-16 18:18:46.622 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'loggingCodecCustomizer' via factory method to bean named 'spring.http-org.springframework.boot.autoconfigure.http.HttpProperties'
[] 2025-05-16 18:18:46.625 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration$JacksonCodecConfiguration'
[] 2025-05-16 18:18:46.626 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jacksonCodecCustomizer'
[] 2025-05-16 18:18:46.626 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'jacksonCodecCustomizer' via factory method to bean named 'jacksonObjectMapper'
[] 2025-05-16 18:18:46.627 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.http.codec.CodecsAutoConfiguration'
[] 2025-05-16 18:18:46.627 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration'
[] 2025-05-16 18:18:46.627 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration$$EnhancerBySpringCGLIB$$d02103ee] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.628 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2025-05-16 18:18:46.629 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration' via constructor to bean named 'spring.info-org.springframework.boot.autoconfigure.info.ProjectInfoProperties'
[] 2025-05-16 18:18:46.629 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration'
[] 2025-05-16 18:18:46.629 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration$$EnhancerBySpringCGLIB$$acb22542] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.630 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2025-05-16 18:18:46.630 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'dataSource'
[] 2025-05-16 18:18:46.630 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$JdbcTemplateConfiguration' via constructor to bean named 'spring.jdbc-org.springframework.boot.autoconfigure.jdbc.JdbcProperties'
[] 2025-05-16 18:18:46.631 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'jdbcTemplate'
[] 2025-05-16 18:18:46.640 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration$NamedParameterJdbcTemplateConfiguration'
[] 2025-05-16 18:18:46.641 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'namedParameterJdbcTemplate'
[] 2025-05-16 18:18:46.642 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'namedParameterJdbcTemplate' via factory method to bean named 'jdbcTemplate'
[] 2025-05-16 18:18:46.646 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.JdbcTemplateAutoConfiguration'
[] 2025-05-16 18:18:46.647 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.task.TaskSchedulingAutoConfiguration'
[] 2025-05-16 18:18:46.647 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'taskSchedulerBuilder'
[] 2025-05-16 18:18:46.648 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2025-05-16 18:18:46.650 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'taskSchedulerBuilder' via factory method to bean named 'spring.task.scheduling-org.springframework.boot.autoconfigure.task.TaskSchedulingProperties'
[] 2025-05-16 18:18:46.652 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration$ThymeleafWebMvcConfiguration'
[] 2025-05-16 18:18:46.652 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.thymeleaf.ThymeleafAutoConfiguration'
[] 2025-05-16 18:18:46.653 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration'
[] 2025-05-16 18:18:46.654 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration$$EnhancerBySpringCGLIB$$41278f44] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.654 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration$DataSourceTransactionManagerConfiguration' via constructor to bean named 'dataSource'
[] 2025-05-16 18:18:46.655 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'platformTransactionManagerCustomizers'
[] 2025-05-16 18:18:46.655 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration'
[] 2025-05-16 18:18:46.658 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'spring.transaction-org.springframework.boot.autoconfigure.transaction.TransactionProperties'
[] 2025-05-16 18:18:46.661 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionManager'
[] 2025-05-16 18:18:46.661 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'transactionManager' via factory method to bean named 'spring.datasource-org.springframework.boot.autoconfigure.jdbc.DataSourceProperties'
[] 2025-05-16 18:18:46.664 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration'
[] 2025-05-16 18:18:46.664 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration$CglibAutoProxyConfiguration'
[] 2025-05-16 18:18:46.665 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$EnableTransactionManagementConfiguration'
[] 2025-05-16 18:18:46.665 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration'
[] 2025-05-16 18:18:46.665 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration$$EnhancerBySpringCGLIB$$92b15417] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.665 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 777 - Autowiring by type from bean name 'org.springframework.boot.autoconfigure.transaction.TransactionAutoConfiguration$TransactionTemplateConfiguration' via constructor to bean named 'transactionManager'
[] 2025-05-16 18:18:46.665 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'transactionTemplate'
[] 2025-05-16 18:18:46.669 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration'
[] 2025-05-16 18:18:46.669 [DEBUG] [main] [org.springframework.core.LocalVariableTableParameterNameDiscoverer] 108 - Cannot find '.class' file for class [class org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration$$EnhancerBySpringCGLIB$$2f3cf056] - unable to determine constructor/method parameter names
[] 2025-05-16 18:18:46.671 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'restTemplateBuilder'
[] 2025-05-16 18:18:46.677 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'org.springframework.boot.autoconfigure.web.embedded.EmbeddedWebServerFactoryCustomizerAutoConfiguration'
[] 2025-05-16 18:18:46.677 [DEBUG] [main] [org.springframework.beans.factory.support.DefaultListableBeanFactory] 213 - Creating shared instance of singleton bean 'multipartResolver'
[] 2025-05-16 18:18:46.689 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 433 - Registering beans for JMX exposure on startup
[] 2025-05-16 18:18:46.690 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 540 - Autodetecting user-defined JMX MBeans
[] 2025-05-16 18:18:46.691 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'statFilter' has been autodetected for JMX exposure
[] 2025-05-16 18:18:46.692 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 895 - Bean with name 'dataSource' has been autodetected for JMX exposure
[] 2025-05-16 18:18:46.701 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'dataSource': registering with JMX server as MBean [com.alibaba.druid.spring.boot.autoconfigure:name=dataSource,type=DruidDataSourceWrapper]
[] 2025-05-16 18:18:46.702 [DEBUG] [main] [org.springframework.jmx.export.annotation.AnnotationMBeanExporter] 668 - Located MBean 'statFilter': registering with JMX server as MBean [com.alibaba.druid.filter.stat:name=statFilter,type=StatFilter]
[] 2025-05-16 18:18:46.778 [DEBUG] [main] [org.springframework.boot.autoconfigure.logging.ConditionEvaluationReportLoggingListener] 132 - 


============================
CONDITIONS EVALUATION REPORT
============================


Positive matches:
-----------------

   CodecsAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.codec.CodecConfigurer' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   CodecsAutoConfiguration.JacksonCodecConfiguration#jacksonCodecCustomizer matched:
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   DataSourceAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   DataSourceConfiguration.Generic matched:
      - @ConditionalOnProperty (spring.datasource.type) matched (OnPropertyCondition)

   DataSourceJmxConfiguration matched:
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   DataSourceJmxConfiguration.Hikari matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.HikariPoolDataSourceMetadataProviderConfiguration matched:
      - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.springframework.jdbc.core.JdbcTemplate', 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration matched:
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   DataSourceTransactionManagerAutoConfiguration.DataSourceTransactionManagerConfiguration#transactionManager matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DispatcherServletAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - Default DispatcherServlet did not find dispatcher servlet beans (DispatcherServletAutoConfiguration.DefaultDispatcherServletCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRegistration' (OnClassCondition)
      - DispatcherServlet Registration did not find servlet registration bean (DispatcherServletAutoConfiguration.DispatcherServletRegistrationCondition)

   DispatcherServletAutoConfiguration.DispatcherServletRegistrationConfiguration#dispatcherServletRegistration matched:
      - @ConditionalOnBean (names: dispatcherServlet; types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet' (OnBeanCondition)

   DruidDataSourceAutoConfigure matched:
      - @ConditionalOnClass found required class 'com.alibaba.druid.pool.DruidDataSource' (OnClassCondition)

   DruidDataSourceAutoConfigure#dataSource matched:
      - @ConditionalOnMissingBean (types: javax.sql.DataSource; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidFilterConfiguration#statFilter matched:
      - @ConditionalOnProperty (spring.datasource.druid.filter.stat.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: com.alibaba.druid.filter.stat.StatFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   DruidStatViewServletConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.stat-view-servlet.enabled=true) matched (OnPropertyCondition)

   DruidWebStatFilterConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.datasource.druid.web-stat-filter.enabled=true) matched (OnPropertyCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration matched:
      - @ConditionalOnWebApplication (required) found 'session' scope (OnWebApplicationCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.TomcatWebServerFactoryCustomizerConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)

   ErrorMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ErrorMvcAutoConfiguration#basicErrorController matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorController; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration#errorAttributes matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.error.ErrorAttributes; SearchStrategy: current) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.DefaultErrorViewResolverConfiguration#conventionErrorViewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.DispatcherServlet; SearchStrategy: all) found bean 'dispatcherServlet'; @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.web.servlet.error.DefaultErrorViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration matched:
      - @ConditionalOnProperty (server.error.whitelabel.enabled) matched (OnPropertyCondition)
      - ErrorTemplate Missing did not find error template view (ErrorMvcAutoConfiguration.ErrorTemplateMissingCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#beanNameViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ErrorMvcAutoConfiguration.WhitelabelErrorViewConfiguration#defaultErrorView matched:
      - @ConditionalOnMissingBean (names: error; SearchStrategy: all) did not find any beans (OnBeanCondition)

   GenericCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.GenericCacheConfiguration automatic cache type (CacheCondition)

   HttpEncodingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.filter.CharacterEncodingFilter' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.http.encoding.enabled) matched (OnPropertyCondition)

   HttpEncodingAutoConfiguration#characterEncodingFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.CharacterEncodingFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.HttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration#messageConverters matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.http.HttpMessageConverters; SearchStrategy: all) did not find any beans (OnBeanCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.StringHttpMessageConverter' (OnClassCondition)

   HttpMessageConvertersAutoConfiguration.StringHttpMessageConverterConfiguration#stringHttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.StringHttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)

   JacksonAutoConfiguration.Jackson2ObjectMapperBuilderCustomizerConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperBuilderConfiguration#jacksonObjectMapperBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.Jackson2ObjectMapperBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.http.converter.json.Jackson2ObjectMapperBuilder' (OnClassCondition)

   JacksonAutoConfiguration.JacksonObjectMapperConfiguration#jacksonObjectMapper matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.module.paramnames.ParameterNamesModule' (OnClassCondition)

   JacksonAutoConfiguration.ParameterNamesModuleConfiguration#parameterNamesModule matched:
      - @ConditionalOnMissingBean (types: com.fasterxml.jackson.module.paramnames.ParameterNamesModule; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration matched:
      - @ConditionalOnClass found required class 'com.fasterxml.jackson.databind.ObjectMapper' (OnClassCondition)
      - @ConditionalOnProperty (spring.http.converters.preferred-json-mapper=jackson) matched (OnPropertyCondition)
      - @ConditionalOnBean (types: com.fasterxml.jackson.databind.ObjectMapper; SearchStrategy: all) found bean 'jacksonObjectMapper' (OnBeanCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2HttpMessageConverterConfiguration#mappingJackson2HttpMessageConverter matched:
      - @ConditionalOnMissingBean (types: org.springframework.http.converter.json.MappingJackson2HttpMessageConverter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.core.JdbcTemplate' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   JdbcTemplateAutoConfiguration.JdbcTemplateConfiguration#jdbcTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.jdbc.core.JdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JdbcTemplateAutoConfiguration.NamedParameterJdbcTemplateConfiguration#namedParameterJdbcTemplate matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.jdbc.core.JdbcTemplate; SearchStrategy: all) found a primary bean from beans 'jdbcTemplate'; @ConditionalOnMissingBean (types: org.springframework.jdbc.core.namedparam.NamedParameterJdbcOperations; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.jmx.export.MBeanExporter' (OnClassCondition)
      - @ConditionalOnProperty (spring.jmx.enabled=true) matched (OnPropertyCondition)

   JmxAutoConfiguration#mbeanExporter matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.MBeanExporter; SearchStrategy: current) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#mbeanServer matched:
      - @ConditionalOnMissingBean (types: javax.management.MBeanServer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   JmxAutoConfiguration#objectNamingStrategy matched:
      - @ConditionalOnMissingBean (types: org.springframework.jmx.export.naming.ObjectNamingStrategy; SearchStrategy: current) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.multipart.support.StandardServletMultipartResolver', 'javax.servlet.MultipartConfigElement' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.servlet.multipart.enabled) matched (OnPropertyCondition)

   MultipartAutoConfiguration#multipartConfigElement matched:
      - @ConditionalOnMissingBean (types: javax.servlet.MultipartConfigElement,org.springframework.web.multipart.commons.CommonsMultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MultipartAutoConfiguration#multipartResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.ibatis.session.SqlSessionFactory', 'org.mybatis.spring.SqlSessionFactoryBean' (OnClassCondition)
      - @ConditionalOnSingleCandidate (types: javax.sql.DataSource; SearchStrategy: all) found a primary bean from beans 'dataSource' (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionFactory matched:
      - @ConditionalOnMissingBean (types: org.apache.ibatis.session.SqlSessionFactory; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration#sqlSessionTemplate matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.SqlSessionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisAutoConfiguration.MapperScannerRegistrarNotFoundConfiguration matched:
      - @ConditionalOnMissingBean (types: org.mybatis.spring.mapper.MapperFactoryBean,org.mybatis.spring.mapper.MapperScannerConfigurer; SearchStrategy: all) did not find any beans (OnBeanCondition)

   MybatisLanguageDriverAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.apache.ibatis.scripting.LanguageDriver' (OnClassCondition)

   NoOpCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.NoOpCacheConfiguration automatic cache type (CacheCondition)

   PersistenceExceptionTranslationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor' (OnClassCondition)

   PersistenceExceptionTranslationAutoConfiguration#persistenceExceptionTranslationPostProcessor matched:
      - @ConditionalOnProperty (spring.dao.exceptiontranslation.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.dao.annotation.PersistenceExceptionTranslationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   PropertyPlaceholderAutoConfiguration#propertySourcesPlaceholderConfigurer matched:
      - @ConditionalOnMissingBean (types: org.springframework.context.support.PropertySourcesPlaceholderConfigurer; SearchStrategy: current) did not find any beans (OnBeanCondition)

   RestTemplateAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.web.client.RestTemplate' (OnClassCondition)

   RestTemplateAutoConfiguration#restTemplateBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.client.RestTemplateBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ServletWebServerFactoryAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.servlet.ServletRequest' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   ServletWebServerFactoryAutoConfiguration#tomcatServletWebServerFactoryCustomizer matched:
      - @ConditionalOnClass found required class 'org.apache.catalina.startup.Tomcat' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedTomcat matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.apache.catalina.startup.Tomcat', 'org.apache.coyote.UpgradeProtocol' (OnClassCondition)
      - @ConditionalOnMissingBean (types: org.springframework.boot.web.servlet.server.ServletWebServerFactory; SearchStrategy: current) did not find any beans (OnBeanCondition)

   SimpleCacheConfiguration matched:
      - Cache org.springframework.boot.autoconfigure.cache.SimpleCacheConfiguration automatic cache type (CacheCondition)

   TaskExecutionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor' (OnClassCondition)

   TaskExecutionAutoConfiguration#applicationTaskExecutor matched:
      - @ConditionalOnMissingBean (types: java.util.concurrent.Executor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskExecutionAutoConfiguration#taskExecutorBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskExecutorBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TaskSchedulingAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskSchedulerBuilder matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.task.TaskSchedulerBuilder; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.thymeleaf.templatemode.TemplateMode' (OnClassCondition)

   ThymeleafAutoConfiguration.DefaultTemplateResolverConfiguration matched:
      - @ConditionalOnMissingBean (names: defaultTemplateResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafDefaultConfiguration#templateEngine matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.spring5.SpringTemplateEngine; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect matched:
      - @ConditionalOnClass found required class 'org.thymeleaf.extras.java8time.dialect.Java8TimeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafJava8TimeDialect#java8TimeDialect matched:
      - @ConditionalOnMissingBean (types: org.thymeleaf.extras.java8time.dialect.Java8TimeDialect; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration matched:
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnProperty (spring.thymeleaf.enabled) matched (OnPropertyCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration.ThymeleafViewResolverConfiguration#thymeleafViewResolver matched:
      - @ConditionalOnMissingBean (names: thymeleafViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration matched:
      - @ConditionalOnClass found required class 'org.springframework.transaction.PlatformTransactionManager' (OnClassCondition)

   TransactionAutoConfiguration#platformTransactionManagerCustomizers matched:
      - @ConditionalOnMissingBean (types: org.springframework.boot.autoconfigure.transaction.TransactionManagerCustomizers; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration matched:
      - @ConditionalOnBean (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found bean 'transactionManager'; @ConditionalOnMissingBean (types: org.springframework.transaction.annotation.AbstractTransactionManagementConfiguration; SearchStrategy: all) did not find any beans (OnBeanCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.CglibAutoProxyConfiguration matched:
      - @ConditionalOnProperty (spring.aop.proxy-target-class=true) matched (OnPropertyCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration matched:
      - @ConditionalOnSingleCandidate (types: org.springframework.transaction.PlatformTransactionManager; SearchStrategy: all) found a primary bean from beans 'transactionManager' (OnBeanCondition)

   TransactionAutoConfiguration.TransactionTemplateConfiguration#transactionTemplate matched:
      - @ConditionalOnMissingBean (types: org.springframework.transaction.support.TransactionTemplate; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration matched:
      - @ConditionalOnClass found required class 'javax.validation.executable.ExecutableValidator' (OnClassCondition)
      - @ConditionalOnResource found location classpath:META-INF/services/javax.validation.spi.ValidationProvider (OnResourceCondition)

   ValidationAutoConfiguration#defaultValidator matched:
      - @ConditionalOnMissingBean (types: javax.validation.Validator; SearchStrategy: all) did not find any beans (OnBeanCondition)

   ValidationAutoConfiguration#methodValidationPostProcessor matched:
      - @ConditionalOnMissingBean (types: org.springframework.validation.beanvalidation.MethodValidationPostProcessor; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'org.springframework.web.servlet.DispatcherServlet', 'org.springframework.web.servlet.config.annotation.WebMvcConfigurer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#formContentFilter matched:
      - @ConditionalOnProperty (spring.mvc.formcontent.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.FormContentFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration#hiddenHttpMethodFilter matched:
      - @ConditionalOnProperty (spring.mvc.hiddenmethod.filter.enabled) matched (OnPropertyCondition)
      - @ConditionalOnMissingBean (types: org.springframework.web.filter.HiddenHttpMethodFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#defaultViewResolver matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.InternalResourceViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#requestContextFilter matched:
      - @ConditionalOnMissingBean (types: org.springframework.web.context.request.RequestContextListener,org.springframework.web.filter.RequestContextFilter; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#viewResolver matched:
      - @ConditionalOnBean (types: org.springframework.web.servlet.ViewResolver; SearchStrategy: all) found beans 'defaultViewResolver', 'beanNameViewResolver', 'mvcViewResolver'; @ConditionalOnMissingBean (names: viewResolver; types: org.springframework.web.servlet.view.ContentNegotiatingViewResolver; SearchStrategy: all) did not find any beans (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter.FaviconConfiguration matched:
      - @ConditionalOnProperty (spring.mvc.favicon.enabled) matched (OnPropertyCondition)

   WebSocketServletAutoConfiguration matched:
      - @ConditionalOnClass found required classes 'javax.servlet.Servlet', 'javax.websocket.server.ServerContainer' (OnClassCondition)
      - found 'session' scope (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration matched:
      - @ConditionalOnClass found required classes 'org.apache.catalina.startup.Tomcat', 'org.apache.tomcat.websocket.server.WsSci' (OnClassCondition)

   WebSocketServletAutoConfiguration.TomcatWebSocketConfiguration#websocketServletWebServerCustomizer matched:
      - @ConditionalOnMissingBean (names: websocketServletWebServerCustomizer; SearchStrategy: all) did not find any beans (OnBeanCondition)


Negative matches:
-----------------

   ActiveMQAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   AopAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.aspectj.lang.annotation.Aspect' (OnClassCondition)

   ArtemisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.ConnectionFactory' (OnClassCondition)

   BatchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.batch.core.launch.JobLauncher' (OnClassCondition)

   CacheAutoConfiguration:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.cache.interceptor.CacheAspectSupport; SearchStrategy: all) did not find any beans of type org.springframework.cache.interceptor.CacheAspectSupport (OnBeanCondition)
      Matched:
         - @ConditionalOnClass found required class 'org.springframework.cache.CacheManager' (OnClassCondition)

   CacheAutoConfiguration.CacheManagerJpaDependencyConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean' (OnClassCondition)
         - Ancestor org.springframework.boot.autoconfigure.cache.CacheAutoConfiguration did not match (ConditionEvaluationReport.AncestorsMatchedCondition)

   CaffeineCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.github.benmanes.caffeine.cache.Caffeine', 'org.springframework.cache.caffeine.CaffeineCacheManager' (OnClassCondition)

   CassandraAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Cluster' (OnClassCondition)

   CassandraReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.cassandra.ReactiveSession' (OnClassCondition)

   CassandraRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.datastax.driver.core.Session' (OnClassCondition)

   ClientHttpConnectorAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   CloudServiceConnectorsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.cloud.config.java.CloudScanConfiguration' (OnClassCondition)

   CouchbaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Cluster' (OnClassCondition)

   CouchbaseCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.couchbase.client.java.Bucket', 'com.couchbase.client.spring.cache.CouchbaseCacheManager' (OnClassCondition)

   CouchbaseDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   CouchbaseRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.couchbase.client.java.Bucket' (OnClassCondition)

   DataSourceAutoConfiguration.EmbeddedDatabaseConfiguration:
      Did not match:
         - EmbeddedDataSource found supported pooled data source (DataSourceAutoConfiguration.EmbeddedDatabaseCondition)

   DataSourceAutoConfiguration.PooledDataSourceConfiguration:
      Did not match:
         - @ConditionalOnMissingBean (types: javax.sql.DataSource,javax.sql.XADataSource; SearchStrategy: all) found beans of type 'javax.sql.DataSource' dataSource (OnBeanCondition)
      Matched:
         - AnyNestedCondition 2 matched 0 did not; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.PooledDataSourceAvailable PooledDataSource found supported DataSource; NestedCondition on DataSourceAutoConfiguration.PooledDataSourceCondition.ExplicitType @ConditionalOnProperty (spring.datasource.type) matched (DataSourceAutoConfiguration.PooledDataSourceCondition)

   DataSourceConfiguration.Dbcp2:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourceConfiguration.Hikari:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.type=com.zaxxer.hikari.HikariDataSource) found different value in property 'spring.datasource.type' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required class 'com.zaxxer.hikari.HikariDataSource' (OnClassCondition)

   DataSourceConfiguration.Tomcat:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DataSourceJmxConfiguration.TomcatDataSourceJmxConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSourceProxy' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.CommonsDbcp2PoolDataSourceMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.commons.dbcp2.BasicDataSource' (OnClassCondition)

   DataSourcePoolMetadataProvidersConfiguration.TomcatDataSourcePoolMetadataProviderConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.tomcat.jdbc.pool.DataSource' (OnClassCondition)

   DispatcherServletAutoConfiguration.DispatcherServletConfiguration#multipartResolver:
      Did not match:
         - @ConditionalOnBean (types: org.springframework.web.multipart.MultipartResolver; SearchStrategy: all) did not find any beans of type org.springframework.web.multipart.MultipartResolver (OnBeanCondition)

   DruidFilterConfiguration#commonsLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.commons-log.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#configFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.config.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#encodingConvertFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.encoding.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4j2Filter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j2.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#log4jFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.log4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#slf4jLogFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.slf4j.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallConfig:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidFilterConfiguration#wallFilter:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.filter.wall.enabled) did not find property 'enabled' (OnPropertyCondition)

   DruidSpringAopConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.druid.aop-patterns) did not find property 'spring.datasource.druid.aop-patterns' (OnPropertyCondition)

   EhCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'net.sf.ehcache.Cache', 'org.springframework.cache.ehcache.EhCacheCacheManager' (OnClassCondition)

   ElasticsearchAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   ElasticsearchRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.Client' (OnClassCondition)

   EmbeddedLdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.unboundid.ldap.listener.InMemoryDirectoryServer' (OnClassCondition)

   EmbeddedMongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.JettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.NettyWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.netty.http.server.HttpServer' (OnClassCondition)

   EmbeddedWebServerFactoryCustomizerAutoConfiguration.UndertowWebServerFactoryCustomizerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   ErrorWebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   FlywayAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.flywaydb.core.Flyway' (OnClassCondition)

   FreeMarkerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'freemarker.template.Configuration' (OnClassCondition)

   GroovyTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'groovy.text.markup.MarkupTemplateEngine' (OnClassCondition)

   GsonAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   GsonHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.google.gson.Gson' (OnClassCondition)

   H2ConsoleAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.h2.server.web.WebServlet' (OnClassCondition)

   HazelcastAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HazelcastCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'com.hazelcast.core.HazelcastInstance', 'com.hazelcast.spring.cache.HazelcastCacheManager' (OnClassCondition)

   HazelcastJpaDependencyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.hazelcast.core.HazelcastInstance' (OnClassCondition)

   HibernateJpaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.persistence.EntityManager' (OnClassCondition)

   HttpHandlerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.DispatcherHandler' (OnClassCondition)

   HypermediaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.hateoas.Resource' (OnClassCondition)

   InfinispanCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.infinispan.spring.provider.SpringEmbeddedCacheManager' (OnClassCondition)

   InfluxDbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.influxdb.InfluxDB' (OnClassCondition)

   IntegrationAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.integration.config.EnableIntegration' (OnClassCondition)

   JCacheCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'javax.cache.Caching', 'org.springframework.cache.jcache.JCacheCacheManager' (OnClassCondition)

   JacksonAutoConfiguration.JodaDateTimeJacksonConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.joda.time.DateTime', 'com.fasterxml.jackson.datatype.joda.ser.DateTimeSerializer', 'com.fasterxml.jackson.datatype.joda.cfg.JacksonJodaDateFormat' (OnClassCondition)

   JacksonHttpMessageConvertersConfiguration.MappingJackson2XmlHttpMessageConverterConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.fasterxml.jackson.dataformat.xml.XmlMapper' (OnClassCondition)

   JdbcRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jdbc.repository.config.JdbcConfiguration' (OnClassCondition)

   JerseyAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.glassfish.jersey.server.spring.SpringComponentProvider' (OnClassCondition)

   JestAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.searchbox.client.JestClient' (OnClassCondition)

   JmsAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.jms.Message' (OnClassCondition)

   JndiConnectionFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.jms.core.JmsTemplate' (OnClassCondition)

   JndiDataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.datasource.jndi-name) did not find property 'jndi-name' (OnPropertyCondition)
      Matched:
         - @ConditionalOnClass found required classes 'javax.sql.DataSource', 'org.springframework.jdbc.datasource.embedded.EmbeddedDatabaseType' (OnClassCondition)

   JooqAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.jooq.DSLContext' (OnClassCondition)

   JpaRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.jpa.repository.JpaRepository' (OnClassCondition)

   JsonbAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JsonbHttpMessageConvertersConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.json.bind.Jsonb' (OnClassCondition)

   JtaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.Transaction' (OnClassCondition)

   KafkaAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.kafka.core.KafkaTemplate' (OnClassCondition)

   LdapAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ldap.core.ContextSource' (OnClassCondition)

   LdapRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.ldap.repository.LdapRepository' (OnClassCondition)

   LiquibaseAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'liquibase.change.DatabaseChange' (OnClassCondition)

   MailSenderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.mail.internet.MimeMessage' (OnClassCondition)

   MailSenderValidatorAutoConfiguration:
      Did not match:
         - @ConditionalOnSingleCandidate did not find required type 'org.springframework.mail.javamail.JavaMailSenderImpl' (OnBeanCondition)

   MessageSourceAutoConfiguration:
      Did not match:
         - ResourceBundle did not find bundle with basename messages (MessageSourceAutoConfiguration.ResourceBundleCondition)

   MongoAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MongoDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.client.MongoClient' (OnClassCondition)

   MongoReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoReactiveRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.reactivestreams.client.MongoClient' (OnClassCondition)

   MongoRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.mongodb.MongoClient' (OnClassCondition)

   MustacheAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.samskivert.mustache.Mustache' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.FreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver', 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriverConfig' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyFreeMarkerConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.freemarker.FreeMarkerLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.LegacyVelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.velocity.Driver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.ThymeleafConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.mybatis.scripting.thymeleaf.ThymeleafLanguageDriver' (OnClassCondition)

   MybatisLanguageDriverAutoConfiguration.VelocityConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.mybatis.scripting.velocity.VelocityLanguageDriver', 'org.mybatis.scripting.velocity.VelocityLanguageDriverConfig' (OnClassCondition)

   Neo4jDataAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.SessionFactory' (OnClassCondition)

   Neo4jRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.neo4j.ogm.session.Neo4jSession' (OnClassCondition)

   OAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.configuration.EnableWebSecurity' (OnClassCondition)

   OAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.oauth2.jwt.JwtDecoder' (OnClassCondition)

   ProjectInfoAutoConfiguration#buildProperties:
      Did not match:
         - @ConditionalOnResource did not find resource '${spring.info.build.location:classpath:META-INF/build-info.properties}' (OnResourceCondition)

   ProjectInfoAutoConfiguration#gitProperties:
      Did not match:
         - GitResource did not find git info at classpath:git.properties (ProjectInfoAutoConfiguration.GitResourceAvailableCondition)

   QuartzAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.quartz.Scheduler' (OnClassCondition)

   RabbitAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.rabbitmq.client.Channel' (OnClassCondition)

   ReactiveOAuth2ClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveOAuth2ResourceServerAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity' (OnClassCondition)

   ReactiveSecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   ReactiveUserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.ReactiveAuthenticationManager' (OnClassCondition)

   ReactiveWebServerFactoryAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   ReactorCoreAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.core.RedisOperations' (OnClassCondition)

   RedisCacheConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.connection.RedisConnectionFactory' (OnClassCondition)

   RedisReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'reactor.core.publisher.Flux' (OnClassCondition)

   RedisRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.redis.repository.configuration.EnableRedisRepositories' (OnClassCondition)

   RepositoryRestMvcAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.rest.webmvc.config.RepositoryRestMvcConfiguration' (OnClassCondition)

   RestClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.elasticsearch.client.RestClient' (OnClassCondition)

   SecurityAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.DefaultAuthenticationEventPublisher' (OnClassCondition)

   SecurityFilterAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.config.http.SessionCreationPolicy' (OnClassCondition)

   SecurityRequestMatcherProviderAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.web.util.matcher.RequestMatcher' (OnClassCondition)

   SendGridAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.sendgrid.SendGrid' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedJetty:
      Did not match:
         - @ConditionalOnClass did not find required classes 'org.eclipse.jetty.server.Server', 'org.eclipse.jetty.util.Loader', 'org.eclipse.jetty.webapp.WebAppContext' (OnClassCondition)

   ServletWebServerFactoryConfiguration.EmbeddedUndertow:
      Did not match:
         - @ConditionalOnClass did not find required classes 'io.undertow.Undertow', 'org.xnio.SslClientAuthMode' (OnClassCondition)

   SessionAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.session.Session' (OnClassCondition)

   SolrAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.impl.CloudSolrClient' (OnClassCondition)

   SolrRepositoriesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.apache.solr.client.solrj.SolrClient' (OnClassCondition)

   SpringApplicationAdminJmxAutoConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.application.admin.enabled=true) did not find property 'enabled' (OnPropertyCondition)

   SpringDataWebAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.data.web.PageableHandlerMethodArgumentResolver' (OnClassCondition)

   TaskSchedulingAutoConfiguration#taskScheduler:
      Did not match:
         - @ConditionalOnBean (names: org.springframework.context.annotation.internalScheduledAnnotationProcessor; SearchStrategy: all) did not find any beans named org.springframework.context.annotation.internalScheduledAnnotationProcessor (OnBeanCondition)

   ThymeleafAutoConfiguration.DataAttributeDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'com.github.mxab.thymeleaf.extras.dataattribute.dialect.DataAttributeDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafReactiveConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafSecurityDialectConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.thymeleaf.extras.springsecurity5.dialect.SpringSecurityDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebFluxConfiguration:
      Did not match:
         - did not find reactive web application classes (OnWebApplicationCondition)

   ThymeleafAutoConfiguration.ThymeleafWebLayoutConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'nz.net.ultraq.thymeleaf.LayoutDialect' (OnClassCondition)

   ThymeleafAutoConfiguration.ThymeleafWebMvcConfiguration#resourceUrlEncodingFilter:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   TransactionAutoConfiguration.EnableTransactionManagementConfiguration.JdkDynamicAutoProxyConfiguration:
      Did not match:
         - @ConditionalOnProperty (spring.aop.proxy-target-class=false) did not find property 'proxy-target-class' (OnPropertyCondition)

   UserDetailsServiceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.security.authentication.AuthenticationManager' (OnClassCondition)

   WebClientAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.function.client.WebClient' (OnClassCondition)

   WebFluxAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.reactive.config.WebFluxConfigurer' (OnClassCondition)

   WebMvcAutoConfiguration.ResourceChainCustomizerConfiguration:
      Did not match:
         - @ConditionalOnEnabledResourceChain did not find class org.webjars.WebJarAssetLocator (OnEnabledResourceChainCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#beanNameViewResolver:
      Did not match:
         - @ConditionalOnMissingBean (types: org.springframework.web.servlet.view.BeanNameViewResolver; SearchStrategy: all) found beans of type 'org.springframework.web.servlet.view.BeanNameViewResolver' beanNameViewResolver (OnBeanCondition)

   WebMvcAutoConfiguration.WebMvcAutoConfigurationAdapter#localeResolver:
      Did not match:
         - @ConditionalOnProperty (spring.mvc.locale) did not find property 'locale' (OnPropertyCondition)

   WebServiceTemplateAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.oxm.Marshaller' (OnClassCondition)

   WebServicesAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.ws.transport.http.MessageDispatcherServlet' (OnClassCondition)

   WebSocketMessagingAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer' (OnClassCondition)

   WebSocketReactiveAutoConfiguration:
      Did not match:
         - @ConditionalOnWebApplication did not find reactive web application classes (OnWebApplicationCondition)

   WebSocketServletAutoConfiguration.JettyWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'org.eclipse.jetty.websocket.jsr356.server.deploy.WebSocketServerContainerInitializer' (OnClassCondition)

   WebSocketServletAutoConfiguration.UndertowWebSocketConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'io.undertow.websockets.jsr.Bootstrap' (OnClassCondition)

   XADataSourceAutoConfiguration:
      Did not match:
         - @ConditionalOnClass did not find required class 'javax.transaction.TransactionManager' (OnClassCondition)


Exclusions:
-----------

    None


Unconditional classes:
----------------------

    org.springframework.boot.autoconfigure.context.ConfigurationPropertiesAutoConfiguration

    org.springframework.boot.autoconfigure.context.PropertyPlaceholderAutoConfiguration

    org.springframework.boot.autoconfigure.info.ProjectInfoAutoConfiguration



[] 2025-05-16 18:18:46.782 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2025-05-16 18:18:46.807 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 204 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2025-05-16 18:18:46.810 [INFO] [main] [com.tomas.mybaties3.MyBatisApplication] 59 - Started MyBatisApplication in 4.659 seconds (JVM running for 5.049)
[] 2025-05-16 18:18:50.047 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2025-05-16 18:18:50.049 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2025-05-16 18:18:50.049 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 525 - Detected StandardServletMultipartResolver
[] 2025-05-16 18:18:50.077 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 541 - enableLoggingRequestDetails='false': request parameters and headers will be masked to prevent unsafe logging of potentially sensitive data
[] 2025-05-16 18:18:50.078 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 29 ms
[] 2025-05-16 18:18:50.095 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 90 - GET "/hana/getOne", parameters={}
[] 2025-05-16 18:18:50.109 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.handler.SimpleUrlHandlerMapping] 420 - Mapped to ResourceHttpRequestHandler ["classpath:/META-INF/resources/", "classpath:/resources/", "classpath:/static/", "classpath:/public/", "/"]
[] 2025-05-16 18:18:50.112 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.resource.ResourceHttpRequestHandler] 453 - Resource not found
[] 2025-05-16 18:18:50.113 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 1130 - Completed 404 NOT_FOUND
[] 2025-05-16 18:18:50.121 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 90 - "ERROR" dispatch for GET "/error", parameters={}
[] 2025-05-16 18:18:50.134 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 420 - Mapped to public org.springframework.web.servlet.ModelAndView org.springframework.boot.autoconfigure.web.servlet.error.BasicErrorController.errorHtml(javax.servlet.http.HttpServletRequest,javax.servlet.http.HttpServletResponse)
[] 2025-05-16 18:18:50.153 [DEBUG] [http-nio-8081-exec-1] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:50.154 [DEBUG] [http-nio-8081-exec-1] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:50.155 [DEBUG] [http-nio-8081-exec-1] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.prefix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:50.155 [DEBUG] [http-nio-8081-exec-1] [org.springframework.core.env.PropertySourcesPropertyResolver] 115 - Found key 'spring.thymeleaf.suffix' in PropertySource 'configurationProperties' with value of type String
[] 2025-05-16 18:18:50.180 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.view.ContentNegotiatingViewResolver] 347 - Selected 'text/html' given [text/html, text/html;q=0.8]
[] 2025-05-16 18:18:50.192 [DEBUG] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 1126 - Exiting from "ERROR" dispatch, status 404
[] 2025-05-16 18:19:14.938 [DEBUG] [http-nio-8081-exec-2] [org.springframework.web.servlet.DispatcherServlet] 90 - GET "/hana/getDictOptions", parameters={}
[] 2025-05-16 18:19:14.952 [DEBUG] [http-nio-8081-exec-2] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 420 - Mapped to public java.lang.String com.tomas.mybaties3.rest.HanaController.updateOne(java.lang.String,javax.servlet.http.HttpServletRequest)
[] 2025-05-16 18:19:14.985 [DEBUG] [http-nio-8081-exec-2] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 268 - Using 'text/html', given [text/html, application/xhtml+xml, image/avif, image/webp, image/apng, application/xml;q=0.9, application/signed-exchange;v=b3;q=0.7, */*;q=0.8] and supported [text/plain, */*, text/plain, */*, application/json, application/*+json, application/json, application/*+json]
[] 2025-05-16 18:19:14.986 [DEBUG] [http-nio-8081-exec-2] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 90 - Writing ["GET"]
[] 2025-05-16 18:19:14.990 [DEBUG] [http-nio-8081-exec-2] [org.springframework.web.servlet.DispatcherServlet] 1130 - Completed 200 OK
[] 2025-05-16 18:25:43.130 [DEBUG] [http-nio-8081-exec-5] [org.springframework.web.servlet.DispatcherServlet] 90 - POST "/hana/getDictOptions", parameters={}
[] 2025-05-16 18:25:43.142 [DEBUG] [http-nio-8081-exec-5] [org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping] 420 - Mapped to public java.lang.String com.tomas.mybaties3.rest.HanaController.updateOne(java.lang.String,javax.servlet.http.HttpServletRequest)
[] 2025-05-16 18:25:43.158 [DEBUG] [http-nio-8081-exec-5] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 268 - Using 'text/plain', given [*/*] and supported [text/plain, */*, text/plain, */*, application/json, application/*+json, application/json, application/*+json]
[] 2025-05-16 18:25:43.158 [DEBUG] [http-nio-8081-exec-5] [org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor] 90 - Writing ["POST"]
[] 2025-05-16 18:25:43.162 [DEBUG] [http-nio-8081-exec-5] [org.springframework.web.servlet.DispatcherServlet] 1130 - Completed 200 OK
