[] 2024-05-11 00:07:49.593 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 00:07:53.396 [WARN] [http-nio-8081-exec-9] [org.springframework.web.multipart.support.StandardServletMultipartResolver] 104 - Failed to perform cleanup of multipart items
java.lang.NullPointerException: null
	at org.apache.catalina.connector.Request.parseParts(Request.java:2803)
	at org.apache.catalina.connector.Request.getParts(Request.java:2780)
	at org.apache.catalina.connector.RequestFacade.getParts(RequestFacade.java:1098)
	at javax.servlet.http.HttpServletRequestWrapper.getParts(HttpServletRequestWrapper.java:356)
	at org.springframework.web.multipart.support.StandardServletMultipartResolver.cleanupMultipart(StandardServletMultipartResolver.java:97)
	at org.springframework.web.servlet.DispatcherServlet.cleanupMultipart(DispatcherServlet.java:1217)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1075)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-11 00:08:07.219 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 56568 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 00:08:07.226 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 00:08:09.261 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 00:08:09.279 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 00:08:09.294 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 00:08:09.295 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 00:08:09.436 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 00:08:09.436 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 2006 ms
[] 2024-05-11 00:08:09.883 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 00:08:10.187 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 00:08:10.386 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 00:08:10.424 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 00:08:10.430 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 5.143 seconds (JVM running for 7.127)
[] 2024-05-11 00:08:38.731 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 00:08:50.358 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 56597 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 00:08:50.364 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 00:08:51.596 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 00:08:51.623 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 00:08:51.638 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 00:08:51.639 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 00:08:51.761 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 00:08:51.762 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1302 ms
[] 2024-05-11 00:08:51.953 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 00:08:52.045 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 00:08:52.149 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 00:08:52.177 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 00:08:52.180 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 2.852 seconds (JVM running for 3.531)
[] 2024-05-11 00:09:20.137 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-11 00:09:20.173 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-11 00:09:20.189 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 14 ms
[] 2024-05-11 02:19:49.356 [ERROR] [http-nio-8081-exec-7] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:72)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-11 02:19:59.719 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:20:16.379 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 58749 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 02:20:16.398 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 02:20:22.925 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 02:20:22.954 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:20:22.969 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 02:20:22.969 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 02:20:23.121 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 02:20:23.121 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 6473 ms
[] 2024-05-11 02:20:23.415 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:20:23.578 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 02:20:23.746 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:20:23.789 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 02:20:23.794 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 8.336 seconds (JVM running for 9.533)
[] 2024-05-11 02:20:24.837 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-11 02:20:24.837 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-11 02:20:24.845 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 8 ms
[] 2024-05-11 02:20:27.292 [ERROR] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:72)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-11 02:23:15.161 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:23:21.206 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 58833 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 02:23:21.210 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 02:23:22.545 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 02:23:22.564 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:23:22.576 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 02:23:22.576 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 02:23:22.685 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 02:23:22.685 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1393 ms
[] 2024-05-11 02:23:22.918 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:23:23.084 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 02:23:23.319 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:23:23.384 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 02:23:23.394 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 2.823 seconds (JVM running for 3.285)
[] 2024-05-11 02:23:50.864 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-11 02:23:50.864 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-11 02:23:50.873 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 8 ms
[] 2024-05-11 02:23:53.086 [ERROR] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:72)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-11 02:24:22.606 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:24:41.054 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 58862 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 02:24:41.058 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 02:24:42.734 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 02:24:42.768 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:24:42.786 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 02:24:42.787 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 02:24:42.931 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 02:24:42.931 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1815 ms
[] 2024-05-11 02:24:43.250 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:24:43.424 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 02:24:43.513 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:24:43.570 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 02:24:43.574 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.464 seconds (JVM running for 5.876)
[] 2024-05-11 02:25:05.987 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-11 02:25:05.987 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-11 02:25:05.995 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 7 ms
[] 2024-05-11 02:25:36.412 [ERROR] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:72)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-11 02:26:47.829 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:26:52.543 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 58956 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 02:26:52.548 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 02:26:53.981 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 02:26:54.023 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:26:54.057 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 02:26:54.060 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 02:26:54.246 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 02:26:54.246 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1558 ms
[] 2024-05-11 02:26:54.523 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:26:54.688 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 02:26:54.848 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:26:54.889 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 02:26:54.896 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.413 seconds (JVM running for 4.137)
[] 2024-05-11 02:28:10.327 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:28:15.732 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 58992 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 02:28:15.735 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 02:28:17.675 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 02:28:17.696 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:28:17.714 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 02:28:17.714 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 02:28:17.848 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 02:28:17.848 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 2003 ms
[] 2024-05-11 02:28:18.133 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:28:18.233 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 02:28:18.357 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:28:18.388 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 02:28:18.392 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.306 seconds (JVM running for 3.953)
[] 2024-05-11 02:28:35.161 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-11 02:28:35.161 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-11 02:28:35.167 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 6 ms
[] 2024-05-11 02:28:42.617 [ERROR] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:70)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-11 02:32:13.988 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:32:38.399 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 59102 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 02:32:38.403 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 02:32:39.927 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 02:32:39.943 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:32:39.957 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 02:32:39.958 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 02:32:40.065 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 02:32:40.066 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1461 ms
[] 2024-05-11 02:32:40.398 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:32:40.493 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 02:32:40.674 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:32:40.702 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 02:32:40.706 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.122 seconds (JVM running for 3.712)
[] 2024-05-11 02:33:00.632 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-11 02:33:00.664 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-11 02:33:01.134 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 465 ms
[] 2024-05-11 02:33:10.308 [ERROR] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:70)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:34)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-11 02:34:46.073 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:35:18.934 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 59171 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 02:35:18.938 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 02:35:20.876 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 02:35:20.927 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:35:20.945 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 02:35:20.948 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 02:35:21.162 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 02:35:21.163 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 2152 ms
[] 2024-05-11 02:35:21.411 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:35:21.619 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 02:35:21.884 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:35:21.950 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 02:35:21.958 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 4.289 seconds (JVM running for 5.023)
[] 2024-05-11 02:35:29.419 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:35:34.495 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 59182 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 02:35:34.499 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 02:35:35.912 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 02:35:35.928 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:35:35.941 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 02:35:35.941 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 02:35:36.042 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 02:35:36.042 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1362 ms
[] 2024-05-11 02:35:36.250 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:35:36.346 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 02:35:36.489 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:35:36.531 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 02:35:36.535 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 2.78 seconds (JVM running for 3.318)
[] 2024-05-11 02:35:54.076 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-11 02:35:54.077 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-11 02:35:54.084 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 7 ms
[] 2024-05-11 02:35:59.476 [ERROR] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:71)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:35)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-11 02:39:37.332 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:39:43.706 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 59271 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 02:39:43.711 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 02:39:44.954 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 02:39:44.971 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:39:44.986 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 02:39:44.987 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 02:39:45.094 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 02:39:45.094 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1321 ms
[] 2024-05-11 02:39:45.395 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 02:39:45.496 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 02:39:45.606 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 02:39:45.638 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 02:39:45.644 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 2.576 seconds (JVM running for 3.212)
[] 2024-05-11 02:39:54.319 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-11 02:39:54.320 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-11 02:39:54.328 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 8 ms
[] 2024-05-11 02:39:59.242 [ERROR] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:71)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:35)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-11 02:39:59.442 [ERROR] [http-nio-8081-exec-2] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/].[dispatcherServlet]] 175 - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception
java.io.EOFException: null
	at java.io.DataInputStream.readInt(DataInputStream.java:392)
	at com.sun.media.sound.WaveFileReader.getFMT(WaveFileReader.java:234)
	at com.sun.media.sound.WaveFileReader.getAudioInputStream(WaveFileReader.java:199)
	at javax.sound.sampled.AudioSystem.getAudioInputStream(AudioSystem.java:1181)
	at org.acme.speak.controller.AudioMerger.mergeWavFiles(AudioMerger.java:19)
	at org.acme.speak.controller.HelloController.processText(HelloController.java:71)
	at org.acme.speak.controller.HelloController.importExcel(HelloController.java:35)
	at sun.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at sun.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:62)
	at sun.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.lang.reflect.Method.invoke(Method.java:498)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:190)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:138)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:104)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:892)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:797)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1039)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:942)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1005)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:908)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:660)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:882)
	at javax.servlet.http.HttpServlet.service(HttpServlet.java:741)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:231)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:53)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:99)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:92)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.HiddenHttpMethodFilter.doFilterInternal(HiddenHttpMethodFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:200)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:109)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:193)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:166)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:202)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:96)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:490)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:139)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:92)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:343)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:408)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:66)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:853)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1587)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:49)
	at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
	at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.lang.Thread.run(Thread.java:750)
[] 2024-05-11 03:26:42.653 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 03:26:46.398 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 60414 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 03:26:46.401 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 03:26:47.851 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 03:26:47.867 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 03:26:47.882 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 03:26:47.882 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 03:26:47.983 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 03:26:47.983 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1488 ms
[] 2024-05-11 03:26:48.244 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 03:26:48.386 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 03:26:48.513 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 03:26:48.540 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 03:26:48.544 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.035 seconds (JVM running for 3.537)
[] 2024-05-11 03:27:05.895 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-11 03:27:05.895 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-11 03:27:05.918 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 18 ms
[] 2024-05-11 10:10:07.268 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 19:49:49.078 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 75300 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 19:49:49.082 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 19:49:50.656 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 19:49:50.681 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 19:49:50.699 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 19:49:50.700 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 19:49:50.859 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 19:49:50.860 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1715 ms
[] 2024-05-11 19:49:51.187 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 19:49:51.419 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 19:49:51.504 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 19:49:51.552 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 19:49:51.556 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.942 seconds (JVM running for 5.831)
[] 2024-05-11 19:50:48.357 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-11 19:50:48.358 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-11 19:50:48.367 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 9 ms
[] 2024-05-11 19:58:26.405 [INFO] [Thread-3] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 208 - Shutting down ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 19:58:35.355 [INFO] [main] [org.acme.speak.SpeakApplication] 50 - Starting SpeakApplication on kingdeMacBook-Air.local with PID 75536 (/Users/<USER>/Documents/code/github/SpringBoot-Learning/speak/target/classes started by tomas in /Users/<USER>/Documents/code/github/SpringBoot-Learning)
[] 2024-05-11 19:58:35.360 [INFO] [main] [org.acme.speak.SpeakApplication] 646 - No active profile set, falling back to default profiles: default
[] 2024-05-11 19:58:36.885 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 90 - Tomcat initialized with port(s): 8081 (http)
[] 2024-05-11 19:58:36.901 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Initializing ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 19:58:36.913 [INFO] [main] [org.apache.catalina.core.StandardService] 173 - Starting service [Tomcat]
[] 2024-05-11 19:58:36.913 [INFO] [main] [org.apache.catalina.core.StandardEngine] 173 - Starting Servlet engine: [Apache Tomcat/9.0.21]
[] 2024-05-11 19:58:37.017 [INFO] [main] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring embedded WebApplicationContext
[] 2024-05-11 19:58:37.018 [INFO] [main] [org.springframework.web.context.ContextLoader] 283 - Root WebApplicationContext: initialization completed in 1512 ms
[] 2024-05-11 19:58:37.304 [INFO] [main] [org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor] 171 - Initializing ExecutorService 'applicationTaskExecutor'
[] 2024-05-11 19:58:37.431 [INFO] [main] [org.springframework.boot.autoconfigure.web.servlet.WelcomePageHandlerMapping] 54 - Adding welcome page: class path resource [META-INF/resources/index.html]
[] 2024-05-11 19:58:37.496 [INFO] [main] [org.apache.coyote.http11.Http11NioProtocol] 173 - Starting ProtocolHandler ["http-nio-8081"]
[] 2024-05-11 19:58:37.576 [INFO] [main] [org.springframework.boot.web.embedded.tomcat.TomcatWebServer] 202 - Tomcat started on port(s): 8081 (http) with context path ''
[] 2024-05-11 19:58:37.580 [INFO] [main] [org.acme.speak.SpeakApplication] 59 - Started SpeakApplication in 3.804 seconds (JVM running for 4.359)
[] 2024-05-11 19:58:49.153 [INFO] [http-nio-8081-exec-1] [org.apache.catalina.core.ContainerBase.[Tomcat].[localhost].[/]] 173 - Initializing Spring DispatcherServlet 'dispatcherServlet'
[] 2024-05-11 19:58:49.154 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 524 - Initializing Servlet 'dispatcherServlet'
[] 2024-05-11 19:58:49.159 [INFO] [http-nio-8081-exec-1] [org.springframework.web.servlet.DispatcherServlet] 546 - Completed initialization in 5 ms
