package com.tomas.mybaties3.excel;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;

public class ExcelExportDemo {

    public static String path="/Users/<USER>/code/github/SpringBoot-Learning/dbDoc/src/test/java/com/tomas/mybaties3/testDemo/" ;
    public static void main(String[] args) {


        String content = "【执价未成功预警】" +
                "<br>调价单号："+
                "<br>调价单名称：";
        System.out.println(content);
       String content2 = content.replaceAll("<br>","\n");

        System.out.println(content2);

        // 第一个sheet的列映射
        Map<String, String> sheet1ColumnMapping = new HashMap<>();
        sheet1ColumnMapping.put("productId", "商品编码9");
        sheet1ColumnMapping.put("productName", "商品名称7");
        sheet1ColumnMapping.put("specification", "规格8");
        sheet1ColumnMapping.put("unit", "单位4");
        sheet1ColumnMapping.put("specialPrice", "特价");
        sheet1ColumnMapping.put("effectiveDate", "生效日期");
        sheet1ColumnMapping.put("expirationDate", "失效日期");

        // 第二个sheet的列映射
        Map<String, String> sheet2ColumnMapping = new HashMap<>();
        sheet2ColumnMapping.put("supplierCode", "供应商编码5");
        sheet2ColumnMapping.put("supplierName", "供应商名称5");
        sheet2ColumnMapping.put("contactPerson", "联系人4");
        sheet2ColumnMapping.put("phoneNumber", "联系电话3");
        sheet2ColumnMapping.put("address", "地址2");
        sheet2ColumnMapping.put("businessScope", "经营范围2");
        sheet2ColumnMapping.put("cooperationStatus", "合作状态1");

        // 生成模拟数据
        List<Map<String, Object>> productData = generateProductMockData(20);
        List<Map<String, Object>> supplierData = generateSupplierMockData(15);

        // 导出Excel
        exportMultiSheetExcel(productData, supplierData, 
                              sheet1ColumnMapping, sheet2ColumnMapping, 
                              "multi_sheet_export.xlsx");
    }

    /**
     * 生成模拟商品特价数据
     */
    private static List<Map<String, Object>> generateProductMockData(int count) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        String[] productNames = {"苹果", "香蕉", "橙子", "葡萄", "西瓜", "草莓", "梨", "樱桃"};
        String[] specifications = {"500g", "1kg", "2kg", "盒装", "袋装"};
        String[] units = {"斤", "箱", "包", "个"};

        for (int i = 0; i < count; i++) {
            Map<String, Object> data = new HashMap<>();
            
            data.put("productId", "P" + String.format("%04d", RandomUtil.randomInt(1, 9999)));
            data.put("productName", productNames[RandomUtil.randomInt(productNames.length)]);
            data.put("specification", specifications[RandomUtil.randomInt(specifications.length)]);
            data.put("unit", units[RandomUtil.randomInt(units.length)]);
            
            BigDecimal originalPrice = BigDecimal.valueOf(RandomUtil.randomDouble(10, 100));
            BigDecimal specialPrice = originalPrice.multiply(BigDecimal.valueOf(RandomUtil.randomDouble(0.7, 0.9)))
                    .setScale(2, BigDecimal.ROUND_HALF_UP);
            data.put("specialPrice", specialPrice);
            
            Date effectiveDate = DateUtil.offsetDay(new Date(), RandomUtil.randomInt(-30, 30));
            data.put("effectiveDate", effectiveDate);
            
            Date expirationDate = DateUtil.offsetDay(effectiveDate, RandomUtil.randomInt(7, 30));
            data.put("expirationDate", expirationDate);

            dataList.add(data);
        }
        
        return dataList;
    }

    /**
     * 生成模拟供应商数据
     */
    private static List<Map<String, Object>> generateSupplierMockData(int count) {
        List<Map<String, Object>> dataList = new ArrayList<>();
        
        String[] supplierNames = {"农夫果园", "阳光农场", "绿色蔬果", "鲜果汇", "丰收农业", "优质果业"};
        String[] contactPersons = {"张三", "李四", "王五", "赵六", "陈七", "刘八"};
        String[] businessScopes = {"水果种植", "蔬菜供应", "农产品加工", "有机农业", "果蔬批发"};
        String[] cooperationStatuses = {"合作中", "洽谈中", "暂停合作", "新签约", "长期合作"};

        for (int i = 0; i < count; i++) {
            Map<String, Object> data = new HashMap<>();
            
            data.put("supplierCode", "S" + String.format("%04d", RandomUtil.randomInt(1, 9999)));
            data.put("supplierName", supplierNames[RandomUtil.randomInt(supplierNames.length)]);
            data.put("contactPerson", contactPersons[RandomUtil.randomInt(contactPersons.length)]);
            data.put("phoneNumber", "1" + RandomUtil.randomNumbers(10));
            data.put("address", "XX省XX市XX区" + RandomUtil.randomNumbers(3) + "号");
            data.put("businessScope", businessScopes[RandomUtil.randomInt(businessScopes.length)]);
            data.put("cooperationStatus", cooperationStatuses[RandomUtil.randomInt(cooperationStatuses.length)]);

            dataList.add(data);
        }
        
        return dataList;
    }

    /**
     * 导出多sheet的Excel，并支持合并首行标题
     */
    private static void exportMultiSheetExcel(
            List<Map<String, Object>> sheet1Data, 
            List<Map<String, Object>> sheet2Data,
            Map<String, String> sheet1ColumnMapping,
            Map<String, String> sheet2ColumnMapping,
            String fileName) {
        // 创建工作簿
        Workbook workbook = new XSSFWorkbook();

        try {
            // 第一个sheet
            exportSheetWithMergedTitle(workbook, "商品特价信息", 
                                       sheet1Data, sheet1ColumnMapping);

            // 第二个sheet
            exportSheetWithMergedTitle(workbook, "供应商信息", 
                                       sheet2Data, sheet2ColumnMapping);

            // 写入文件
            try (FileOutputStream fileOut = new FileOutputStream(path+fileName)) {
                workbook.write(fileOut);
            }

            System.out.println("Excel文件 " + path+fileName + " 导出成功!");
        } catch (IOException e) {
            e.printStackTrace();
            System.err.println("Excel文件导出失败: " + e.getMessage());
        } finally {
            try {
                workbook.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 导出带有合并首行标题的sheet
     */
    private static void exportSheetWithMergedTitle(
            Workbook workbook, 
            String sheetName,
            List<Map<String, Object>> data, 
            Map<String, String> columnMapping) {
        // 创建sheet
        Sheet sheet = workbook.createSheet(sheetName);

        // 创建样式
        CellStyle titleStyle = workbook.createCellStyle();
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 14);
        titleStyle.setFont(titleFont);
        titleStyle.setAlignment(HorizontalAlignment.CENTER);

        // 创建第一行合并标题
        Row titleRow = sheet.createRow(0);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(sheetName);
        titleCell.setCellStyle(titleStyle);
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, columnMapping.size() - 1));

        // 创建列头
        Row headerRow = sheet.createRow(1);
        int colIndex = 0;
        for (String columnName : columnMapping.values()) {
            Cell cell = headerRow.createCell(colIndex++);
            cell.setCellValue(columnName);
        }

        // 写入数据
        List<String> columnOrder = new ArrayList<>(columnMapping.keySet());
        for (int i = 0; i < data.size(); i++) {
            Row dataRow = sheet.createRow(i + 2);
            Map<String, Object> rowData = data.get(i);
            
            for (int j = 0; j < columnOrder.size(); j++) {
                Cell cell = dataRow.createCell(j);
                Object value = rowData.get(columnOrder.get(j));
                if (value != null) {
                    if (value instanceof Date) {
                        // 日期格式转换
                        cell.setCellValue((Date) value);
                    } else {
                        cell.setCellValue(value.toString());
                    }
                }
            }
        }
    }
}