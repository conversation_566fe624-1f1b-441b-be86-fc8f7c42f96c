package com.tomas.mybaties3.rest;

import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.servlet.http.HttpServletResponse;

public class ExcelController {

/**
 * 条件全部导出
 */
@ResponseBody
@RequestMapping("/export_all")
public void export_all(HttpServletResponse response) {
//    List<Map<String, Object>> listData1 = xxxService.listData1();
//    List<Map<String, Object>> listData2 = xxxService.listData2();
//    List<Map<String, Object>> listData3 = xxxService.listData3();
//
//    Map<String, String> map1 = new LinkedHashMap<String, String>();
//    map1.put("store_name", "客户名称");
//    map1.put("store_out_trade_no", "客户编码");
//    map1.put("store_contract_year", "年份");
//    map1.put("business_dept_name", "所属事业部");
//
//    Map<String, String> map2 = new LinkedHashMap<String, String>();
//    map2.put("store_name", "客户名称");
//    map2.put("store_out_trade_out", "客户编码");
//    map2.put("store_contract_year", "年份");
//    map2.put("store_name", "关联客户名称");
//    map2.put("store_out_trade_out", "关联客户编码");
//
//    Map<String, String> map3 = new LinkedHashMap<String, String>();
//    map3.put("store_name", "客户名称");
//    map3.put("store_out_trade_out", "客户编码");
//    map3.put("store_contract_year", "年份");
//    map3.put("name", "重要负责人姓名");
//    map3.put("position", "重要负责人职位");
//
//    List<SheetDTO> arrayList = new ArrayList<SheetDTO>();
//    arrayList.add(new SheetDTO("客户信息", map1, listData1));
//    arrayList.add(new SheetDTO("关联客户信息", map2, listData2));
//    arrayList.add(new SheetDTO("重要负责人信息", map3, listData3));

   // HuExcelUtils.exportExcel(response, arrayList, "客户信息导出");
}

}
