/*
package com.tomas.mybaties3;

import cn.hutool.core.io.IoUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.smallbun.screw.core.Configuration;
import cn.smallbun.screw.core.engine.EngineConfig;
import cn.smallbun.screw.core.engine.EngineFileType;
import cn.smallbun.screw.core.engine.EngineTemplateType;
import cn.smallbun.screw.core.execute.DocumentationExecute;
import cn.smallbun.screw.core.process.ProcessConfig;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.tomas.mybaties3.model.IscmSuggestAllotGoodsDetail;
import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Slf4j
@RestController
@RequestMapping("dbDoc")
public class DBDocsController {

    @RequestMapping("hello")
    public Object jdbc() {
        return "ok";
    }

    @RequestMapping("create")
    public Object jdbc(String uid) {
            //数据源
            HikariConfig hikariConfig = new HikariConfig();
            hikariConfig.setDriverClassName("com.mysql.cj.jdbc.Driver");
            hikariConfig.setJdbcUrl("************************************************************************************************************");
            hikariConfig.setUsername("root");
            hikariConfig.setPassword("123456");
            //设置可以获取tables remarks信息
            hikariConfig.addDataSourceProperty("useInformationSchema", "true");
            hikariConfig.setMinimumIdle(2);
            hikariConfig.setMaximumPoolSize(5);
            DataSource dataSource = new HikariDataSource(hikariConfig);
            //生成配置
            EngineConfig engineConfig = EngineConfig.builder()
                    //生成文件路径
                    .fileOutputDir("/tmp/")
                    //打开目录
                    .openOutputDir(true)
                    //文件类型
                    .fileType(EngineFileType.MD)
                    //生成模板实现
                    .produceType(EngineTemplateType.freemarker)
                    //自定义文件名称
                    .fileName("数据库2020-02-02").build();

            //忽略表
            ArrayList<String> ignoreTableName = new ArrayList<>();
            ignoreTableName.add("test_user");
            ignoreTableName.add("test_group");
            //忽略表前缀
            ArrayList<String> ignorePrefix = new ArrayList<>();
            ignorePrefix.add("test_");
            //忽略表后缀
            ArrayList<String> ignoreSuffix = new ArrayList<>();
            ignoreSuffix.add("_test");
            ProcessConfig processConfig = ProcessConfig.builder()
                    //指定生成逻辑、当存在指定表、指定表前缀、指定表后缀时，将生成指定表，其余表不生成、并跳过忽略表配置
                    //根据名称指定表生成
                    .designatedTableName(new ArrayList<>())
                    //根据表前缀生成
                    .designatedTablePrefix(new ArrayList<>())
                    //根据表后缀生成
                    .designatedTableSuffix(new ArrayList<>())
                    //忽略表名
                    .ignoreTableName(ignoreTableName)
                    //忽略表前缀
                    .ignoreTablePrefix(ignorePrefix)
                    //忽略表后缀
                    .ignoreTableSuffix(ignoreSuffix).build();
            //配置
            Configuration config = Configuration.builder()
                    //版本
                    .version("1.0.1")
                    //描述
                    .description("数据库设计文档")
                    //数据源
                    .dataSource(dataSource)
                    //生成配置
                    .engineConfig(engineConfig)
                    //生成配置
                    .produceConfig(processConfig)
                    .build();
            //执行生成
            new DocumentationExecute(config).execute();

            return "ok";
    }



        @RequestMapping("json")
        public Object json(String id) {

        String json="[{\"allotNo\":\"43897\",\"allotType\":1,\"approveBy\":107102823410000,\"approveName\":\"jinlan\",\"approveResult\":\"\",\"approveStatus\":1,\"approveTime\":1616649617000,\"batchNo\":\"AGJ91108102\",\"businessDate\":1615132800000,\"createdBy\":-1,\"createdName\":\"bdp\",\"dosageForm\":\"胶囊剂-肠溶胶囊\",\"expiryTime\":1629907200000,\"gmtCreate\":1615200449000,\"gmtUpdate\":1616667960000,\"goodsCommonName\":\"双氯芬酸钠双释放肠溶胶囊\",\"goodsDesc\":\"(戴芬)双氯芬酸钠双释放肠溶胶囊75mg*10粒Temmler Ireland Ltd.\",\"goodsNo\":\"1166353\",\"id\":160121,\"inCompanyCode\":\"3080\",\"inCompanyId\":534,\"inCompanyName\":\"百家好一生\",\"inStoreCode\":\"A10G\",\"inStoreEstimateSalesDays\":11.1111,\"inStoreId\":10001201,\"inStoreName\":\"A10G\",\"inStorePosDailySales\":0.0900,\"inStoreStock\":4.0000,\"inStoreStockMax\":6.0000,\"inStoreStockMin\":3.0000,\"inStoreStockOnway\":0.0000,\"inStoreThirtyDaysSaleTimes\":3.0000,\"inStoreThirtyDaysSales\":4.0000,\"manufacturer\":\"Temmler Ireland Ltd.\",\"outCompanyCode\":\"3080\",\"outCompanyId\":534,\"outCompanyName\":\"百家好一生\",\"outStoreCode\":\"A11V\",\"outStoreEstimateSalesDays\":9999.0000,\"outStoreId\":10001168,\"outStoreName\":\"A11V\",\"outStorePosDailySales\":0.0000,\"outStoreStock\":0.0000,\"outStoreStockMax\":0.0000,\"outStoreStockMin\":0.0000,\"outStoreStockOnway\":0.0000,\"outStoreThirtyDaysSaleTimes\":0.0000,\"outStoreThirtyDaysSales\":0.0000,\"platformOrgId\":17,\"platformOrgName\":\"华中区域平台\",\"posAllotNo\":\"202103110000171\",\"realAllotQuantity\":1.0000,\"recordId\":142495,\"registerBy\":107102823410000,\"registerName\":\"jinlan\",\"registerNo\":\"DBJXQ-A11V-20210310-0001\",\"registerSource\":1,\"registerTime\":1615258770000,\"season\":0,\"status\":0,\"suggestAllotQuantity\":1.0000,\"transferCostAmount\":0.0000,\"unit\":\"盒\",\"updatedBy\":107102823410000,\"updatedName\":\"jinlan\",\"version\":0},{\"allotNo\":\"43866\",\"allotType\":1,\"approveBy\":107102823410000,\"approveName\":\"jinlan\",\"approveResult\":\"\",\"approveStatus\":1,\"approveTime\":1616674990000,\"batchNo\":\"191206\",\"businessDate\":1615132800000,\"createdBy\":-1,\"createdName\":\"bdp\",\"dosageForm\":\"口服溶液剂\",\"expiryTime\":1638115200000,\"gmtCreate\":1615200449000,\"gmtUpdate\":1616675050000,\"goodsCommonName\":\"复方大青叶合剂\",\"goodsDesc\":\"复方大青叶合剂10ml*6支荣昌制药(淄博)\",\"goodsNo\":\"1015016\",\"id\":160123,\"inCompanyCode\":\"3080\",\"inCompanyId\":534,\"inCompanyName\":\"百家好一生\",\"inStoreCode\":\"A116\",\"inStoreEstimateSalesDays\":1.0000,\"inStoreId\":10001222,\"inStoreName\":\"A116\",\"inStorePosDailySales\":6.4233,\"inStoreStock\":115.0000,\"inStoreStockMax\":142.0000,\"inStoreStockMin\":97.0000,\"inStoreStockOnway\":0.0000,\"inStoreThirtyDaysSaleTimes\":84.0000,\"inStoreThirtyDaysSales\":196.0000,\"manufacturer\":\"荣昌制药(淄博)有限公司\",\"outCompanyCode\":\"3080\",\"outCompanyId\":534,\"outCompanyName\":\"百家好一生\",\"outStoreCode\":\"A11L\",\"outStoreEstimateSalesDays\":45.3743,\"outStoreId\":10001168,\"outStoreName\":\"A11L\",\"outStorePosDailySales\":0.3967,\"outStoreStock\":18.0000,\"outStoreStockMax\":15.0000,\"outStoreStockMin\":8.0000,\"outStoreStockOnway\":0.0000,\"outStoreThirtyDaysSaleTimes\":4.0000,\"outStoreThirtyDaysSales\":7.0000,\"platformOrgId\":17,\"platformOrgName\":\"华中区域平台\",\"posAllotNo\":\"202103250000171\",\"realAllotQuantity\":2.0000,\"recordId\":142486,\"registerBy\":107102823410000,\"registerName\":\"jinlan\",\"registerNo\":\"DBJXQ-A11L-20210309-0001\",\"registerSource\":1,\"registerTime\":1615258770000,\"season\":0,\"status\":0,\"suggestAllotQuantity\":2.0000,\"transferCostAmount\":19.5400,\"unit\":\"盒\",\"updatedBy\":107102823410000,\"updatedName\":\"jinlan\",\"version\":0},{\"allotNo\":\"43865\",\"allotType\":1,\"approveBy\":107102823410000,\"approveName\":\"jinlan\",\"approveResult\":\"\",\"approveStatus\":1,\"approveTime\":1616675501000,\"batchNo\":\"191206\",\"businessDate\":1615132800000,\"createdBy\":-1,\"createdName\":\"bdp\",\"dosageForm\":\"口服溶液剂\",\"expiryTime\":1638115200000,\"gmtCreate\":1615200449000,\"gmtUpdate\":1616675530000,\"goodsCommonName\":\"复方大青叶合剂\",\"goodsDesc\":\"复方大青叶合剂10ml*6支荣昌制药(淄博)\",\"goodsNo\":\"1015016\",\"id\":160124,\"inCompanyCode\":\"3080\",\"inCompanyId\":534,\"inCompanyName\":\"百家好一生\",\"inStoreCode\":\"A11C\",\"inStoreEstimateSalesDays\":4.8137,\"inStoreId\":10001223,\"inStoreName\":\"A11C\",\"inStorePosDailySales\":5.8167,\"inStoreStock\":100.0000,\"inStoreStockMax\":128.0000,\"inStoreStockMin\":88.0000,\"inStoreStockOnway\":0.0000,\"inStoreThirtyDaysSaleTimes\":114.0000,\"inStoreThirtyDaysSales\":246.0000,\"manufacturer\":\"荣昌制药(淄博)有限公司\",\"outCompanyCode\":\"3080\",\"outCompanyId\":534,\"outCompanyName\":\"百家好一生\",\"outStoreCode\":\"A11L\",\"outStoreEstimateSalesDays\":45.3743,\"outStoreId\":10001168,\"outStoreName\":\"A11L\",\"outStorePosDailySales\":0.3967,\"outStoreStock\":18.0000,\"outStoreStockMax\":15.0000,\"outStoreStockMin\":8.0000,\"outStoreStockOnway\":0.0000,\"outStoreThirtyDaysSaleTimes\":4.0000,\"outStoreThirtyDaysSales\":7.0000,\"platformOrgId\":17,\"platformOrgName\":\"华中区域平台\",\"posAllotNo\":\"202103250000271\",\"realAllotQuantity\":20.0000,\"recordId\":142487,\"registerBy\":107102823410000,\"registerName\":\"jinlan\",\"registerNo\":\"DBJXQ-A11L-20210309-0001\",\"registerSource\":1,\"registerTime\":1615258770000,\"season\":0,\"status\":0,\"suggestAllotQuantity\":28.0000,\"transferCostAmount\":273.5600,\"unit\":\"盒\",\"updatedBy\":107102823410000,\"updatedName\":\"jinlan\",\"version\":0},{\"allotNo\":\"43874\",\"allotType\":1,\"approveBy\":107102823410000,\"approveName\":\"jinlan\",\"approveResult\":\"\",\"approveStatus\":1,\"approveTime\":1616675501000,\"batchNo\":\"20190306D\",\"businessDate\":1615219200000,\"createdBy\":-1,\"createdName\":\"bdp\",\"dosageForm\":\"片剂\",\"expiryTime\":1615824000000,\"gmtCreate\":1615200449000,\"gmtUpdate\":1616675530000,\"goodsCommonName\":\"B族维生素片\",\"goodsDesc\":\"(汤臣倍健)B族维生素片500mg*100片汤臣倍健股份\",\"goodsNo\":\"1020075\",\"id\":160125,\"inCompanyCode\":\"3080\",\"inCompanyId\":534,\"inCompanyName\":\"百家好一生\",\"inStoreCode\":\"A085\",\"inStoreEstimateSalesDays\":26.0926,\"inStoreId\":10001200,\"inStoreName\":\"A085\",\"inStorePosDailySales\":0.1533,\"inStoreStock\":1.0000,\"inStoreStockMax\":7.0000,\"inStoreStockMin\":4.0000,\"inStoreStockOnway\":0.0000,\"inStoreThirtyDaysSaleTimes\":4.0000,\"inStoreThirtyDaysSales\":6.0000,\"manufacturer\":\"汤臣倍健股份有限公司\",\"outCompanyCode\":\"3080\",\"outCompanyId\":534,\"outCompanyName\":\"百家好一生\",\"outStoreCode\":\"A11L\",\"outStoreEstimateSalesDays\":100.0000,\"outStoreId\":10001168,\"outStoreName\":\"A11L\",\"outStorePosDailySales\":0.0300,\"outStoreStock\":3.0000,\"outStoreStockMax\":2.0000,\"outStoreStockMin\":1.0000,\"outStoreStockOnway\":0.0000,\"outStoreThirtyDaysSaleTimes\":1.0000,\"outStoreThirtyDaysSales\":1.0000,\"platformOrgId\":17,\"platformOrgName\":\"华中区域平台\",\"posAllotNo\":\"202103250000371\",\"realAllotQuantity\":2.0000,\"recordId\":142492,\"registerBy\":107102823410000,\"registerName\":\"jinlan\",\"registerNo\":\"DBJXQ-A11L-20210309-0001\",\"registerSource\":1,\"registerTime\":1615258770000,\"season\":0,\"status\":0,\"suggestAllotQuantity\":4.0000,\"transferCostAmount\":225.6000,\"unit\":\"瓶\",\"updatedBy\":107102823410000,\"updatedName\":\"jinlan\",\"version\":0},{\"allotNo\":\"43863\",\"allotType\":1,\"approveBy\":107102823410000,\"approveName\":\"jinlan\",\"approveResult\":\"\",\"approveStatus\":1,\"approveTime\":1616676312000,\"batchNo\":\"43190612\",\"businessDate\":1615132800000,\"createdBy\":-1,\"createdName\":\"bdp\",\"dosageForm\":\"片剂\",\"expiryTime\":1661529600000,\"gmtCreate\":1615200449000,\"gmtUpdate\":1616676370000,\"goodsCommonName\":\"左炔诺孕酮片\",\"goodsDesc\":\"(金毓婷)左炔诺孕酮片1.5mg*1片华润紫竹\",\"goodsNo\":\"1006050\",\"id\":160127,\"inCompanyCode\":\"3080\",\"inCompanyId\":534,\"inCompanyName\":\"百家好一生\",\"inStoreCode\":\"A118\",\"inStoreEstimateSalesDays\":1.0000,\"inStoreId\":10001203,\"inStoreName\":\"A118\",\"inStorePosDailySales\":4.2200,\"inStoreStock\":70.0000,\"inStoreStockMax\":93.0000,\"inStoreStockMin\":64.0000,\"inStoreStockOnway\":0.0000,\"inStoreThirtyDaysSaleTimes\":158.0000,\"inStoreThirtyDaysSales\":152.0000,\"manufacturer\":\"华润紫竹药业有限公司\",\"outCompanyCode\":\"3080\",\"outCompanyId\":534,\"outCompanyName\":\"百家好一生\",\"outStoreCode\":\"A11L\",\"outStoreEstimateSalesDays\":48.0879,\"outStoreId\":10001168,\"outStoreName\":\"A11L\",\"outStorePosDailySales\":0.4367,\"outStoreStock\":21.0000,\"outStoreStockMax\":16.0000,\"outStoreStockMin\":8.0000,\"outStoreStockOnway\":0.0000,\"outStoreThirtyDaysSaleTimes\":12.0000,\"outStoreThirtyDaysSales\":13.0000,\"platformOrgId\":17,\"platformOrgName\":\"华中区域平台\",\"posAllotNo\":\"202103250000471\",\"realAllotQuantity\":1.0000,\"recordId\":142485,\"registerBy\":107102823410000,\"registerName\":\"jinlan\",\"registerNo\":\"DBJXQ-A11L-20210309-0001\",\"registerSource\":1,\"registerTime\":1615258770000,\"season\":0,\"status\":0,\"suggestAllotQuantity\":1.0000,\"transferCostAmount\":8.8000,\"unit\":\"盒\",\"updatedBy\":107102823410000,\"updatedName\":\"jinlan\",\"version\":0}]";
        List<IscmSuggestAllotGoodsDetail> detailList = JSONArray.parseArray(json, IscmSuggestAllotGoodsDetail.class);

        detailList.forEach(v->{
               // System.out.println(v.toString());
        });
        //, Collectors.collectingAndThen(Collectors.toList(), value -> value.get(0))
        Map<String, List<IscmSuggestAllotGoodsDetail>> collect = detailList.stream().collect(Collectors.groupingBy(v ->(fetchNearGroupKey(v)), Collectors.toList()));

        log.info(JSON.toJSONString(collect));
        IscmSuggestAllotGoodsDetail detail = detailList.get(0);

        String bug= detail.getRegisterNo() + "#" + detail.getPosAllotNo() +"#" + detail.getOutStoreCode()+ "#" + detail.getInStoreCode()+ "#" + detail.getGoodsNo() + "#" + detail.getBatchNo();
       log.info("08888888888*******"+bug);

        String s = fetchNearGroupKey(detailList.get(0));
                log.info("---&&&---**-----"+s);


                List<IscmSuggestAllotGoodsDetail> detailList1 = collect.get(bug);


                List<IscmSuggestAllotGoodsDetail> detailList2 = collect.get(s);

                List<IscmSuggestAllotGoodsDetail> detailList3 = collect.get("43190612");
                log.info("------catch1-----"+detailList1);
                log.info("------catch2-----"+detailList2);
                log.info("------catch3-----"+detailList3);
        //detailList.stream()
                return "detailList1";

        }


        @RequestMapping("file")
        public void excelDl(HttpServletResponse response){
            OutputStream out = null;
            ExcelWriter writer = ExcelUtil.getBigWriter();
            try {
                out = response.getOutputStream();
                //设置ConetentType CharacterEncoding
                response.setContentType("application/x-download;charset=utf-8");
                response.setCharacterEncoding("utf-8");
                response.setHeader("content-type", "application/x-download;charset=utf-8");
                response.setHeader("Content-disposition", "attachment;filename=" + URLEncoder.encode("这是中文","UTF-8")+".xlsx");
                //docker镜像增加fontconfig ttf-dejavu字体支持
                //只导出标题列字段
                writer.writeCellValue(3, 5, "aaa");
                writer.flush(out);
                //logger.info("AsyncExportFileServiceImpl.downloadFileToResponse|下载文件完成. key={}", key);
            } catch (Exception e) {
                //logger.warn("AsyncExportFileServiceImpl.downloadFileToResponse|下载文件:{}.", e);
            }finally {
                //关闭writer,关闭输出流 释放内存
                IoUtil.close(writer);
                IoUtil.close(out);
            }
        }

        public String fetchNearGroupKey(IscmSuggestAllotGoodsDetail v){
                log.info("08888888888*******"+v.getRegisterNo() + "#" + v.getPosAllotNo() +"#" + v.getOutStoreCode()+ "#" + v.getInStoreCode()+ "#" + v.getGoodsNo() + "#" +v.getBatchNo());
                return  (v.getRegisterNo() + "#" + v.getPosAllotNo() +"#" + v.getOutStoreCode()+ "#" + v.getInStoreCode()+ "#" + v.getGoodsNo() + "#" + v.getBatchNo());
        }

        @RequestMapping("del")
        public ResponseEntity<Object> deleteBatchPosHangup(){
             return new ResponseEntity("成功",HttpStatus.OK);
        }



}
*/
