package com.tomas.mybaties3.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;

/**
 * Test 描述
 *
 * <AUTHOR>
 * @create 2021/7/28
 **/
public class GJPostOnline3 {
    public static String epath="/Users/<USER>/code/github/SpringBoot-Learning/dbDoc/src/test/java/com/tomas/mybaties3/testDemo/" ;
    public static void main(String[] args) throws IOException, InterruptedException {

        String path = "/Users/<USER>/Desktop/su3000.txt" ;
        String result = "/Users/<USER>/Desktop/result3000.txt" ;
        String str = FileUtil.readUtf8String(new File(path));
        System.out.println(str);
        List<JSONObject> errJsonObjectList=new ArrayList<>();
        JSONArray parse = (JSONArray) JSONArray.parse(str);
        parse.stream().forEach(v->{
            JSONObject o = (JSONObject) JSON.toJSON(v);
            System.out.println(o.toString());
            //  System.out.println(s);authority: api-store.gaojihealth.cn
            String name= null;
            String address=null;
            String bz=null;
            String sfz=null;
            String sjh=null;
            try {
                name = URLEncoder.encode(o.getString("name").trim(), "UTF-8").trim();
                address = URLEncoder.encode(o.getString("address").trim(), "UTF-8").trim();
                bz = URLEncoder.encode(o.getString("bz").trim(), "UTF-8").trim();
                sfz = URLEncoder.encode(o.getString("sfz").trim(), "UTF-8").trim();
                sjh = URLEncoder.encode(o.getString("sjh").trim(), "UTF-8").trim();
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }

            //curl 'http://************:8691/WebAdmin/siji/UpdateSiJi.aspx?ID=372923198202103821' \
            //  -H 'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9' \
            //  -H 'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,fr;q=0.7' \
            //  -H 'Cache-Control: max-age=0' \
            //  -H 'Connection: keep-alive' \
            //  -H 'Content-Type: application/x-www-form-urlencoded' \
            //  -H 'Cookie: ASP.NET_SessionId=cpbjm1cfvy3caus553wbirwc; User_PID=5525e79c-f069-4706-a9fe-9092763b0ce8; User_Name=371703002; User_RoleType=5' \
            //  -H 'Origin: http://************:8691' \
            //  -H 'Referer: http://************:8691/WebAdmin/siji/UpdateSiJi.aspx?ID=372923198202103821' \
            //  -H 'Upgrade-Insecure-Requests: 1' \
            //  -H 'User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/103.0.0.0 Safari/537.36' \
            //  --data-raw '__VIEWSTATE=%2FwEPDwUJNjA4MTA2MjQ0D2QWAgIDD2QWBgIbDw8WAh4EVGV4dAUSMzcyOTIzMTk4MjAyMTAzODIxZGQCHQ8QDxYGHg5EYXRhVmFsdWVGaWVsZAUFRGFpTWEeDURhdGFUZXh0RmllbGQFCU1pbmdDaGVuZx4LXyFEYXRhQm91bmRnZBAVOgbmsYnml48J6JKZ5Y%2Bk5pePBuWbnuaXjwbol4%2Fml48M57u05ZC%2B5bCU5pePBuiLl%2BaXjwblvZ3ml48G5aOu5pePCeW4g%2BS%2BneaXjwnmnJ3pspzml48G5ruh5pePBuS%2Bl%2BaXjwbnkbbml48G55m95pePCeWcn%2BWutuaXjwnlk4jlsLzml48M5ZOI6JCo5YWL5pePBuWCo%2BaXjwbpu47ml48J5YKI5YOz5pePBuS9pOaXjwbnlbLml48J6auY5bGx5pePCeaLieelnOaXjwbmsLTml48J5Lic5Lmh5pePCee6s%2Bilv%2BaXjwnmma%2Fpoofml48P5p%2Bv5bCU5YWL5a2c5pePBuWcn%2BaXjwzovr7mlqHlsJTml48J5Lur5L2s5pePBue%2BjOaXjwnluIPmnJfml48J5pKS5ouJ5pePCeavm%2BmavuaXjwnku6Hkvazml48J6ZSh5Lyv5pePCemYv%2BaYjOaXjwnmma7nsbPml48M5aGU5ZCJ5YWL5pePBuaAkuaXjw%2FkuYzlrZzliKvlhYvml48M5L%2BE572X5pav5pePDOmEgua4qeWFi%2BaXjwnltKnpvpnml48J5L%2Bd5a6J5pePCeijleWbuuaXjwbkuqzml48M5aGU5aGU5bCU5pePCeeLrOm%2BmeaXjwzphILkvKbmmKXml48J6LWr5ZOy5pePCemXqOW3tOaXjwnnj57lt7Tml48J5Z%2B66K%2B65pePBuWFtuS7lhvlpJblm73ooYDnu5%2FkuK3lm73nsY3kurrlo6sVOgExATIBMwE0ATUBNgE3ATgBOQIxMAIxMQIxMgIxMwIxNAIxNQIxNgIxNwIxOAIxOQIyMAIyMQIyMgIyMwIyNAIyNQIyNgIyNwIyOAIyOQIzMAIzMQIzMgIzMwIzNAIzNQIzNgIzNwIzOAIzOQI0MAI0MQI0MgI0MwI0NAI0NQI0NgI0NwI0OAI0OQI1MAI1MQI1MgI1MwI1NAI1NQI1NgI5NwI5OBQrAzpnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZGQCHw8QDxYGHwEFBURhaU1hHwIFCU1pbmdDaGVuZx8DZ2QQFSM857qz5YWl56S%2B5Yy6566h55CG55qE5paw5Yag6IK654KO5oSf5p%2BT6ICF5Y%2BK5YW25ZCM5L2P5Lq65ZGYHumrmOaatOmcsuWyl%2BS9jeeahOS7juS4muS6uuWRmCrljLvnlpfmnLrmnoTlhaXpmaLmgqPogIXlj4rlhbbpmarmiqTkurrlkZgn5YW25LuW5py65YWz5LyB5LqL5Lia5Y2V5L2N5bel5L2c5Lq65ZGYJ%2BW3peS9nOeOr%2BWig%2BS6uuWRmOWkjeadgueahOS7juS4muS6uuWRmELmma7pgJrljLvnlpfmnLrmnoTvvIjpmaTlj5Hng63pl6jor4rlpJbvvInjgIHoja%2Flupflt6XkvZzkurrlkZjnrYkh55Sf5Lqn6L2m6Ze05ZKM5bu6562R5bel5Zyw5Lq65ZGYM%2BW5suitpuiBjOW3peOAgeacjeWKoeS%2FnemanOS6uuWRmOWSjOiiq%2BebkeeuoeS6uuWRmDbmnI3liqHmsJHmlL%2FmnI3liqHlr7nosaHnmoTlt6XkvZzkurrlkZjlkozmnI3liqHlr7nosaEV5biI55Sf5ZKM5bel5L2c5Lq65ZGYJ%2Bi%2Fm%2BWPo%2BmdnuWGt%2BmTvuS9jeeahOWFtuS7luW3peS9nOS6uuWRmDDov5vlj6Plhrfpk77pl63njq%2FnrqHnkIbkurrlkZjmnI3liqHkv53pmpzkurrlkZgw6ZuG5Lit6ZqU56a75Zy65omA5Lq65ZGY5aSW5Zu05pyN5Yqh5L%2Bd6Zqc5Lq65ZGYGOWPo%2BWyuOeuoeeQhuacjeWKoeS6uuWRmCrnm7TmjqXmjqXop6blm73pmYXpgq7ku7blv6vku7blt6XkvZzkurrlkZgz55u05o6l5o6l6Kem5YWl5aKD5Lq65ZGY5ZKM54mp5ZOB55qE5LiA57q%2F5Lq65ZGY562JHuWPo%2BWyuOi%2Fm%2BWPo%2BeJqeWTgeaQrOi%2FkOS6uuWRmDbot6jlooPkuqTpgJrlt6XlhbfnmoTlj7jkuZjjgIHkv53mtIHjgIHnu7Tkv67nrYnkurrlkZgw5aKD5aSW5p2l5paZ5Yqg5bel44CB6L%2BQ6L6T562J5LyB5Lia5bel5L2c5Lq65ZGYTui%2FnOa0i%2BawtOS6p%2BaNleaNnuOAgei%2FkOi%2Bk%2BOAgeS7k%2BWCqOOAgeWKoOW3peWSjOeggeWktOi%2FkOiQpeS8geS4muS7juS4muS6uuWRmBLlhrfpk77nm7jlhbPkurrlkZge6ZuG5Lit6ZqU56a75Zy65omA5bel5L2c5Lq65ZGYIeaWsOWGoOeXheavkuWunumqjOWupOajgOa1i%2BS6uuWRmCrmma7pgJrljLvnlpfmnLrmnoTlj5Hng63pl6jor4rlt6XkvZzkurrlkZge5a6a54K55Yy755aX5py65p6E5bel5L2c5Lq65ZGYMOiNr%2BW6l%2BeZu%2BiusOi0reS5sOS9v%2BeUqOKAnOWbm%2Bexu%2BiNr%2BWTgeKAneS6uuWRmGPlj5Hng63mgqPogIXjgIHlj6%2FnlpHmgqPogIXjgIHkuI3mmI7ljp%2Flm6Dogrrngo7jgIHkvY%2FpmaLmgqPogIXkuK3kuKXph43mgKXmgKflkbzlkLjpgZPmhJ%2Fmn5Pnl4XkvosY5L2O6aOO6Zmp5Yy65aSW5rqi5Lq65ZGYGOS4remjjumZqeWMuuWklua6ouS6uuWRmBjpq5jpo47pmanljLrlpJbmuqLkurrlkZgS5qyh5a%2BG5YiH5o6l6Kem6ICFHuWvhuWIh%2BaOpeinpuiAheOAgeWFpeWig%2BS6uuWRmBLln47luILnpL7ljLrlsYXmsJEM5Yac5p2R5bGF5rCRBuWFtuS7lhUjAjMyAjMxAjMwAjI5AjI4AjI3AjI2AjI1AjI0AjIzAjIyAjIxAjIwAjE5AjE4AjE3AjE2AjE1AjE0AjEzAjEyAjExAjEwATkBOAE3ATYBNQE0ATMBMgExAjM1AjM0AjMzFCsDI2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZGRkllzRtmKOmdiNitGX8D%2FzCQUKhpmLcjltdEqoErWrXII%3D&__VIEWSTATEGENERATOR=08B888C1&__EVENTVALIDATION=%2FwEdAH3p061iWdxSIMPRky5BuPY1X9ZLR5rgGocRm4vzvcbSbJ%2FHEXC05jPNYWraiRyooU6wlzXD6U6HCyo44SsC85LkCQn5Qt1eVqs10juWBbE66Ww4PLpNyJogmbbM%2BMT%2FZ9f5rj7FBBBotbrGr3q77D%2FJ8b4IkE6oHqht5eSfKjT%2BqsN3DA15cemMdivrkJvT4C0X66UykHRP8aSDrKFdF6Qs8T9HuPEYd%2F4fHtBjKQZ9Mr%2FqU8NlfImWrqXPBSRD3VvOcdtBQj7VpmmkpkJ3cRDhGpN5meBpVVfhPBDhIMPuA6Hfu%2FZY4ieT5VQLBxW2vFFpS04KTv%2FqQlWqVYkpnZNhh8Hbn1rO8rW0IBoMLMMPDPwMMk8osazfK8Wm4qNgMzI0BCf9lG7AfXX1%2FpBHcFHGY6ZASk5Tg9l5J8vFG1A0sBE65Xiz2FWvoLRGLNfqoxZrpEyU%2FSeTFOiH2RT4cLa%2FBMeDElpxbVeW9H6O%2Bk476G5TeJntxOWcm73582%2BCCqQWr7QcFQalN8IUvG%2BSfNvIBVCLLaxCd7nvUMvAm%2BaL0ZaSKOaMKKlZNOLUrmAVbReb4PUKzrC5tT4jIg%2Bq0mlPrA6azRIId%2F1%2FNoz5BNZq0rrUPPLKbtWcJI63mEbojYWa%2BTIhhYuA7N6lvHmSO63UxgHoDCr2wzbKKx1HeiI%2B0ewiscpXTg8iNHE8jfn4Pd8xF6Ot4ckLDyibh%2BkVbgCqUQmydEJiHfQt6vnJHdYuJqAKRvME31q%2F8YQvdRp6aKL%2BxNbbYw4eevu24omdNkQYz72N%2B12sDx39FJFTLxiWNIwt4IVlvV%2By1cIyifvYbjZT9TEQJEVVpNeYrr3iZBPYwCHt0zW6MZqxRvvDPAXPFg%2BaMT8EpsUl2pW%2FpjkG1XGtWyDeOlTw7FiE1x58CBp5ba%2FekPsqepgQR7Axv8V%2Fl9MqPCdQX%2BjEgYjnNwnJSrVLRKA1cuvb7L5HeUz5KzOA3qcvlJXgRKr8TG5Mnr10julNWXI2QrO4MRrTBb3TBzawWESuJzoToy6%2BWpIrp6XedMm6vm%2FhPScw%2Fytuv0iLsMHmHEmI3JnFMcxSQkhfnmvtaHl4ZtKUg0kNqPrOGm2DM6NpGzuvzafyfIwon9XpJ9fxlthbi9fMT1unl%2FCTyYs%2Faxu0eU3mZkVkgdR%2F4t59x00HX1g8QuIW1f95EYXwB1%2B42gs5Bcxo9ubnxtqxYJjqH%2BJB6S%2FFrKCyxlpj5AiPadHyZn2u2oRle9w93L8ElDZmHPBF5KrTAtoa9fRTOjsJAzxURICqfAo9yPRKQtzia4NU35P2ANDD9h12d6oK4pnvhEr0Z%2F5v%2BU1fPcZT%2FN0bGPxuFtKGBHv4JDDgTil0QkzdQtczRo3JzLpxjOj1vfMuH37k6aH7ofnhX2bYxuUVzALzJ9M1Zb6keBD4EldgbLzIqyVzx4RBqwy2yQIR%2Ft1mbOu1gSi0O8P8aYprpydA53zGKZApwGgq4zmjflxSYg1HjnIwGcXqzpEJ4bETBMUgkFdHf%2BJ71icg93hoWLKcp5ndZI%2Bwvty4CVBS%2FX2o0CdI6o3qXbaf2NqXCeIGZsqTYqhUbu2AX1pm1%2FxlXX9VG6xIa%2Fkslam0AlRNn7ue9faDEaiTDcObN4X8QMUXYUbFLJY1g81IcfFBB4aLUD9tMcR%2FR8b8yQ39qJoiXOQajuGKQ47FGxr%2BZNCsyti1%2FCu9N5AKGN7njWqrmZxucXHKCWcwtrkXjwhpUzRJBLrb3e4jn1xESJkbsaqDAzmCfRZm30YJUXMBXArmBN6ScbyTnUM9K%2FziEv0mgIgWBqGE9m%2BtCZsnyHEuY%2FK2DGoQR2Qz6Drr8WEd5aJ7cGTHW%2FvRxNMVjjFOeEEF9V7f7DThiNbTA8m7AmTLCK809XICXrzbjwjUVPXuzA0qwzEtYxTyeTvvCIBIvCxguTmxr61c%2BoPnVEbc5EnAyGTxLQUrl00Qx8G%2FUD0kNFAMxt2Tjk0%2BvpWpmURfwJA6TCIm3z9K5ZyJGmZWBGUHtpeottvsFeNTnTp5NKmriXiMCP7ZNMgzNrBBloF2cQFNCGEgMcE%2BPIzysdZxG2b35TF3uGDgJKRH0CeHyuZcVPAEdS%2BmVZZ3ZuJNUquILqCbRR53VzTWFJOscj375J04uBYHQbHB2gT6OB3o1CRdhUt15YZwieqnfmDH2TptW1zifSdSEe4oevb%2B4QG2CgikYe1%2FWFK%2F0LuNno3xJqo6OjLYIMpLymlbLkGtvCIcyb7KjUDSCR9EhKER72K57mrkZrXR6HzrzrEAO1DhoaA3LhaFMsImcbRuLwvRj0%2FRJCigLuwbwA%2FBXUFluxfOUbWCaQikmPTXUk2KORXAylA5magiUB396JEGKVNyMVXnzl4cYzXUIBB6G1689LjKBk5kfRjXVsXmEIXNzTirY3kRs2Edjyi31rUpuYuS5CccpCHac%2F7I%2Fz797td5TRXJdSlE%2BCGk9c7QLsVmlqnmT1sRN8tVlC%2B5qH7L1LN4QJa0CxV2em2EABCOagYW9duRb3b1ZNs1bcj2QL4SoAm21dRf8leTkgcyO7F8YXRryBUOePVGLrdz29BtHkGU%2BU5JS02PR4htu9FAGrqpmoUM1V7VvDm96WyjJBfyDTAEYj1x7xXcDtx3blGXzfg78Z8BXhXifTCAVkevd6kANx33Lwc87rYKrCtWKSE7S1QRPN95QiAv2VB7ngAz&hid_yinhangkalist=&hidRoleCode=371703002&hidRoleType=5&hidPro=371703&hidCity=371703002&hidArea=371703002202&hidProName=%E5%AE%9A%E9%99%B6%E5%8C%BA&hidCityName=%E6%BB%A8%E6%B2%B3%E8%A1%97%E9%81%93&hidAreaName=%E4%BB%98%E5%BA%84%E6%9D%91%E5%A7%94%E4%BC%9A&ShengShiQuNew%24hidPro=&ShengShiQuNew%24hidProName=&ShengShiQuNew%24hidCity=&ShengShiQuNew%24hisCityname=&ShengShiQuNew%24hidArea=&ShengShiQuNew%24hidAreaname=&tbXingMing=%E6%9D%8E%E7%94%B3%E7%BF%A0&hidID=1660020686&ddlXingBie=2&ddlMinu=1&ddlRenQun=33&ddlRenYuanZhuangTai=1&ddlFangWu=1&tbShouJiHao=15376113229&tbDiZhi=%E9%99%B6%E9%83%BD%E6%96%B0%E9%9F%B5%E5%8D%97%E5%8C%BA4-2-1101&ddlIsYingJian=1&tbZhunJiaCheXing=%E9%99%B6%E9%83%BD%E6%96%B0%E9%9F%B5%E5%8D%97%E5%8C%BA1&Button1=%E4%BF%9D%E5%AD%98' \
            //  --compressed \
            //  --insecure
            //%E9%97%AB%E6%98%AD%E5%A5%87
            String url = "__VIEWSTATE=%2FwEPDwUJOTQ2Njk5NjYyD2QWAgIDD2QWBAIbDxAPFgYeDkRhdGFWYWx1ZUZpZWxkBQVEYWlNYR4NRGF0YVRleHRGaWVsZAUJTWluZ0NoZW5nHgtfIURhdGFCb3VuZGdkEBU6BuaxieaXjwnokpnlj6Tml48G5Zue5pePBuiXj%2BaXjwznu7TlkL7lsJTml48G6IuX5pePBuW9neaXjwblo67ml48J5biD5L6d5pePCeacnemynOaXjwbmu6Hml48G5L6X5pePBueRtuaXjwbnmb3ml48J5Zyf5a625pePCeWTiOWwvOaXjwzlk4jokKjlhYvml48G5YKj5pePBum7juaXjwnlgojlg7Pml48G5L2k5pePBueVsuaXjwnpq5jlsbHml48J5ouJ56Wc5pePBuawtOaXjwnkuJzkuaHml48J57qz6KW%2F5pePCeaZr%2Bmih%2BaXjw%2Fmn6%2FlsJTlhYvlrZzml48G5Zyf5pePDOi%2BvuaWoeWwlOaXjwnku6vkvazml48G576M5pePCeW4g%2Bacl%2BaXjwnmkpLmi4nml48J5q%2Bb6Zq%2B5pePCeS7oeS9rOaXjwnplKHkvK%2Fml48J6Zi%2F5piM5pePCeaZruexs%2BaXjwzloZTlkInlhYvml48G5oCS5pePD%2BS5jOWtnOWIq%2BWFi%2BaXjwzkv4TnvZfmlq%2Fml48M6YSC5rip5YWL5pePCeW0qem%2BmeaXjwnkv53lronml48J6KOV5Zu65pePBuS6rOaXjwzloZTloZTlsJTml48J54us6b6Z5pePDOmEguS8puaYpeaXjwnotavlk7Lml48J6Zeo5be05pePCeePnuW3tOaXjwnln7ror7rml48G5YW25LuWG%2BWkluWbveihgOe7n%2BS4reWbveexjeS6uuWjqxU6ATEBMgEzATQBNQE2ATcBOAE5AjEwAjExAjEyAjEzAjE0AjE1AjE2AjE3AjE4AjE5AjIwAjIxAjIyAjIzAjI0AjI1AjI2AjI3AjI4AjI5AjMwAjMxAjMyAjMzAjM0AjM1AjM2AjM3AjM4AjM5AjQwAjQxAjQyAjQzAjQ0AjQ1AjQ2AjQ3AjQ4AjQ5AjUwAjUxAjUyAjUzAjU0AjU1AjU2Ajk3Ajk4FCsDOmdnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dkZAIdDxAPFgYfAAUFRGFpTWEfAQUJTWluZ0NoZW5nHwJnZBAVITznurPlhaXnpL7ljLrnrqHnkIbnmoTmlrDlhqDogrrngo7mhJ%2Fmn5PogIXlj4rlhbblkIzkvY%2FkurrlkZge6auY5pq06Zyy5bKX5L2N55qE5LuO5Lia5Lq65ZGYKuWMu%2BeWl%2BacuuaehOWFpemZouaCo%2BiAheWPiuWFtumZquaKpOS6uuWRmCflhbbku5bmnLrlhbPkvIHkuovkuJrljZXkvY3lt6XkvZzkurrlkZgn5bel5L2c546v5aKD5Lq65ZGY5aSN5p2C55qE5LuO5Lia5Lq65ZGYQuaZrumAmuWMu%2BeWl%2BacuuaehO%2B8iOmZpOWPkeeDremXqOiviuWklu%2B8ieOAgeiNr%2BW6l%2BW3peS9nOS6uuWRmOetiSHnlJ%2Fkuqfovabpl7Tlkozlu7rnrZHlt6XlnLDkurrlkZgz5bmy6K2m6IGM5bel44CB5pyN5Yqh5L%2Bd6Zqc5Lq65ZGY5ZKM6KKr55uR566h5Lq65ZGYNuacjeWKoeawkeaUv%2BacjeWKoeWvueixoeeahOW3peS9nOS6uuWRmOWSjOacjeWKoeWvueixoRXluIjnlJ%2Flkozlt6XkvZzkurrlkZgn6L%2Bb5Y%2Bj6Z2e5Ya36ZO%2B5L2N55qE5YW25LuW5bel5L2c5Lq65ZGYMOi%2Fm%2BWPo%2BWGt%2BmTvumXreeOr%2BeuoeeQhuS6uuWRmOacjeWKoeS%2FnemanOS6uuWRmDDpm4bkuK3pmpTnprvlnLrmiYDkurrlkZjlpJblm7TmnI3liqHkv53pmpzkurrlkZgY5Y%2Bj5bK4566h55CG5pyN5Yqh5Lq65ZGYKuebtOaOpeaOpeinpuWbvemZhemCruS7tuW%2Fq%2BS7tuW3peS9nOS6uuWRmDPnm7TmjqXmjqXop6blhaXlooPkurrlkZjlkoznianlk4HnmoTkuIDnur%2FkurrlkZjnrYke5Y%2Bj5bK46L%2Bb5Y%2Bj54mp5ZOB5pCs6L%2BQ5Lq65ZGYNui3qOWig%2BS6pOmAmuW3peWFt%2BeahOWPuOS5mOOAgeS%2Fnea0geOAgee7tOS%2FruetieS6uuWRmDDlooPlpJbmnaXmlpnliqDlt6XjgIHov5DovpPnrYnkvIHkuJrlt6XkvZzkurrlkZhO6L%2Bc5rSL5rC05Lqn5o2V5o2e44CB6L%2BQ6L6T44CB5LuT5YKo44CB5Yqg5bel5ZKM56CB5aS06L%2BQ6JCl5LyB5Lia5LuO5Lia5Lq65ZGYEuWGt%2BmTvuebuOWFs%2BS6uuWRmB7pm4bkuK3pmpTnprvlnLrmiYDlt6XkvZzkurrlkZgh5paw5Yag55eF5q%2BS5a6e6aqM5a6k5qOA5rWL5Lq65ZGYKuaZrumAmuWMu%2BeWl%2BacuuaehOWPkeeDremXqOiviuW3peS9nOS6uuWRmB7lrprngrnljLvnlpfmnLrmnoTlt6XkvZzkurrlkZgw6I2v5bqX55m76K6w6LSt5Lmw5L2%2F55So4oCc5Zub57G76I2v5ZOB4oCd5Lq65ZGYY%2BWPkeeDreaCo%2BiAheOAgeWPr%2BeWkeaCo%2BiAheOAgeS4jeaYjuWOn%2BWboOiCuueCjuOAgeS9j%2BmZouaCo%2BiAheS4reS4pemHjeaApeaAp%2BWRvOWQuOmBk%2BaEn%2Bafk%2BeXheS%2BixjkvY7po47pmanljLrlpJbmuqLkurrlkZgY5Lit6aOO6Zmp5Yy65aSW5rqi5Lq65ZGYGOmrmOmjjumZqeWMuuWklua6ouS6uuWRmBLmrKHlr4bliIfmjqXop6bogIUe5a%2BG5YiH5o6l6Kem6ICF44CB5YWl5aKD5Lq65ZGYBuWFtuS7lhUhAjMyAjMxAjMwAjI5AjI4AjI3AjI2AjI1AjI0AjIzAjIyAjIxAjIwAjE5AjE4AjE3AjE2AjE1AjE0AjEzAjEyAjExAjEwATkBOAE3ATYBNQE0ATMBMgExAjMzFCsDIWdnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2RkZCNG6t%2FwTLfIfWYjmIA1t2ihkSpmHY4BLjFzPgQIwtW0&__VIEWSTATEGENERATOR=66CEDE76&__EVENTVALIDATION=%2FwEdAHy0R%2FgOtgCE8mJllQ7C7%2Fk4X9ZLR5rgGocRm4vzvcbSbJ%2FHEXC05jPNYWraiRyooU6wlzXD6U6HCyo44SsC85LkCQn5Qt1eVqs10juWBbE66Ww4PLpNyJogmbbM%2BMT%2FZ9f5rj7FBBBotbrGr3q77D%2FJ8b4IkE6oHqht5eSfKjT%2BqsN3DA15cemMdivrkJvT4C0X66UykHRP8aSDrKFdF6QsNAQn%2FZRuwH119f6QR3BRxmOmQEpOU4PZeSfLxRtQNLCph8Lv2wSiPV%2BxO3Yz%2FJJwthxdVmP3CBA%2B%2FBiW9VV3bPE%2FR7jxGHf%2BHx7QYykGfTK%2F6lPDZXyJlq6lzwUkQ91bznHbQUI%2B1aZppKZCd3EQ4RqTeZngaVVX4TwQ4SDD7gOh37v2WOInk%2BVUCwcVtrxRaUtOCk7%2F6kJVqlWJKZ2TYYfB259azvK1tCAaDCzDDwwROuV4s9hVr6C0RizX6qMWa6RMlP0nkxToh9kU%2BHC2vwTHgxJacW1XlvR%2BjvpOO%2BhuU3iZ7cTlnJu9%2BfNvggqkFq%2B0HBUGpTfCFLxvknzbyAVQiy2sQne571DLwJvmi9GWkijmjCipWTTi1K5gFW0Xm%2BD1Cs6wubU%2BIyIPqtJpT6wOms0SCHf9fzaM%2BQTWatK61Dzyym7VnCSOt5hG6I2FmvkyIYWLgOzepbx5kjut1MYB6Awq9sM2yisdR3oiPtHsIrHKV04PIjRxPI35%2BD3fMRejreHJCw8om4fpFW4AqlEJsnRCYh30Ler5yR3WLiagCkbzBN9av%2FGEL3Uaemii%2FsTW22MOHnr7tuKJnTZEGM%2B9jftdrA8d%2FRSRUy8YljSMLeCFZb1fstXCMon72G42U%2FUxECRFVaTXmK694mQT2MAh7dM1ujGasUb7wzwFzxYPmjE%2FBKbFJdqVv6Y5BtVxrVsg3jpU8OxYhNcefAgaeW2v3pD7KnqYEEewMb%2FFf5fTKjwnUF%2FoxIGI5zcJyUq1S0SgNXLr2%2By%2BR3lM%2BSszgN6nL5SV4ESq%2FExuTJ69dI7pTVlyNkKzuDEa0wW90wc2sFhEric6E6MuvlqSK6el3nTJur5v4T0nMP8rbr9Ii7DB5hxJiNyZxTHMUkJIX55r7Wh5eGbSlINJDaj6zhptgzOjaRs7r82n8nyMKJ%2FV6SfX8ZbYW4vXzE9bp5fwk8mLP2sbtHlN5mZFZIHUf%2BLefcdNB19YPELiFtX%2FeRGF8AdfuNoLOQXMaPbm58basWCY6h%2FiQekvxaygssZaY%2BQIj2nR8mZ9rtqEZXvcPdy%2FBJQ2ZhzwReSq0wLaGvX0Uzo7CQM8VESAqnwKPcj0SkLc4muDVN%2BT9gDQw%2FYddneqCuKZ74RK9Gf%2Bb%2FlNXz3GU%2FzdGxj8bhbShgR7%2BCQw4E4pdEJM3ULXM0aNycy6cYzo9b3zLh9%2B5Omh%2B6H54V9m2MblFcwC8yfTNWW%2BpHgQ%2BBJXYGy8yKslc8eEQasMtskCEf7dZmzrtYEotDvD%2FGmKa6cnQOd8ximQKcBoKuM5o35cUmINR45yMBnF6s6RCeGxEwTFIJBXR3%2Fie9YnIPd4aFiynKeZ3WSPsL7cuAlQUv19qNAnSOqN6l22n9jalwniBmbKk2KoVG7tgF9aZtf8ZV1%2FVRusSGv5LJWptAJUTZ%2B7nvX2gxGokw3DmzeF%2FEDFF2FGxSyWNYPNSHHxQQeGi1A%2FbTHEf0fG%2FMkN%2FaiaIlzkGo7hikOOxRsa%2FmTQrMrYtfwrvTeQChje541qq5mcbnFxyglnMLa5F48IaVM0SQS6293uI59cREiZG7GqgwM5gn0WZt9GCVFzAVwK5gTeknG8k51DPSv84hL9JoCIFgahhPZvrQmbJ8hxLmPytgxqEEdkM%2Bg66%2FFhHeWie3Bkx1v70cTTFY4xTnhBBfVe3%2Bw04YjW0wPJuwJkywivNPVyAl68248I1FT17swNKsMxLWMU8nk77wiASLwsYLk5sa%2BtXPqD51RG3ORJwMhk8S0FK5dNEMfBv1A9JDRQDMbdk45NPr6VqZlEX8CQOkwiJt8%2FSuWciRpmVgRlB7aXqLbb7BXjU506eTSpq4l4jAj%2B2TTIMzawQZaBdnEBTQhhIDHBPjyM8rHWcRtm9%2BUxd7hg4CSkR9Anh8rmXFTwBHUvplWWd2biTVKriC6gm0Ued1c01hSTrHI9%2B%2BSdOLgWB0GxwdoE%2Bjgd6NQkXYVLdeWGcInqp35gx9k6bVtc4n0nUhHuKHr2%2FuEBtgoIpGHtf1hSv9C7jZ6N8SaqOjoy2CDKS8ppWy5BrbwiHMm%2Byo1A0gkfRIShEe9iue5q5Ga10eh8686xADtQ4aGgNy4WhTLCJnG0bi8L0Y9P0SQooC7sG8APwV1BZbsXzlG1gmkIpJj011IbXrz0uMoGTmR9GNdWxeYQhc3NOKtjeRGzYR2PKLfWtSm5i5LkJxykIdpz%2Fsj%2FPv3u13lNFcl1KUT4IaT1ztAuxWaWqeZPWxE3y1WUL7mofsvUs3hAlrQLFXZ6bYQAEI5qBhb125FvdvVk2zVtyPZAvhKgCbbV1F%2FyV5OSBzI7sXxhdGvIFQ549UYut3Pb0G0eQZT5TklLTY9HiG270UAauqmahQzVXtW8Ob3pbKMkF%2FINMARiPXHvFdwO3HduUZfN%2BDvxnwFeFeJ9MIBWR693CxbJmKtAvR5ISaWiq2G4e8LAGlaVoG19QKsTgMcRs5I%3D&hid_yinhangkalist=&hidRoleCode=371703002&hidRoleType=5&hidPro=371703&hidCity=371703002&hidArea=371703002202&hidProName=%E5%AE%9A%E9%99%B6%E5%8C%BA&hidCityName=%E6%BB%A8%E6%B2%B3%E8%A1%97%E9%81%93&hidAreaName=%E4%BB%98%E5%BA%84%E6%9D%91%E5%A7%94%E4%BC%9A&ddlXingBie=1&tbShenFenZhengHao="+sfz+"&province=371703&ShengShiQuNew%24hidPro=&ShengShiQuNew%24hidProName=&city=371703002&ShengShiQuNew%24hidCity=&ShengShiQuNew%24hisCityname=&area=371703002202&ShengShiQuNew%24hidArea=&ShengShiQuNew%24hidAreaname=&tbXingMing="+name+"&ddlMinu=1&ddlRenQun=33&ddlRenYuanZhuangTai=1&ddlFangWu=0&tbShouJiHao="+sjh+"&tbDiZhi="+address+"&ddlIsYingJian=1&tbZhunJiaCheXing="+bz+"&Button1=%E4%BF%9D%E5%AD%98";

            String[] cmds = {"curl", "-X", "POST",
                    "-H", "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,fr;q=0.7",
                    "-H", "pragma: no-cache",
                    "-H", "Cache-Control: max-age=0",
                    "-H", "Connection: keep-alive",
                    // "-H","authorization: bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" ,
                    "-H", "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                    "-H", "Content-Type: application/x-www-form-urlencoded",
                    "-H", "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36",
                    "-H", "Cookie: ASP.NET_SessionId=cpbjm1cfvy3caus553wbirwc; User_PID=5525e79c-f069-4706-a9fe-9092763b0ce8; User_Name=371703002; User_RoleType=5",
                    "-H", "origin: http://************:8691",
                    "-H", "Upgrade-Insecure-Requests: 1",
                    "-H", "referer: http://************:8691/WebAdmin/SiJi/AddNewMsg.aspx",

                    "--data-raw", url,
                    "--compressed", "http://************:8691/WebAdmin/SiJi/AddNewMsg.aspx"};

            System.out.println(JSON.toJSONString(cmds));
            String s1 = execCurl(cmds);
            if(null != s1  && s1.contains("parent.closeAndOpenNew('添加成功','SiJi/SiJiList.aspx')")){
                System.out.println("添加成功"+o.toString());
                o.put("success","添加成功");
                try {
                    FileUtil.appendUtf8String(o.toString()+",",result);
                }catch (IORuntimeException e){
                    //抛出一个运行时异常(直接停止掉程序)
                    throw new RuntimeException("运行时异常",e);
                }
            }else {
                int i = s1.indexOf("//<![CDATA[\n" +
                        "alert('");
                if(i!=-1){
                    String substring = s1.substring(i + 19, i + 50);
                    int i1 = substring.indexOf("');");
                    //System.out.println(substring.substring(0,i1));
                    o.put("error",substring.substring(0,i1));
                    errJsonObjectList.add(o);
                }else {
                    o.put("error","添加失败");
                    errJsonObjectList.add(o);
                }
                try {
                    FileUtil.appendUtf8String(o.toString()+",",result);
                }catch (IORuntimeException e){
                    //抛出一个运行时异常(直接停止掉程序)
                    throw new RuntimeException("运行时异常",e);
                }
            }
            try {
                Thread.sleep(50L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });
        excel(errJsonObjectList);
    }


    public static String execCurl(String[] cmds) {
        ProcessBuilder process = new ProcessBuilder(cmds);
        Process p;
        try {
            p = process.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(p.getInputStream()));
            StringBuilder builder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
                builder.append(System.getProperty("line.separator"));
            }
            return builder.toString();

        } catch (IOException e) {
            System.out.print("error");
            e.printStackTrace();
        }
        return null;
    }


    public static void excel(Iterable<?> rowData ){

        // 通过工具类创建writer
        String path1 = epath+"bigWriteMapTest2222223000.xlsx";
        FileUtil.del(path1);
        BigExcelWriter writer = ExcelUtil.getBigWriter(path1);
        // 一次性写出内容，使用默认样式
        writer.writeRow(rowData, true);
        // 关闭writer，释放内存
        writer.close();
    }

}
