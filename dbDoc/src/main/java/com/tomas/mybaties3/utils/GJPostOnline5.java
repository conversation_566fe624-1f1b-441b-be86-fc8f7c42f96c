package com.tomas.mybaties3.utils;

import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * Test 描述
 *
 * <AUTHOR>
 * @create 2021/7/28
 **/
public class GJPostOnline5 {
    public static String epath="/Users/<USER>/code/github/SpringBoot-Learning/dbDoc/src/test/java/com/tomas/mybaties3/testDemo/" ;
    public static void main(String[] args) throws IOException, InterruptedException {

        String a=new StringBuffer("packagecom.cowell.marketing.domain.promotion;importcn.afterturn.easypoi.excel.annotation.Excel;importcom.alibaba.excel.annotation.ExcelProperty;importlombok.Data;importorg.apache.commons.lang.builder.ToStringBuilder;importjava.io.Serializable;importjava.math.BigDecimal;importjava.util.Date;importjava.util.Set;/***导入实体类*@authorhhyangzhongqi*@desc2023-03-14说明@ExcelProperty注解是使用easyExcel注解解决,easyPoi导入总出现oom情况,目前先保持两个注解后续可考虑去除@Excel注解*/@DatapublicclassPromotionExcelimplementsSerializable{/***商品编码*/@Excel(name=\"*商品编码\")@ExcelProperty(value=\"*商品编码\")privateStringgoodsCode;/***单笔订单条件商品要求数量*/@Excel(name=\"单笔订单条件商品要求数量\")@ExcelProperty(value=\"单笔订单条件商品要求数量\")privateIntegerorderRequireCount;/***单笔订单条件商品要求金额*/@Excel(name=\"单笔订单条件商品要求金额\")@ExcelProperty(value=\"单笔订单条件商品要求金额\")privateBigDecimalorderRequireAmount;/***单店限购数量*/@Excel(name=\"单店限购数量\")@ExcelProperty(value=\"单店限购数量\")privateIntegerstoreOverloadCount;/***单日单门店限购数量*/@Excel(name=\"单日单门店限购数量\")@ExcelProperty(value=\"单日单门店限购数量\")privateIntegerdayStoreOverloadCount;/***单日单会员限购数量*/@Excel(name=\"单日单会员限购数量\")@ExcelProperty(value=\"单日单会员限购数量\")privateIntegerdayUserOverloadCount;/***单会员限购数量*/@Excel(name=\"单会员限购数量\")@ExcelProperty(value=\"单会员限购数量\")privateIntegeruserOverloadCount;/***单笔订单限购数量*/@Excel(name=\"单笔订单限购数量\")@ExcelProperty(value=\"单笔订单限购数量\")privateIntegerorderOverloadCount;/***限购总量*/@Excel(name=\"限购总量\")@ExcelProperty(value=\"限购总量\")privateIntegeroverloadCount;/***限购开始时间*/@Excel(name=\"限购开始时间\")@ExcelProperty(value=\"限购开始时间\")privateDatestartOverloadDate;/***限购结束时间*/@Excel(name=\"限购结束时间\")@ExcelProperty(value=\"限购结束时间\")privateDateendOverloadDate;/***非限购价格选择1录入,对应值为non_overload_price2原价(非会员取零售价,会员取会员价)3零售价*/@Excel(name=\"*非限购价格选择\")@ExcelProperty(value=\"*非限购价格选择\")privateIntegernonOverloadPriceType;/***非限购价格*/@Excel(name=\"非限购价格\")@ExcelProperty(value=\"非限购价格\")privateBigDecimalnonOverloadPrice;/***超量购买计价方式*/@Excel(name=\"*超量购买计价方式\")@ExcelProperty(value=\"*超量购买计价方式\")privateIntegeroverPurchase;/***扣减类型0整单立减1按件立减*/@Excel(name=\"扣减类型\")@ExcelProperty(value=\"扣减类型\")privateIntegerreduceType;/***返利模式*/@Excel(name=\"返利模式\")@ExcelProperty(value=\"返利模式\")privateIntegerrebateModel;/***单件返利金额(元)*/@Excel(name=\"单件返利金额\")@ExcelProperty(value=\"单件返利金额\")privateBigDecimalrebateAmount;/***单份返利金额(元)*/@Excel(name=\"单份返利金额\")@ExcelProperty(value=\"单份返利金额\")privateBigDecimalsingleRebateAmount;/***返利协议号*/@Excel(name=\"返利协议号\")@ExcelProperty(value=\"返利协议号\")privateStringrebateNum;/***返利厂家/供应商*/@Excel(name=\"返利厂家/供应商\")@ExcelProperty(value=\"返利厂家/供应商\")privateStringrebateFactory;/***入组数量*/@Excel(name=\"*入组数量\")@ExcelProperty(value=\"*入组数量\")privateIntegergroupNum;/***必选目录*/@Excel(name=\"*必选目录\")@ExcelProperty(value=\"*必选目录\")privateIntegerconditionGoods;/***单组限购数量*/@Excel(name=\"单组限购数量\")@ExcelProperty(value=\"单组限购数量\")privateIntegersingleGroupCount;/***1档满额(元)/第X件/满量(件)*/@Excel(name=\"*1档满额(元)/第X件/满量(件)\")@ExcelProperty(value=\"*1档满额(元)/第X件/满量(件)\")privateBigDecimalfristGear;/***1档折扣*/@Excel(name=\"*1档折扣\")@ExcelProperty(value=\"*1档折扣\")privateBigDecimalfristDiscount;/***1档会员折扣*/@Excel(name=\"1档会员折扣\")@ExcelProperty(value=\"1档会员折扣\")privateBigDecimalfristMemberDiscount;/***1档立减现金(元)*/@Excel(name=\"*1档立减现金(元)\")@ExcelProperty(value=\"*1档立减现金(元)\")privateBigDecimalfristAmount;/***1档会员立减现金(元)*/@Excel(name=\"1档会员立减现金(元)\")@ExcelProperty(value=\"1档会员立减现金(元)\")privateBigDecimalfristMemberAmount;/***1档价格选择1输入2零售价*/@Excel(name=\"*1档价格选择\")@ExcelProperty(value=\"*1档价格选择\")privateIntegerfristSpecialType;/***1档会员价格选择1输入2会员价*/@Excel(name=\"*1档会员价格选择\")@ExcelProperty(value=\"*1档会员价格选择\")privateIntegerfristSpecialMemberType;/***1档特价(元)*/@Excel(name=\"1档特价(元)\")@ExcelProperty(value=\"1档特价(元)\")privateBigDecimalfristPrice;/***1档会员特价(元)*/@Excel(name=\"1档会员特价(元)\")@ExcelProperty(value=\"1档会员特价(元)\")privateBigDecimalfristMemberPrice;/***1档赠品选择方式*/@Excel(name=\"*1档赠品选择方式\")@ExcelProperty(value=\"*1档赠品选择方式\")privateIntegerfristGiftOption;/***1档加价品选择方式*/@Excel(name=\"*1档加价品选择方式\")@ExcelProperty(value=\"*1档加价品选择方式\")privateIntegerfristIncreaseOption;/***1档赠品件/份数*/@Excel(name=\"1档赠品/加价品件/份数\")@ExcelProperty(value=\"1档赠品/加价品件/份数\")privateIntegerfristGiftNum;@Excel(name=\"1档加价购加价价格\")@ExcelProperty(value=\"1档加价购加价价格\")privateBigDecimalfristAddPrice;@Excel(name=\"1档单件兑换积分\")@ExcelProperty(value=\"1档单件兑换积分\")privateBigDecimalfristIncreasePoint;/***2档满额(元)/第X件/满量(件)*/@Excel(name=\"2档满额(元)/第X件/满量(件)\")@ExcelProperty(value=\"2档满额(元)/第X件/满量(件)\")privateBigDecimalsecondGear;/***2档折扣*/@Excel(name=\"2档折扣\")@ExcelProperty(value=\"2档折扣\")privateBigDecimalsecondDiscount;/***2档会员折扣*/@Excel(name=\"2档会员折扣\")@ExcelProperty(value=\"2档会员折扣\")privateBigDecimalsecondMemberDiscount;/***2档价格选择1输入2零售价*/@Excel(name=\"2档价格选择\")@ExcelProperty(value=\"2档价格选择\")privateIntegersecondSpecialType;/***2档会员价格选择1输入2会员价*/@Excel(name=\"2档会员价格选择\")@ExcelProperty(value=\"2档会员价格选择\")privateIntegersecondSpecialMemberType;/***2档特价(元)*/@Excel(name=\"2档特价(元)\")@ExcelProperty(value=\"2档特价(元)\")privateBigDecimalsecondPrice;/***2档会员特价(元)*/@Excel(name=\"2档会员特价(元)\")@ExcelProperty(value=\"2档会员特价(元)\")privateBigDecimalsecondMemberPrice;/***2档赠品选择方式*/@Excel(name=\"2档赠品选择方式\")@ExcelProperty(value=\"2档赠品选择方式\")privateIntegersecondGiftOption;/***2档加价品选择方式*/@Excel(name=\"2档加价品选择方式\")@ExcelProperty(value=\"2档加价品选择方式\")privateIntegersecondIncreaseOption;/***2档立减现金(元)*/@Excel(name=\"2档立减现金(元)\")@ExcelProperty(value=\"2档立减现金(元)\")privateBigDecimalsecondAmount;/***2档会员立减现金(元)*/@Excel(name=\"2档会员立减现金(元)\")@ExcelProperty(value=\"2档会员立减现金(元)\")privateBigDecimalsecondMemberAmount;/***2档赠品件/份数*/@Excel(name=\"2档赠品/加价品件/份数\")@ExcelProperty(value=\"2档赠品/加价品件/份数\")privateIntegersecondGiftNum;@Excel(name=\"2档单件兑换积分\")@ExcelProperty(value=\"2档单件兑换积分\")privateBigDecimalsecondIncreasePoint;@Excel(name=\"2档加价购加价价格\")@ExcelProperty(value=\"2档加价购加价价格\")privateBigDecimalsecondAddPrice;/***3档满额(元)/第X件/满量(件)*/@Excel(name=\"3档满额(元)/第X件/满量(件)\")@ExcelProperty(value=\"3档满额(元)/第X件/满量(件)\")privateBigDecimalthirdGear;/***3档折扣*/@Excel(name=\"3档折扣\")@ExcelProperty(value=\"3档折扣\")privateBigDecimalthirdDiscount;/***3档会员折扣*/@Excel(name=\"3档会员折扣\")@ExcelProperty(value=\"3档会员折扣\")privateBigDecimalthirdMemberDiscount;/***3档价格选择1输入2零售价*/@Excel(name=\"3档价格选择\")@ExcelProperty(value=\"3档价格选择\")privateIntegerthirdSpecialType;/***3档会员价格选择1输入2会员价*/@Excel(name=\"3档会员价格选择\")@ExcelProperty(value=\"3档会员价格选择\")privateIntegerthirdSpecialMemberType;/***3档特价(元)*/@Excel(name=\"3档特价(元)\")@ExcelProperty(value=\"3档特价(元)\")privateBigDecimalthirdPrice;/***3档会员特价(元)*/@Excel(name=\"3档会员特价(元)\")@ExcelProperty(value=\"3档会员特价(元)\")privateBigDecimalthirdMemberPrice;/***3档赠品选择方式*/@Excel(name=\"3档赠品选择方式\")@ExcelProperty(value=\"3档赠品选择方式\")privateIntegerthirdGiftOption;/***3档加价品选择方式*/@Excel(name=\"3档加价品选择方式\")@ExcelProperty(value=\"3档加价品选择方式\")privateIntegerthirdIncreaseOption;/***3档立减现金(元)*/@Excel(name=\"3档立减现金(元)\")@ExcelProperty(value=\"3档立减现金(元)\")privateBigDecimalthirdAmount;/***3档会员立减现金(元)*/@Excel(name=\"3档会员立减现金(元)\")@ExcelProperty(value=\"3档会员立减现金(元)\")privateBigDecimalthirdMemberAmount;/***3档赠品件/份数*/@Excel(name=\"3档赠品/加价品件/份数\")@ExcelProperty(value=\"3档赠品/加价品件/份数\")privateIntegerthirdGiftNum;@Excel(name=\"3档单件兑换积分\")@ExcelProperty(value=\"3档单件兑换积分\")privateBigDecimalthirdIncreasePoint;@Excel(name=\"3档加价购加价价格\")@ExcelProperty(value=\"3档加价购加价价格\")privateBigDecimalthirdAddPrice;/***4档满额(元)/第X件/满量(件)*/@Excel(name=\"4档满额(元)/第X件/满量(件)\")@ExcelProperty(value=\"4档满额(元)/第X件/满量(件)\")privateBigDecimalfourthGear;/***4档折扣*/@Excel(name=\"4档折扣\")@ExcelProperty(value=\"4档折扣\")privateBigDecimalfourthDiscount;/***4档会员折扣*/@Excel(name=\"4档会员折扣\")@ExcelProperty(value=\"4档会员折扣\")privateBigDecimalfourthMemberDiscount;/***4档价格选择1输入2零售价*/@Excel(name=\"4档价格选择\")@ExcelProperty(value=\"4档价格选择\")privateIntegerfourthSpecialType;/***4档会员价格选择1输入2会员价*/@Excel(name=\"4档会员价格选择\")@ExcelProperty(value=\"4档会员价格选择\")privateIntegerfourthSpecialMemberType;/***4档特价(元)*/@Excel(name=\"4档特价(元)\")@ExcelProperty(value=\"4档特价(元)\")privateBigDecimalfourthPrice;/***4档会员特价(元)*/@Excel(name=\"4档会员特价(元)\")@ExcelProperty(value=\"4档会员特价(元)\")privateBigDecimalfourthMemberPrice;/***4档赠品选择方式*/@Excel(name=\"4档赠品选择方式\")@ExcelProperty(value=\"4档赠品选择方式\")privateIntegerfourthGiftOption;/***4档立减现金(元)*/@Excel(name=\"4档立减现金(元)\")@ExcelProperty(value=\"4档立减现金(元)\")privateBigDecimalfourthAmount;/***4档会员立减现金(元)*/@Excel(name=\"4档会员立减现金(元)\")@ExcelProperty(value=\"4档会员立减现金(元)\")privateBigDecimalfourthMemberAmount;/***4档加价品选择方式*/@Excel(name=\"4档加价品选择方式\")@ExcelProperty(value=\"4档加价品选择方式\")privateIntegerfourthIncreaseOption;/***4档赠品件/份数*/@Excel(name=\"4档赠品/加价品件/份数\")@ExcelProperty(value=\"4档赠品/加价品件/份数\")privateIntegerfourthGiftNum;@Excel(name=\"4档加价购加价价格\")@ExcelProperty(value=\"4档加价购加价价格\")privateBigDecimalfourthAddPrice;@Excel(name=\"4档单件兑换积分\")@ExcelProperty(value=\"4档单件兑换积分\")privateBigDecimalfourthIncreasePoint;/***5档满额(元)/第X件/满量(件)*/@Excel(name=\"5档满额(元)/第X件/满量(件)\")@ExcelProperty(value=\"5档满额(元)/第X件/满量(件)\")privateBigDecimalfifthGear;/***5档折扣*/@Excel(name=\"5档折扣\")@ExcelProperty(value=\"5档折扣\")privateBigDecimalfifthDiscount;/***5档会员折扣*/@Excel(name=\"5档会员折扣\")@ExcelProperty(value=\"5档会员折扣\")privateBigDecimalfifthMemberDiscount;/***5档价格选择1输入2零售价*/@Excel(name=\"5档价格选择\")@ExcelProperty(value=\"5档价格选择\")privateIntegerfifthSpecialType;/***5档会员价格选择1输入2会员价*/@Excel(name=\"5档会员价格选择\")@ExcelProperty(value=\"5档会员价格选择\")privateIntegerfifthSpecialMemberType;/***5档特价(元)*/@Excel(name=\"5档特价(元)\")@ExcelProperty(value=\"5档特价(元)\")privateBigDecimalfifthPrice;/***5档会员特价(元)*/@Excel(name=\"5档会员特价(元)\")@ExcelProperty(value=\"5档会员特价(元)\")privateBigDecimalfifthMemberPrice;/***5档加价品选择方式*/@Excel(name=\"5档加价品选择方式\")@ExcelProperty(value=\"5档加价品选择方式\")privateIntegerfifthIncreaseOption;/***5档立减现金(元)*/@Excel(name=\"5档立减现金(元)\")@ExcelProperty(value=\"5档立减现金(元)\")privateBigDecimalfifthAmount;/***5档会员立减现金(元)*/@Excel(name=\"5档会员立减现金(元)\")@ExcelProperty(value=\"5档会员立减现金(元)\")privateBigDecimalfifthMemberAmount;/***5档赠品选择方式*/@Excel(name=\"5档赠品选择方式\")@ExcelProperty(value=\"5档赠品选择方式\")privateIntegerfifthGiftOption;/***5档赠品件/份数*/@Excel(name=\"5档赠品/加价品件/份数\")@ExcelProperty(value=\"5档赠品/加价品件/份数\")privateIntegerfifthGiftNum;@Excel(name=\"5档加价购加价价格\")@ExcelProperty(value=\"5档加价购加价价格\")privateBigDecimalfifthAddPrice;@Excel(name=\"5档单件兑换积分\")@ExcelProperty(value=\"5档单件兑换积分\")privateBigDecimalfifthIncreasePoint;/***6档满额(元)/第X件/满量(件)*/@Excel(name=\"6档满额(元)/第X件/满量(件)\")@ExcelProperty(value=\"6档满额(元)/第X件/满量(件)\")privateBigDecimalsixthGear;/***6档价格选择1输入2零售价*/@Excel(name=\"6档价格选择\")@ExcelProperty(value=\"6档价格选择\")privateIntegersixthSpecialType;/***6档会员价格选择1输入2会员价*/@Excel(name=\"6档会员价格选择\")@ExcelProperty(value=\"6档会员价格选择\")privateIntegersixthSpecialMemberType;/***6档特价(元)*/@Excel(name=\"6档特价(元)\")@ExcelProperty(value=\"6档特价(元)\")privateBigDecimalsixthPrice;/***6档会员特价(元)*/@Excel(name=\"6档会员特价(元)\")@ExcelProperty(value=\"6档会员特价(元)\")privateBigDecimalsixthMemberPrice;/***6档立减现金(元)*/@Excel(name=\"6档立减现金(元)\")@ExcelProperty(value=\"6档立减现金(元)\")privateBigDecimalsixthAmount;/***6档会员立减现金(元)*/@Excel(name=\"6档会员立减现金(元)\")@ExcelProperty(value=\"6档会员立减现金(元)\")privateBigDecimalsixthMemberAmount;@Excel(name=\"6档单件兑换积分\")@ExcelProperty(value=\"6档单件兑换积分\")privateBigDecimalsixthIncreasePoint;/***7档满额(元)/第X件/满量(件)*/@Excel(name=\"7档满额(元)/第X件/满量(件)\")@ExcelProperty(value=\"7档满额(元)/第X件/满量(件)\")privateBigDecimalseventhGear;/***7档价格选择1输入2零售价*/@Excel(name=\"7档价格选择\")@ExcelProperty(value=\"7档价格选择\")privateIntegerseventhSpecialType;/***7档会员价格选择1输入2会员价*/@Excel(name=\"7档会员价格选择\")@ExcelProperty(value=\"7档会员价格选择\")privateIntegerseventhSpecialMemberType;/***7档特价(元)*/@Excel(name=\"7档特价(元)\")@ExcelProperty(value=\"7档特价(元)\")privateBigDecimalseventhPrice;/***7档会员特价(元)*/@Excel(name=\"7档会员特价(元)\")@ExcelProperty(value=\"7档会员特价(元)\")privateBigDecimalseventhMemberPrice;/***7档立减现金(元)*/@Excel(name=\"7档立减现金(元)\")@ExcelProperty(value=\"7档立减现金(元)\")privateBigDecimalseventhAmount;/***7档会员立减现金(元)*/@Excel(name=\"7档会员立减现金(元)\")@ExcelProperty(value=\"7档会员立减现金(元)\")privateBigDecimalseventhMemberAmount;@Excel(name=\"7档单件兑换积分\")@ExcelProperty(value=\"7档单件兑换积分\")privateBigDecimalseventhIncreasePoint;/***8档满额(元)/第X件/满量(件)*/@Excel(name=\"8档满额(元)/第X件/满量(件)\")@ExcelProperty(value=\"8档满额(元)/第X件/满量(件)\")privateBigDecimaleighthGear;/***8档价格选择1输入2零售价*/@Excel(name=\"8档价格选择\")@ExcelProperty(value=\"8档价格选择\")privateIntegereighthSpecialType;/***8档会员价格选择1输入2会员价*/@Excel(name=\"8档会员价格选择\")@ExcelProperty(value=\"8档会员价格选择\")privateIntegereighthSpecialMemberType;/***8档特价(元)*/@Excel(name=\"8档特价(元)\")@ExcelProperty(value=\"8档特价(元)\")privateBigDecimaleighthPrice;/***8档会员特价(元)*/@Excel(name=\"8档会员特价(元)\")@ExcelProperty(value=\"8档会员特价(元)\")privateBigDecimaleighthMemberPrice;/***8档立减现金(元)*/@Excel(name=\"8档立减现金(元)\")@ExcelProperty(value=\"8档立减现金(元)\")privateBigDecimaleighthAmount;/***8档会员立减现金(元)*/@Excel(name=\"8档会员立减现金(元)\")@ExcelProperty(value=\"8档会员立减现金(元)\")privateBigDecimaleighthMemberAmount;@Excel(name=\"8档单件兑换积分\")@ExcelProperty(value=\"8档单件兑换积分\")privateBigDecimaleighthIncreasePoint;/***9档满额(元)/第X件/满量(件)*/@Excel(name=\"9档满额(元)/第X件/满量(件)\")@ExcelProperty(value=\"9档满额(元)/第X件/满量(件)\")privateBigDecimalninthGear;/***9档价格选择1输入2零售价*/@Excel(name=\"9档价格选择\")@ExcelProperty(value=\"9档价格选择\")privateIntegerninthSpecialType;/***9档会员价格选择1输入2会员价*/@Excel(name=\"9档会员价格选择\")@ExcelProperty(value=\"9档会员价格选择\")privateIntegerninthSpecialMemberType;/***9档特价(元)*/@Excel(name=\"9档特价(元)\")@ExcelProperty(value=\"9档特价(元)\")privateBigDecimalninthPrice;/***9档会员特价(元)*/@Excel(name=\"9档会员特价(元)\")@ExcelProperty(value=\"9档会员特价(元)\")privateBigDecimalninthMemberPrice;/***9档立减现金(元)*/@Excel(name=\"9档立减现金(元)\")@ExcelProperty(value=\"9档立减现金(元)\")privateBigDecimalninthAmount;/***9档会员立减现金(元)*/@Excel(name=\"9档会员立减现金(元)\")@ExcelProperty(value=\"9档会员立减现金(元)\")privateBigDecimalninthMemberAmount;@Excel(name=\"9档单件兑换积分\")@ExcelProperty(value=\"9档单件兑换积分\")privateBigDecimalninthIncreasePoint;/***10档满额(元)/第X件/满量(件)*/@Excel(name=\"10档满额(元)/第X件/满量(件)\")@ExcelProperty(value=\"10档满额(元)/第X件/满量(件)\")privateBigDecimaltenthGear;/***10档价格选择1输入2零售价*/@Excel(name=\"10档价格选择\")@ExcelProperty(value=\"10档价格选择\")privateIntegertenthSpecialType;/***10档会员价格选择1输入2会员价*/@Excel(name=\"10档会员价格选择\")@ExcelProperty(value=\"10档会员价格选择\")privateIntegertenthSpecialMemberType;/***10档特价(元)*/@Excel(name=\"10档特价(元)\")@ExcelProperty(value=\"10档特价(元)\")privateBigDecimaltenthPrice;/***10档会员特价(元)*/@Excel(name=\"10档会员特价(元)\")@ExcelProperty(value=\"10档会员特价(元)\")privateBigDecimaltenthMemberPrice;/***10档立减现金(元)*/@Excel(name=\"10档立减现金(元)\")@ExcelProperty(value=\"10档立减现金(元)\")privateBigDecimaltenthAmount;/***10档会员立减现金(元)*/@Excel(name=\"10档会员立减现金(元)\")@ExcelProperty(value=\"10档会员立减现金(元)\")privateBigDecimaltenthMemberAmount;@Excel(name=\"10档单件兑换积分\")@ExcelProperty(value=\"10档单件兑换积分\")privateBigDecimaltenthIncreasePoint;/***促销折扣方式*/@Excel(name=\"*促销折扣方式\")@ExcelProperty(value=\"*促销折扣方式\")privateIntegerdiscountType;/***促销费用来源*/@Excel(name=\"*促销费用来源\")@ExcelProperty(value=\"*促销费用来源\")privateIntegerresource;/***有效期至*/@Excel(name=\"有效期至\")@ExcelProperty(value=\"有效期至\")privateDatevalidPeriod;/***近效期*/@Excel(name=\"近效期\")@ExcelProperty(value=\"近效期\")privateIntegerexpireDate;@Excel(name=\"单月限购总量\")@ExcelProperty(value=\"单月限购总量\")privateBigDecimalmonthLimitedCount;@Excel(name=\"单周限购总量\")@ExcelProperty(value=\"单周限购总量\")privateBigDecimalweekLimitedCount;@Excel(name=\"单笔订单最小金额\")@ExcelProperty(value=\"单笔订单最小金额\")privateBigDecimalorderLowestAmount;@Excel(name=\"指定天数限购量值\")@ExcelProperty(value=\"指定天数限购量值\")privateBigDecimaldayLimitedCount;@Excel(name=\"指定天数限购天数\")@ExcelProperty(value=\"指定天数限购天数\")privateBigDecimaldayLimit;@Excel(name=\"厂家投入\")@ExcelProperty(value=\"厂家投入\")privateBigDecimalfactoryInvested;@Excel(name=\"加提金额\")@ExcelProperty(value=\"加提金额\")privateBigDecimalrebateAmountDvc;@Excel(name=\"流向要求\")@ExcelProperty(value=\"流向要求\")privateStringrequireFlowStr;@Excel(name=\"参与门店\")@ExcelProperty(value=\"参与门店\")privateStringselectStore;//重复门店privateBooleanrepeatStore;//选择机构privateSet<Long>orgIdList;//选择机构的全链路privateSet<Long>selectAllOrgIdList;//行级别设置的门店数据类型privateIntegerlineStoreType;@Excel(name=\"券模板编码\")@ExcelProperty(value=\"券模板编码\")privateStringsingleCouponTempId;@Excel(name=\"券模板获取方式\")@ExcelProperty(value=\"券模板获取方式\")privateIntegercouponTempMethod;@Excel(name=\"是否必须有券\")@ExcelProperty(value=\"是否必须有券\")privateIntegercouponTempIdMandatory;@Excel(name=\"1档指定赠品列表\")@ExcelProperty(value=\"1档指定赠品列表\")privateStringfirstGiftListString;@Excel(name=\"1档指定加价品列表\")@ExcelProperty(value=\"1档指定加价品列表\")privateStringfirstIncreaseListString;@Excel(name=\"1档单循环限购种数\")@ExcelProperty(value=\"1档单循环限购种数\")privateIntegerfirstGiftLimitKind;@Excel(name=\"1档单循环限购数量\")@ExcelProperty(value=\"1档单循环限购数量\")privateIntegerfirstIncreaseLimit;@Excel(name=\"2档指定赠品列表\")@ExcelProperty(value=\"2档指定赠品列表\")privateStringsecondGiftListString;@Excel(name=\"2档指定加价品列表\")@ExcelProperty(value=\"2档指定加价品列表\")privateStringsecondIncreaseListString;@Excel(name=\"2档单循环限购种数\")@ExcelProperty(value=\"2档单循环限购种数\")privateIntegersecondGiftLimitKind;@Excel(name=\"2档单循环限购数量\")@ExcelProperty(value=\"2档单循环限购数量\")privateIntegersecondIncreaseLimit;@Excel(name=\"3档指定赠品列表\")@ExcelProperty(value=\"3档指定赠品列表\")privateStringthirdGiftListString;@Excel(name=\"3档指定加价品列表\")@ExcelProperty(value=\"3档指定加价品列表\")privateStringthirdIncreaseListString;@Excel(name=\"3档单循环限购种数\")@ExcelProperty(value=\"3档单循环限购种数\")privateIntegerthirdGiftLimitKind;@Excel(name=\"3档单循环限购数量\")@ExcelProperty(value=\"3档单循环限购数量\")privateIntegerthirdIncreaseLimit;@Excel(name=\"4档指定赠品列表\")@ExcelProperty(value=\"4档指定赠品列表\")privateStringfourthGiftListString;@Excel(name=\"4档指定加价品列表\")@ExcelProperty(value=\"4档指定加价品列表\")privateStringfourthIncreaseListString;@Excel(name=\"4档单循环限购种数\")@ExcelProperty(value=\"4档单循环限购种数\")privateIntegerfourthGiftLimitKind;@Excel(name=\"4档单循环限购数量\")@ExcelProperty(value=\"4档单循环限购数量\")privateIntegerfourthIncreaseLimit;@Excel(name=\"5档指定赠品列表\")@ExcelProperty(value=\"5档指定赠品列表\")privateStringfifthGiftListString;@Excel(name=\"5档指定加价品列表\")@ExcelProperty(value=\"5档指定加价品列表\")privateStringfifthIncreaseListString;@Excel(name=\"5档单循环限购种数\")@ExcelProperty(value=\"5档单循环限购种数\")privateIntegerfifthGiftLimitKind;@Excel(name=\"5档单循环限购数量\")@ExcelProperty(value=\"5档单循环限购数量\")privateIntegerfifthIncreaseLimit;privateStringmessage;privatestaticfinallongserialVersionUID=1L;@OverridepublicStringtoString(){returnToStringBuilder.reflectionToString(this);}}}").toString();

        System.out.println(a);

        Map<String,String> multipleMap = new LinkedHashMap<String,String>(){
            private static final long serialVersionUID = -6514836291678556529L;
            {
        put("goodsCode","*商品编码");
        put("goodsName", "商品名称");
        put("goodsSpec", "规格");
        put("produceFactory", "生产厂家");
        put("goodsBrand", "商品品牌");
        put("goodsCategory", "商品分类");
        put("curName", "通用名");
        put("drink","是否饮品");
        put("retailPrice", "最新零售价");
        put("memberPrice", "最新会员价");
        put("conditionGoods", "*必选目录");
        put("discountType","*促销折扣方式");
        put("resource","*促销费用来源");
            put("goodsCode","*商品编码");
            put("goodsName", "商品名称");
            put("goodsSpec", "规格");
            put("produceFactory", "生产厂家");
            put("goodsBrand", "商品品牌");
            put("goodsCategory", "商品分类");
            put("curName", "通用名");
            put("retailPrice", "最新零售价");
            put("memberPrice", "最新会员价");
            put("conditionGoods", "*必选目录");
            put("discountType","*促销折扣方式");
            put("resource","*促销费用来源");
            put("message","错误信息");


                    put("goodsCode","*商品编码");
                    put("goodsName", "商品名称");
                    put("goodsSpec", "规格");
                    put("produceFactory", "生产厂家");
                    put("goodsBrand", "商品品牌");
                    put("goodsCategory", "商品分类");
                    put("curName", "通用名");
                    put("retailPrice", "最新零售价");
                    put("memberPrice", "最新会员价");
                    put("conditionGoods", "*必选目录");
                    put("discountType","*促销折扣方式");
                    put("resource","*促销费用来源");
                    put("profit","毛利率");

                    put("goodsCode","*商品编码");
                    put("goodsName", "商品名称");
                    put("goodsSpec", "规格");
                    put("produceFactory", "生产厂家");
                    put("goodsBrand", "商品品牌");
                    put("goodsCategory", "商品分类");
                    put("curName", "通用名");
                    put("drink","是否饮品");
                    //促销凭证化 新增导出字段 begin
                    put("selectStore","参与门店");
                    put("singleCouponTempId","券模板编码");
                    put("couponTempMethod","券模板获取方式");
                    put("couponTempIdMandatory", "是否必须有券");
                    //end
                    put("retailPrice", "最新零售价");
                    put("memberPrice", "最新会员价");
                    put("overPurchase", "*超量购买计价方式");


                    put("fristGear", "*1档满额(元)/第X件/满量(件)");
                    put("fristSpecialType", "*1档价格选择");
                    put("fristPrice","1档特价(元)");
                    put("fristSpecialMemberType", "*1档会员价格选择");
                    put("fristMemberPrice", "1档会员特价(元)");
                    put("fristLowestCalculateRate","1档计算最低折率");
                    put("fristLowestTempleteRate","1档模板最低折率");
                    put("fristLowestMemberCalculateRate","1档计算会员最低折率");
                    put("fristIncreasePoint","1档单件兑换积分");

                    put("secondGear", "2档满额(元)/第X件/满量(件)");
                    put("secondSpecialType", "2档价格选择");
                    put("secondPrice","2档特价(元)");
                    put("secondSpecialMemberType", "2档会员价格选择");
                    put("secondMemberPrice", "2档会员特价(元)");
                    put("secondLowestCalculateRate","2档计算最低折率");
                    put("secondLowestTempleteRate","2档模板最低折率");
                    put("secondLowestMemberCalculateRate","2档计算会员最低折率");
                    put("secondIncreasePoint","2档单件兑换积分");

                    put("thirdGear", "3档满额(元)/第X件/满量(件)");
                    put("thirdSpecialType", "3档价格选择");
                    put("thirdPrice","3档特价(元)");
                    put("thirdSpecialMemberType", "3档会员价格选择");
                    put("thirdMemberPrice", "3档会员特价(元)");
                    put("thirdLowestCalculateRate","3档计算最低折率");
                    put("thirdLowestTempleteRate","3档模板最低折率");
                    put("thirdLowestMemberCalculateRate","3档计算会员最低折率");
                    put("thirdIncreasePoint","3档单件兑换积分");

                    put("fourthGear", "4档满额(元)/第X件/满量(件)");
                    put("fourthSpecialType", "4档价格选择");
                    put("fourthPrice","4档特价(元)");
                    put("fourthSpecialMemberType", "4档会员价格选择");
                    put("fourthMemberPrice", "4档会员特价(元)");
                    put("fourthLowestCalculateRate","4档计算最低折率");
                    put("fourthLowestTempleteRate","4档模板最低折率");
                    put("fourthLowestMemberCalculateRate","4档计算会员最低折率");
                    put("fourthIncreasePoint","4档单件兑换积分");

                    put("fifthGear", "5档满额(元)/第X件/满量(件)");
                    put("fifthSpecialType", "5档价格选择");
                    put("fifthPrice","5档特价(元)");
                    put("fifthSpecialMemberType", "5档会员价格选择");
                    put("fifthMemberPrice","5档会员特价(元)");
                    put("fifthLowestCalculateRate","5档计算最低折率");
                    put("fifthLowestTempleteRate","5档模板最低折率");
                    put("fifthLowestMemberCalculateRate","5档计算会员最低折率");
                    put("fifthIncreasePoint","5档单件兑换积分");

                    put("sixthGear", "6档满额(元)/第X件/满量(件)");
                    put("sixthSpecialType", "6档价格选择");
                    put("sixthPrice","6档特价(元)");
                    put("sixthSpecialMemberType", "6档会员价格选择");
                    put("sixthMemberPrice","6档会员特价(元)");
                    put("sixthLowestCalculateRate","6档计算最低折率");
                    put("sixthLowestTempleteRate","6档模板最低折率");
                    put("sixthLowestMemberCalculateRate","6档计算会员最低折率");
                    put("sixthIncreasePoint","6档单件兑换积分");

                    put("seventhGear", "7档满额(元)/第X件/满量(件)");
                    put("seventhSpecialType", "7档价格选择");
                    put("seventhPrice","7档特价(元)");
                    put("seventhSpecialMemberType", "7档会员价格选择");
                    put("seventhMemberPrice","7档会员特价(元)");
                    put("seventhLowestCalculateRate","7档计算最低折率");
                    put("seventhLowestTempleteRate","7档模板最低折率");
                    put("seventhLowestMemberCalculateRate","7档计算会员最低折率");
                    put("seventhIncreasePoint","7档单件兑换积分");

                    put("eighthGear", "8档满额(元)/第X件/满量(件)");
                    put("eighthSpecialType", "8档价格选择");
                    put("eighthPrice","8档特价(元)");
                    put("eighthSpecialMemberType", "8档会员价格选择");
                    put("eighthMemberPrice","8档会员特价(元)");
                    put("eighthLowestCalculateRate","8档计算最低折率");
                    put("eighthLowestTempleteRate","8档模板最低折率");
                    put("eighthLowestMemberCalculateRate","8档计算会员最低折率");
                    put("eighthIncreasePoint","8档单件兑换积分");

                    put("ninthGear", "9档满额(元)/第X件/满量(件)");
                    put("ninthSpecialType", "9档价格选择");
                    put("ninthPrice","9档特价(元)");
                    put("ninthSpecialMemberType", "9档会员价格选择");
                    put("ninthMemberPrice","9档会员特价(元)");
                    put("ninthLowestCalculateRate","9档计算最低折率");
                    put("ninthLowestTempleteRate","9档模板最低折率");
                    put("ninthLowestMemberCalculateRate","9档计算会员最低折率");
                    put("ninthIncreasePoint","9档单件兑换积分");

                    put("tenthGear", "10档满额(元)/第X件/满量(件)");
                    put("tenthSpecialType", "10档价格选择");
                    put("tenthPrice","10档特价(元)");
                    put("tenthSpecialMemberType", "10档会员价格选择");
                    put("tenthMemberPrice","10档会员特价(元)");
                    put("tenthLowestCalculateRate","10档计算最低折率");
                    put("tenthLowestTempleteRate","10档模板最低折率");
                    put("tenthLowestMemberCalculateRate","10档计算会员最低折率");
                    put("tenthIncreasePoint","10档单件兑换积分");


                    put("monthLimitedCount","单月限购总量");
                    put("weekLimitedCount","单周限购总量");
                    put("orderLowestAmount","单笔订单最小金额");
                    put("dayLimitedCount","指定天数限购量值");
                    put("dayLimit","指定天数限购天数");
                    put("orderRequireCount", "单笔订单条件商品要求数量");
                    put("orderRequireAmount", "单笔订单条件商品要求金额");
                    put("storeOverloadCount", "单店限购数量");
                    put("dayStoreOverloadCount", "单日单门店限购数量");
                    put("dayUserOverloadCount", "单日单会员限购数量");
                    put("userOverloadCount", "单会员限购数量");
                    put("orderOverloadCount", "单笔订单限购数量");
                    put("overloadCount", "限购总量");
                    put("startOverloadDate", "限购开始时间");
                    put("endOverloadDate", "限购结束时间");
                    put("nonOverloadPriceType", "*非限购价格选择");
                    put("nonOverloadPrice", "非限购价格");
                    put("discountType","*促销折扣方式");
                    put("resource","*促销费用来源");
                    put("mostDiscountRate", "最高折率");
                    put("lowestDiscountRate", "最低折率");
                    //put("violateRule", "是否违反规则标记");
                    //put("whiteList", "是否有白名单");
                    //put("hitWhiteList", "是否命中白名单");
                    //put("matchWhiteListId", "匹配白名单ID");
                    //put("blackList", "是否有黑名单");
                    //put("hitBlackList", "是否命中黑名单");
                    //put("matchBlackListId", "匹配黑名单ID");
                    put("templeteId","模板id");
                    put("expireDate","近效期");
                    put("validPeriod","有效期至");
                    put("message","错误信息");

                    put("goodsCode","*商品编码");
                    put("goodsName", "商品名称");
                    put("goodsSpec", "规格");
                    put("produceFactory", "生产厂家");
                    put("goodsBrand", "商品品牌");
                    put("goodsCategory", "商品分类");
                    put("curName", "通用名");
                    put("drink","是否饮品");
                    //促销凭证化 新增导出字段 begin
                    put("selectStore","参与门店");
                    put("singleCouponTempId","券模板编码");
                    put("couponTempMethod","券模板获取方式");
                    put("couponTempIdMandatory", "是否必须有券");
                    //end
                    put("retailPrice", "最新零售价");
                    put("memberPrice", "最新会员价");
                    put("overPurchase", "*超量购买计价方式");
                    put("fristGear", "*1档满额(元)/第X件/满量(件)");
                    put("fristDiscount", "*1档折扣");
                    put("fristMemberDiscount", "1档会员折扣");
                    put("fristLowestCalculateRate","1档计算最低折率");
                    put("fristLowestMemberCalculateRate","1档计算会员最低折率");
                    put("fristLowestTempleteRate","1档模板最低折率");
                    put("fristIncreasePoint","1档单件兑换积分");

                    put("secondGear", "2档满额(元)/第X件/满量(件)");
                    put("secondDiscount", "2档折扣");
                    put("secondMemberDiscount", "2档会员折扣");
                    put("secondLowestCalculateRate","2档计算最低折率");
                    put("secondLowestMemberCalculateRate","2档计算会员最低折率");
                    put("secondLowestTempleteRate","2档模板最低折率");
                    put("secondIncreasePoint","2档单件兑换积分");

                    put("thirdGear", "3档满额(元)/第X件/满量(件)");
                    put("thirdDiscount", "3档折扣");
                    put("thirdMemberDiscount", "3档会员折扣");
                    put("thirdLowestCalculateRate","3档计算最低折率");
                    put("thirdLowestMemberCalculateRate","3档计算会员最低折率");
                    put("thirdLowestTempleteRate","3档模板最低折率");
                    put("thirdIncreasePoint","3档单件兑换积分");

                    put("fourthGear", "4档满额(元)/第X件/满量(件)");
                    put("fourthDiscount", "4档折扣");
                    put("fourthMemberDiscount", "4档会员折扣");
                    put("fourthLowestCalculateRate","4档计算最低折率");
                    put("fourthLowestMemberCalculateRate","4档计算会员最低折率");
                    put("fourthLowestTempleteRate","4档模板最低折率");
                    put("fourthIncreasePoint","4档单件兑换积分");

                    put("fifthGear", "5档满额(元)/第X件/满量(件)");
                    put("fifthDiscount", "5档折扣");
                    put("fifthMemberDiscount", "5档会员折扣");
                    put("fifthLowestCalculateRate","5档计算最低折率");
                    put("fifthLowestMemberCalculateRate","5档计算会员最低折率");
                    put("fifthLowestTempleteRate","5档模板最低折率");
                    put("fifthIncreasePoint","5档单件兑换积分");

                    put("monthLimitedCount","单月限购总量");
                    put("weekLimitedCount","单周限购总量");
                    put("orderLowestAmount","单笔订单最小金额");
                    put("dayLimitedCount","指定天数限购量值");
                    put("dayLimit","指定天数限购天数");
                    put("orderRequireCount", "单笔订单条件商品要求数量");
                    put("orderRequireAmount", "单笔订单条件商品要求金额");
                    put("storeOverloadCount", "单店限购数量");
                    put("dayStoreOverloadCount", "单日单门店限购数量");
                    put("dayUserOverloadCount", "单日单会员限购数量");
                    put("userOverloadCount", "单会员限购数量");
                    put("orderOverloadCount", "单笔订单限购数量");
                    put("overloadCount", "限购总量");
                    put("discountType","*促销折扣方式");
                    put("resource","*促销费用来源");
                    put("mostDiscountRate", "最高折率");
                    put("lowestDiscountRate", "最低折率");
                    //put("violateRule", "是否违反规则标记");
                    //put("whiteList", "是否有白名单");
                    //put("hitWhiteList", "是否命中白名单");
                    //put("matchWhiteListId", "匹配白名单ID");
                    //put("blackList", "是否有黑名单");
                    //put("hitBlackList", "是否命中黑名单");
                    //put("matchBlackListId", "匹配黑名单ID");
                    put("templeteId","模板id");
                    put("expireDate","近效期");
                    put("validPeriod","有效期至");
                    put("message","错误信息");

                    put("goodsCode","*商品编码");
                    put("goodsName", "商品名称");
                    put("goodsSpec", "规格");
                    put("produceFactory", "生产厂家");
                    put("goodsBrand", "商品品牌");
                    put("goodsCategory", "商品分类");
                    put("curName", "通用名");
                    put("drink","是否饮品");
                    //促销凭证化 新增导出字段 begin
                    put("selectStore","参与门店");
                    put("singleCouponTempId","券模板编码");
                    put("couponTempMethod","券模板获取方式");
                    put("couponTempIdMandatory", "是否必须有券");
                    //end
                    put("retailPrice", "最新零售价");
                    put("memberPrice", "最新会员价");
                    put("overPurchase", "*超量购买计价方式");
                    put("reduceType", "扣减类型");
                    put("fristGear", "*1档满额(元)/第X件/满量(件)");
                    put("fristAmount","*1档立减现金(元)");
                    put("fristMemberAmount", "1档会员立减现金(元)");
                    put("fristLowestCalculateRate","1档计算最低折率");
                    put("fristLowestTempleteRate","1档模板最低折率");
                    put("fristLowestMemberCalculateRate","1档计算会员最低折率");
                    put("fristIncreasePoint","1档单件兑换积分");

                    put("secondGear", "2档满额(元)/第X件/满量(件)");
                    put("secondAmount","2档立减现金(元)");
                    put("secondMemberAmount", "2档会员立减现金(元)");
                    put("secondLowestCalculateRate","2档计算最低折率");
                    put("secondLowestTempleteRate","2档模板最低折率");
                    put("secondLowestMemberCalculateRate","2档计算会员最低折率");
                    put("secondIncreasePoint","2档单件兑换积分");

                    put("thirdGear", "3档满额(元)/第X件/满量(件)");
                    put("thirdAmount","3档立减现金(元)");
                    put("thirdMemberAmount", "3档会员立减现金(元)");
                    put("thirdLowestCalculateRate","3档计算最低折率");
                    put("thirdLowestTempleteRate","3档模板最低折率");
                    put("thirdLowestMemberCalculateRate","3档计算会员最低折率");
                    put("thirdIncreasePoint","3档单件兑换积分");

                    put("fourthGear", "4档满额(元)/第X件/满量(件)");
                    put("fourthAmount","4档立减现金(元)");
                    put("fourthMemberAmount", "4档会员立减现金(元)");
                    put("fourthLowestCalculateRate","4档计算最低折率");
                    put("fourthLowestTempleteRate","4档模板最低折率");
                    put("fourthLowestMemberCalculateRate","4档计算会员最低折率");
                    put("fourthIncreasePoint","4档单件兑换积分");

                    put("fifthGear", "5档满额(元)/第X件/满量(件)");
                    put("fifthAmount","5档立减现金(元)");
                    put("fifthMemberAmount","5档会员立减现金(元)");
                    put("fifthLowestCalculateRate","5档计算最低折率");
                    put("fifthLowestTempleteRate","5档模板最低折率");
                    put("fifthLowestMemberCalculateRate","5档计算会员最低折率");
                    put("fifthIncreasePoint","5档单件兑换积分");

                    put("sixthGear", "6档满额(元)/第X件/满量(件)");
                    put("sixthAmount","6档立减现金(元)");
                    put("sixthMemberAmount","6档会员立减现金(元)");
                    put("sixthLowestCalculateRate","6档计算最低折率");
                    put("sixthLowestTempleteRate","6档模板最低折率");
                    put("sixthLowestMemberCalculateRate","6档计算会员最低折率");
                    put("sixthIncreasePoint","6档单件兑换积分");

                    put("seventhGear", "7档满额(元)/第X件/满量(件)");
                    put("seventhAmount","7档立减现金(元)");
                    put("seventhMemberAmount","7档会员立减现金(元)");
                    put("seventhLowestCalculateRate","7档计算最低折率");
                    put("seventhLowestTempleteRate","7档模板最低折率");
                    put("seventhLowestMemberCalculateRate","7档计算会员最低折率");
                    put("seventhIncreasePoint","7档单件兑换积分");

                    put("eighthGear", "8档满额(元)/第X件/满量(件)");
                    put("eighthAmount","8档立减现金(元)");
                    put("eighthMemberAmount","8档会员立减现金(元)");
                    put("eighthLowestCalculateRate","8档计算最低折率");
                    put("eighthLowestTempleteRate","8档模板最低折率");
                    put("eighthLowestMemberCalculateRate","8档计算会员最低折率");
                    put("eighthIncreasePoint","8档单件兑换积分");

                    put("ninthGear", "9档满额(元)/第X件/满量(件)");
                    put("ninthAmount","9档立减现金(元)");
                    put("ninthMemberAmount","9档会员立减现金(元)");
                    put("ninthLowestCalculateRate","9档计算最低折率");
                    put("ninthLowestTempleteRate","9档模板最低折率");
                    put("ninthLowestMemberCalculateRate","9档计算会员最低折率");
                    put("ninthIncreasePoint","9档单件兑换积分");

                    put("tenthGear", "10档满额(元)/第X件/满量(件)");
                    put("tenthAmount","10档立减现金(元)");
                    put("tenthMemberAmount","10档会员立减现金(元)");
                    put("tenthLowestCalculateRate","10档计算最低折率");
                    put("tenthLowestTempleteRate","10档模板最低折率");
                    put("tenthLowestMemberCalculateRate","10档计算会员最低折率");
                    put("tenthIncreasePoint","10档单件兑换积分");

                    put("monthLimitedCount","单月限购总量");
                    put("weekLimitedCount","单周限购总量");
                    put("orderLowestAmount","单笔订单最小金额");
                    put("dayLimitedCount","指定天数限购量值");
                    put("dayLimit","指定天数限购天数");
                    put("orderRequireCount", "单笔订单条件商品要求数量");
                    put("orderRequireAmount", "单笔订单条件商品要求金额");
                    put("storeOverloadCount", "单店限购数量");
                    put("dayStoreOverloadCount", "单日单门店限购数量");
                    put("dayUserOverloadCount", "单日单会员限购数量");
                    put("userOverloadCount", "单会员限购数量");
                    put("orderOverloadCount", "单笔订单限购数量");
                    put("overloadCount", "限购总量");
                    put("discountType","*促销折扣方式");
                    put("resource","*促销费用来源");
                    put("mostDiscountRate", "最高折率");
                    put("lowestDiscountRate", "最低折率");
                    //put("violateRule", "是否违反规则标记");
                    //put("whiteList", "是否有白名单");
                    //put("hitWhiteList", "是否命中白名单");
                    //put("matchWhiteListId", "匹配白名单ID");
                    //put("blackList", "是否有黑名单");
                    //put("hitBlackList", "是否命中黑名单");
                    //put("matchBlackListId", "匹配黑名单ID");
                    put("templeteId","模板id");
                    put("expireDate","近效期");
                    put("validPeriod","有效期至");
                    put("message","错误信息");

                    put("goodsCode","*商品编码");
                    put("goodsName", "商品名称");
                    put("goodsSpec", "规格");
                    put("produceFactory", "生产厂家");
                    put("goodsBrand", "商品品牌");
                    put("goodsCategory", "商品分类");
                    put("curName", "通用名");
                    put("drink","是否饮品");
                    //促销凭证化 新增导出字段 begin
                    put("selectStore","参与门店");
                    put("singleCouponTempId","券模板编码");
                    put("couponTempMethod","券模板获取方式");
                    put("couponTempIdMandatory", "是否必须有券");
                    //end
                    put("retailPrice", "最新零售价");
                    put("memberPrice", "最新会员价");
                    put("overPurchase", "*超量购买计价方式");
                    put("fristGear", "*1档满额(元)/第X件/满量(件)");
                    put("fristGiftOption", "*1档赠品选择方式");
                    put("firstGiftListString","1档指定赠品列表");
                    put("firstGiftLimitKind","1档单循环限购种数");
                    put("firstIncreaseLimit","1档单循环限购数量");
                    put("fristGiftNum","1档赠品/加价品件/份数");
                    put("fristLowestCalculateRate","1档计算最低折率");
                    put("fristLowestTempleteRate","1档模板最低折率");

                    put("secondGear", "2档满额(元)/第X件/满量(件)");
                    put("secondGiftOption", "2档赠品选择方式");
                    put("secondGiftListString","2档指定赠品列表");
                    put("secondGiftLimitKind","2档单循环限购种数");
                    put("secondIncreaseLimit","2档单循环限购数量");
                    put("secondGiftNum","2档赠品/加价品件/份数");
                    put("secondLowestCalculateRate","2档计算最低折率");
                    put("secondLowestTempleteRate","2档模板最低折率");

                    put("thirdGear", "3档满额(元)/第X件/满量(件)");
                    put("thirdGiftOption", "3档赠品选择方式");
                    put("thirdGiftListString","3档指定赠品列表");
                    put("thirdGiftLimitKind","3档单循环限购种数");
                    put("thirdIncreaseLimit","3档单循环限购数量");
                    put("thirdGiftNum","3档赠品/加价品件/份数");
                    put("thirdLowestCalculateRate","3档计算最低折率");
                    put("thirdLowestTempleteRate","3档模板最低折率");

                    put("fourthGear", "4档满额(元)/第X件/满量(件)");
                    put("fourthGiftOption", "4档赠品选择方式");
                    put("fourthGiftListString","4档指定赠品列表");
                    put("fourthGiftLimitKind","4档单循环限购种数");
                    put("fourthIncreaseLimit","4档单循环限购数量");
                    put("fourthGiftNum","4档赠品/加价品件/份数");
                    put("fourthLowestCalculateRate","4档计算最低折率");
                    put("fourthLowestTempleteRate","4档模板最低折率");

                    put("fifthGear", "5档满额(元)/第X件/满量(件)");
                    put("fifthGiftOption", "5档赠品选择方式");
                    put("fifthGiftListString","5档指定赠品列表");
                    put("fifthGiftLimitKind","5档单循环限购种数");
                    put("fifthIncreaseLimit","5档单循环限购数量");
                    put("fifthGiftNum","5档赠品/加价品件/份数");
                    put("fifthLowestCalculateRate","5档计算最低折率");
                    put("fifthLowestTempleteRate","5档模板最低折率");

                    put("monthLimitedCount","单月限购总量");
                    put("weekLimitedCount","单周限购总量");
                    put("orderLowestAmount","单笔订单最小金额");
                    put("dayLimitedCount","指定天数限购量值");
                    put("dayLimit","指定天数限购天数");
                    put("orderRequireCount", "单笔订单条件商品要求数量");
                    put("orderRequireAmount", "单笔订单条件商品要求金额");
                    put("storeOverloadCount", "单店限购数量");
                    put("dayStoreOverloadCount", "单日单门店限购数量");
                    put("dayUserOverloadCount", "单日单会员限购数量");
                    put("userOverloadCount", "单会员限购数量");
                    put("orderOverloadCount", "单笔订单限购数量");
                    put("overloadCount", "限购总量");
                    put("discountType","*促销折扣方式");
                    put("resource","*促销费用来源");
                    put("rebateModel", "返利模式");
                    put("rebateAmount", "单件返利金额");
                    put("singleRebateAmount", "单份返利金额");
                    put("rebateNum", "返利协议号");
                    put("rebateFactory", "返利厂家");
                    put("mostDiscountRate", "最高折率");
                    put("lowestDiscountRate", "最低折率");
                    //put("violateRule", "是否违反规则标记");
                    //put("whiteList", "是否有白名单");
                    //put("hitWhiteList", "是否命中白名单");
                    //put("matchWhiteListId", "匹配白名单ID");
                    //put("blackList", "是否有黑名单");
                    //put("hitBlackList", "是否命中黑名单");
                    //put("matchBlackListId", "匹配黑名单ID");
                    put("templeteId","模板id");
                    put("expireDate","近效期");
                    put("validPeriod","有效期至");
                    put("message","错误信息");

                    put("goodsCode","*商品编码");
                    put("goodsName", "商品名称");
                    put("goodsSpec", "规格");
                    put("produceFactory", "生产厂家");
                    put("goodsBrand", "商品品牌");
                    put("goodsCategory", "商品分类");
                    put("curName", "通用名");
                    put("drink","是否饮品");
                    //促销凭证化 新增导出字段 begin
                    put("selectStore","参与门店");
                    put("singleCouponTempId","券模板编码");
                    put("couponTempMethod","券模板获取方式");
                    put("couponTempIdMandatory", "是否必须有券");
                    //end
                    put("retailPrice", "最新零售价");
                    put("memberPrice", "最新会员价");
                    put("overPurchase", "*超量购买计价方式");
                    put("fristGear", "*1档满额(元)/第X件/满量(件)");
                    put("fristIncreaseOption", "*1档加价品选择方式");
                    put("firstIncreaseListString","1档指定加价品列表");
                    put("firstGiftLimitKind","1档单循环限购种数");
                    put("firstIncreaseLimit","1档单循环限购数量");
                    put("fristGiftNum","1档赠品/加价品件/份数");
                    put("fristAddPrice","1档加价购加价价格");
                    put("fristLowestCalculateRate","1档计算最低折率");
                    put("fristLowestTempleteRate","1档模板最低折率");

                    put("secondGear", "2档满额(元)/第X件/满量(件)");
                    put("secondIncreaseOption", "2档加价品选择方式");
                    put("secondIncreaseListString","2档指定加价品列表");
                    put("secondGiftLimitKind","2档单循环限购种数");
                    put("secondIncreaseLimit","2档单循环限购数量");
                    put("secondGiftNum","2档赠品/加价品件/份数");
                    put("secondAddPrice","2档加价购加价价格");
                    put("secondLowestCalculateRate","2档计算最低折率");
                    put("secondLowestTempleteRate","2档模板最低折率");

                    put("thirdGear", "3档满额(元)/第X件/满量(件)");
                    put("thirdIncreaseOption", "3档加价品选择方式");
                    put("thirdIncreaseListString","3档指定加价品列表");
                    put("thirdGiftLimitKind","3档单循环限购种数");
                    put("thirdIncreaseLimit","3档单循环限购数量");
                    put("thirdGiftNum","3档赠品/加价品件/份数");
                    put("thirdAddPrice","3档加价购加价价格");
                    put("thirdLowestCalculateRate","3档计算最低折率");
                    put("thirdLowestTempleteRate","3档模板最低折率");

                    put("fourthGear", "4档满额(元)/第X件/满量(件)");
                    put("fourthIncreaseOption", "4档加价品选择方式");
                    put("fourthIncreaseListString","4档指定加价品列表");
                    put("fourthGiftLimitKind","4档单循环限购种数");
                    put("fourthIncreaseLimit","4档单循环限购数量");
                    put("fourthGiftNum","4档赠品/加价品件/份数");
                    put("fourthAddPrice","4档加价购加价价格");
                    put("fourthLowestCalculateRate","4档计算最低折率");
                    put("fourthLowestTempleteRate","4档模板最低折率");

                    put("fifthGear", "5档满额(元)/第X件/满量(件)");
                    put("fifthIncreaseOption", "5档加价品选择方式");
                    put("fifthIncreaseListString","5档指定加价品列表");
                    put("fifthGiftLimitKind","5档单循环限购种数");
                    put("fifthIncreaseLimit","5档单循环限购数量");
                    put("fifthGiftNum","5档赠品/加价品件/份数");
                    put("fifthAddPrice","5档加价购加价价格");
                    put("fifthLowestCalculateRate","5档计算最低折率");
                    put("fifthLowestTempleteRate","5档模板最低折率");

                    put("monthLimitedCount","单月限购总量");
                    put("weekLimitedCount","单周限购总量");
                    put("orderLowestAmount","单笔订单最小金额");
                    put("dayLimitedCount","指定天数限购量值");
                    put("dayLimit","指定天数限购天数");
                    put("orderRequireCount", "单笔订单条件商品要求数量");
                    put("orderRequireAmount", "单笔订单条件商品要求金额");
                    put("storeOverloadCount", "单店限购数量");
                    put("dayStoreOverloadCount", "单日单门店限购数量");
                    put("dayUserOverloadCount", "单日单会员限购数量");
                    put("userOverloadCount", "单会员限购数量");
                    put("orderOverloadCount", "单笔订单限购数量");
                    put("overloadCount", "限购总量");
                    put("discountType","*促销折扣方式");
                    put("resource","*促销费用来源");
                    put("mostDiscountRate", "最高折率");
                    put("lowestDiscountRate", "最低折率");
                    //put("violateRule", "是否违反规则标记");
                    //put("whiteList", "是否有白名单");
                    //put("hitWhiteList", "是否命中白名单");
                    //put("matchWhiteListId", "匹配白名单ID");
                    //put("blackList", "是否有黑名单");
                    //put("hitBlackList", "是否命中黑名单");
                    //put("matchBlackListId", "匹配黑名单ID");
                    put("templeteId","模板id");
                    put("expireDate","近效期");
                    put("validPeriod","有效期至");
                    put("message","错误信息");

                    put("goodsCode","*商品编码");
                    put("goodsName", "商品名称");
                    put("goodsSpec", "规格");
                    put("produceFactory", "生产厂家");
                    put("goodsBrand", "商品品牌");
                    put("goodsCategory", "商品分类");
                    put("curName", "通用名");
                    put("retailPrice", "最新零售价");
                    put("memberPrice", "最新会员价");
                    put("groupNum","*入组数量");
                    put("discountType","*促销折扣方式");
                    put("resource","*促销费用来源");
                    put("message","错误信息");

                    put("goodsCode","*商品编码");
                    put("goodsName", "商品名称");
                    put("goodsSpec", "规格");
                    put("produceFactory", "生产厂家");
                    put("goodsBrand", "商品品牌");
                    put("goodsCategory", "商品分类");
                    put("curName", "通用名");
                    put("retailPrice", "最新零售价");
                    put("memberPrice", "最新会员价");
                    put("singleGroupCount","单组限购数量");
                    put("discountType","*促销折扣方式");
                    put("resource","*促销费用来源");
                    put("message","错误信息");

                    put("couponCode","券类型编码A");
                    put("couponName", "劵类型A名称");
                    put("overlapCouponCode", "券类型编码B");
                    put("overlapCouponName", "劵类型B名称");
                    put("message","错误信息");

                    put("orgCode","机构MDM编码");
                    put("couponId","导入券模板编码");
                    put("couponName", "券模板名称");
                    put("message","错误信息");

                    put("couponCode","券模板编码A");
                    put("couponName", "劵模板A名称");
                    put("overlapCouponCode", "券模板编码B");
                    put("overlapCouponName", "劵模板B名称");
                    put("message","错误信息");

                    put("orgCode","机构MDM编码");
                    put("couponId", "导入券模板编码");
                    put("couponName", "劵模板名称");
                    put("message","错误信息");

                    put("goodsNo","*商品编码");
                    put("goodsName","商品名称");
                    put("goodsSpec","规格");
                    put("produceFactory","生产厂家");
                    put("factoryInvested","厂家投入");
                    put("rebateAmount","加提金额");
                    put("requireFlowStr","流向要求");
                    put("message","错误信息");
                }
            };

        //System.out.println( multipleMap);


        for (String v:multipleMap.values()){

            if(v.contains("*") && !a.contains(v)){
                System.out.println("异常:"+v);
            }
        }

    }
/*

        String result = "/Users/<USER>/Desktop/1234.json" ;
        ExcelReader reader = ExcelUtil.getReader("/Users/<USER>/Desktop/cz.xls");
        List<Map<String,Object>> readAll = reader.readAll();

        readAll.forEach(v->{
            System.out.println(v.toString());
            try {
                JSONObject o = (JSONObject) JSON.toJSON(v);
                o.put("bz",o.get("address"));
                FileUtil.appendUtf8String(o.toString()+",",result);
            }catch (IORuntimeException e){
                //抛出一个运行时异常(直接停止掉程序)
                throw new RuntimeException("运行时异常",e);
            }
        });



            String v="20220120";
            String t = v.substring(0,4).concat(".").concat( v.substring(4,6)).concat(".").concat( v.substring(6,8));
            System.out.println(t);

       // 123.json
        /*String path = "/Users/<USER>/Desktop/su3000.txt" ;
        String result = "/Users/<USER>/Desktop/result3000.txt" ;
        String str = FileUtil.readUtf8String(new File(path));
        System.out.println(str);
        List<JSONObject> errJsonObjectList=new ArrayList<>();
        JSONArray parse = (JSONArray) JSONArray.parse(str);
        parse.stream().forEach(v->{
            JSONObject o = (JSONObject) JSON.toJSON(v);
            System.out.println(o.toString());
        });

        BigDecimal a=new BigDecimal("0.1");
        System.out.println(a.compareTo(BigDecimal.ZERO)>0);*/



   /*    String a=new String();
       a="B2R0,B2R1,B2R2,B2R3,B2R4,B2R6,B2R7,B2R8,B2R9,B2RA,B2RB,B2RC,B2RD,B2RF,B2RG,B2RH,B2RJ,B2RK,B2RL,B2RM,B2RN,B2RP,B2RQ,B2RR,B2RS,B2RT,B2RV,B2RW,B2RX,B2RY,B2RZ,B2S0,B2S1,B2S2,B2W7,B2WL,B2XY,B2Z1,B2ZK,B2ZZ,B3BX,B3GM,B3J5,B3LZ,B3SM,B3U6";

       for (int i = 0; i < a.split(",").length;i++ ){

           if("B0EG,B331,B332,B333,B0CV,ADU6,ADU7,B38J,B2CR,B3FM,B3FN,B3CR,PF18,PF1F,PF1G,PF1H,PF1M,PF1L,PF1J,PF1E,PF1K,PF19,PF1A,PF1B,B3LL,B3KX,B3LU,B3NA,B3NB,B3MC,B3P2,B3L8,B3PA,B3DB,B3PU,B3PS,B3PR,B3Q5,B3PQ,B3PX,B3R8,B3QR,PF13,B3S6,B3V9,B3UX,B47R,31R2,B4DF,B3KL,B3KN,B4JE,B3QG,B3KM,B3M0,B4JX,B0ZX,B37A,B0ZZ,B0ZV,B379,B0ZW,B39G,B3QV,B3E1,BKG1,B32F,B36M,B3UH,B3E0,B3P6,B4JY,B3QW,B3DZ,B3JT,B2LZ,B2M0,B3M1,BKHV,BKHW,B32S,BKRE,AA8Y,A87E,A753,A14Y,A090,A131,A88U,A07S,A50Y,A59U,A95Y,AA0P,B2NZ,AE6Q,B3J7,AE3L,AE7J,AE3R,AE3T,AE4N,AE76,AE7G,AE5G,AE52,AE54,AE5C,AE6D,AE63,B1J7,B2D1,B31N,B31U,B31B,B31P,A949,B33T,A91D,A92N,A90H,A715,A67A,B4R5,B4EG".contains(a.split(",")[i])){
               System.out.println("已存在"+" "+a.split(",")[i]);
           }
       }*/







}
