package com.tomas.mybaties3.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * Table123 描述
 *
 * <AUTHOR>
 * @create 4/12/23
 **/
public class ZWBUsiness {

    public static String path="/Users/<USER>/code/github/SpringBoot-Learning/dbDoc/src/test/java/com/tomas/mybaties3/testDemo/" ;

    public static void main(String[] args)  throws Exception{

/*
            // Create a BufferedReader object to read the file
            BufferedReader reader = new BufferedReader(new FileReader("/Users/<USER>/Desktop/a.json"));
            // Read each line of the file and print it to the console
         StringBuffer a=new StringBuffer();
            String line = reader.readLine();
            while (line != null) {
                a.append(line);
            }
        reader.close();
        System.out.println(a.toString());*/

        try {
            // 读取文件内容
            BufferedReader reader = new BufferedReader(new FileReader("/Users/<USER>/Desktop/a.json"));
            StringBuilder stringBuilder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                stringBuilder.append(line);
            }
            reader.close();

            // 将读取的内容解析为 JSONObject 或 JSONArray
            Object parsedObject = JSON.parse(stringBuilder.toString());

            // 判断解析结果的类型并处理
            if (parsedObject instanceof JSONObject) {
                // 如果是 JSONObject
                JSONObject jsonObject = (JSONObject) parsedObject;
                // 处理 jsonObject，可以根据需要进行进一步操作
                System.out.println(jsonObject);
            } else if (parsedObject instanceof JSONArray) {
                // 如果是 JSONArray
                JSONArray jsonArray = (JSONArray) parsedObject;

                List<JSONObject> rows = CollUtil.newArrayList();

                //List<Map<?,?>> rows = CollUtil.newArrayList();

                jsonArray.stream().forEach(v->{
                    JSONObject parse = JSON.parseObject(JSON.toJSONString(v));
                    System.out.println();
                    rows.add(parse);
                });

                // 通过工具类创建writer
                String file = path+"bigWriteMapTest4444.xlsx";
                FileUtil.del(file);
                BigExcelWriter writer = ExcelUtil.getBigWriter(file);
                LinkedHashMap<String,String> DVC_GOODS_LIST = new LinkedHashMap<String,String>(){
                    private static final long serialVersionUID = -6514836291678556529L;
                    {
                        put("name","name");
                        put("sapcode","sapcode");
                        put("outId","outId");
                    }
                };

                // writer.merge(DVC_GOODS_LIST.size() - 1, "明细");
                //Row row = writer.getOrCreateRow(0);
                //row.setHeight((short) 2000);
       /* CellStyle cellStyle = writer.getOrCreateCellStyle(0,0);
        StyleUtil.setColor(cellStyle, (short) 13, FillPatternType.SOLID_FOREGROUND);
        StyleUtil.setAlign(cellStyle, HorizontalAlignment.LEFT, VerticalAlignment.CENTER);*/


                writer.setHeaderAlias(DVC_GOODS_LIST);
                writer.setOnlyAlias(true);
                // writer.write(resultList, true);
                // 合并单元格后的标题行，使用默认标题样式
                //writer.merge(4, "一班成绩单");
                // 一次性写出内容，使用默认样式
                writer.write(rows, true);
                // 关闭writer，释放内存
                writer.close();
                // 处理 jsonArray，可以根据需要进行进一步操作

            } else {
                System.out.println("Unsupported JSON format");
            }



        } catch (IOException e) {
            e.printStackTrace();
        }
        System.out.println("ok");

    }

}
