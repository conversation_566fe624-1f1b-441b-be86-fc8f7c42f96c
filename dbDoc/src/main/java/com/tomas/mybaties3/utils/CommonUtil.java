package com.tomas.mybaties3.utils;


import com.tomas.mybaties3.model.TestBean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 常用小工具
 */
public class CommonUtil  {


    private static Logger logger = LoggerFactory.getLogger(CommonUtil.class);

    private static final String CHINA_TELECOM_PATTERN = "^1[3,4,5,6,7,8,9]\\d{9}$";


    /**
     * 验证是否是正确合法的手机号码
     *
     * @param telephone
     * @return 合法返回true，不合法返回false
     */
    public static boolean isCellPhoneNo(String telephone) {
        Pattern pattern = Pattern.compile(CHINA_TELECOM_PATTERN);
        Matcher matcher = pattern.matcher(telephone);
        if (matcher.matches()) {
            return true;
        }
        return false;
    }

    public static void main(String[] args) {


        List<TestBean> list=new ArrayList<>();
        TestBean a=new TestBean();
        a.setId(1);
        a.setAge(1);

        TestBean b =new TestBean();
        b.setId(2);
        b.setAge(2);



        TestBean c =new TestBean();
        c.setId(8);

        list.add(a);
        list.add(b);
        list.add(c);
        List<Integer> collect = list.stream().filter(v -> v.getId() > 7)
                .map(TestBean::getAge).distinct().collect(Collectors.toList());

        // collect(Collectors.toMap(TestBean::getId, TestBean::getAge));
        logger.info("x={}",getEndOfDay(new Date()));


    }

    public static Date getEndOfDay(Date date) {
        LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()), ZoneId.systemDefault());
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }
}
