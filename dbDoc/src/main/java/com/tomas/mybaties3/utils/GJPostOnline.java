package com.tomas.mybaties3.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.*;
import java.net.URLEncoder;

/**
 * Test 描述
 *
 * <AUTHOR>
 * @create 2021/7/28
 **/
public class GJPostOnline {
    public static String epath="/Users/<USER>/code/github/SpringBoot-Learning/dbDoc/src/test/java/com/tomas/mybaties3/testDemo/" ;
    public static void main(String[] args) throws IOException, InterruptedException {

        String path = "/Users/<USER>/Desktop/1234.json" ;
        String result = "/Users/<USER>/Desktop/result0810.txt" ;
        String str = FileUtil.readUtf8String(new File(path));
        JSONArray parse = (JSONArray) JSONArray.parse(str);
        parse.stream().forEach(v->{
            JSONObject o = (JSONObject) JSON.toJSON(v);
            System.out.println(o.toString());
            String name= null;
            String address=null;
            String bz=null;
            String sfz=null;
            String sjh=null;
            try {
                name = URLEncoder.encode(o.getString("name").trim(), "UTF-8").trim();
                address = URLEncoder.encode(o.getString("address").trim(), "UTF-8").trim();
                bz = URLEncoder.encode(o.getString("bz").trim(), "UTF-8").trim();
                sfz = URLEncoder.encode(o.getString("sfz").trim(), "UTF-8").trim();
                sjh = URLEncoder.encode(o.getString("sjh").trim(), "UTF-8").trim();
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            String data = "__VIEWSTATE=%2FwEPDwUJOTQ2Njk5NjYyD2QWAgIDD2QWBAIbDxAPFgYeDkRhdGFWYWx1ZUZpZWxkBQVEYWlNYR4NRGF0YVRleHRGaWVsZAUJTWluZ0NoZW5nHgtfIURhdGFCb3VuZGdkEBU6BuaxieaXjwnokpnlj6Tml48G5Zue5pePBuiXj%2BaXjwznu7TlkL7lsJTml48G6IuX5pePBuW9neaXjwblo67ml48J5biD5L6d5pePCeacnemynOaXjwbmu6Hml48G5L6X5pePBueRtuaXjwbnmb3ml48J5Zyf5a625pePCeWTiOWwvOaXjwzlk4jokKjlhYvml48G5YKj5pePBum7juaXjwnlgojlg7Pml48G5L2k5pePBueVsuaXjwnpq5jlsbHml48J5ouJ56Wc5pePBuawtOaXjwnkuJzkuaHml48J57qz6KW%2F5pePCeaZr%2Bmih%2BaXjw%2Fmn6%2FlsJTlhYvlrZzml48G5Zyf5pePDOi%2BvuaWoeWwlOaXjwnku6vkvazml48G576M5pePCeW4g%2Bacl%2BaXjwnmkpLmi4nml48J5q%2Bb6Zq%2B5pePCeS7oeS9rOaXjwnplKHkvK%2Fml48J6Zi%2F5piM5pePCeaZruexs%2BaXjwzloZTlkInlhYvml48G5oCS5pePD%2BS5jOWtnOWIq%2BWFi%2BaXjwzkv4TnvZfmlq%2Fml48M6YSC5rip5YWL5pePCeW0qem%2BmeaXjwnkv53lronml48J6KOV5Zu65pePBuS6rOaXjwzloZTloZTlsJTml48J54us6b6Z5pePDOmEguS8puaYpeaXjwnotavlk7Lml48J6Zeo5be05pePCeePnuW3tOaXjwnln7ror7rml48G5YW25LuWG%2BWkluWbveihgOe7n%2BS4reWbveexjeS6uuWjqxU6ATEBMgEzATQBNQE2ATcBOAE5AjEwAjExAjEyAjEzAjE0AjE1AjE2AjE3AjE4AjE5AjIwAjIxAjIyAjIzAjI0AjI1AjI2AjI3AjI4AjI5AjMwAjMxAjMyAjMzAjM0AjM1AjM2AjM3AjM4AjM5AjQwAjQxAjQyAjQzAjQ0AjQ1AjQ2AjQ3AjQ4AjQ5AjUwAjUxAjUyAjUzAjU0AjU1AjU2Ajk3Ajk4FCsDOmdnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dkZAIdDxAPFgYfAAUFRGFpTWEfAQUJTWluZ0NoZW5nHwJnZBAVITznurPlhaXnpL7ljLrnrqHnkIbnmoTmlrDlhqDogrrngo7mhJ%2Fmn5PogIXlj4rlhbblkIzkvY%2FkurrlkZge6auY5pq06Zyy5bKX5L2N55qE5LuO5Lia5Lq65ZGYKuWMu%2BeWl%2BacuuaehOWFpemZouaCo%2BiAheWPiuWFtumZquaKpOS6uuWRmCflhbbku5bmnLrlhbPkvIHkuovkuJrljZXkvY3lt6XkvZzkurrlkZgn5bel5L2c546v5aKD5Lq65ZGY5aSN5p2C55qE5LuO5Lia5Lq65ZGYQuaZrumAmuWMu%2BeWl%2BacuuaehO%2B8iOmZpOWPkeeDremXqOiviuWklu%2B8ieOAgeiNr%2BW6l%2BW3peS9nOS6uuWRmOetiSHnlJ%2Fkuqfovabpl7Tlkozlu7rnrZHlt6XlnLDkurrlkZgz5bmy6K2m6IGM5bel44CB5pyN5Yqh5L%2Bd6Zqc5Lq65ZGY5ZKM6KKr55uR566h5Lq65ZGYNuacjeWKoeawkeaUv%2BacjeWKoeWvueixoeeahOW3peS9nOS6uuWRmOWSjOacjeWKoeWvueixoRXluIjnlJ%2Flkozlt6XkvZzkurrlkZgn6L%2Bb5Y%2Bj6Z2e5Ya36ZO%2B5L2N55qE5YW25LuW5bel5L2c5Lq65ZGYMOi%2Fm%2BWPo%2BWGt%2BmTvumXreeOr%2BeuoeeQhuS6uuWRmOacjeWKoeS%2FnemanOS6uuWRmDDpm4bkuK3pmpTnprvlnLrmiYDkurrlkZjlpJblm7TmnI3liqHkv53pmpzkurrlkZgY5Y%2Bj5bK4566h55CG5pyN5Yqh5Lq65ZGYKuebtOaOpeaOpeinpuWbvemZhemCruS7tuW%2Fq%2BS7tuW3peS9nOS6uuWRmDPnm7TmjqXmjqXop6blhaXlooPkurrlkZjlkoznianlk4HnmoTkuIDnur%2FkurrlkZjnrYke5Y%2Bj5bK46L%2Bb5Y%2Bj54mp5ZOB5pCs6L%2BQ5Lq65ZGYNui3qOWig%2BS6pOmAmuW3peWFt%2BeahOWPuOS5mOOAgeS%2Fnea0geOAgee7tOS%2FruetieS6uuWRmDDlooPlpJbmnaXmlpnliqDlt6XjgIHov5DovpPnrYnkvIHkuJrlt6XkvZzkurrlkZhO6L%2Bc5rSL5rC05Lqn5o2V5o2e44CB6L%2BQ6L6T44CB5LuT5YKo44CB5Yqg5bel5ZKM56CB5aS06L%2BQ6JCl5LyB5Lia5LuO5Lia5Lq65ZGYEuWGt%2BmTvuebuOWFs%2BS6uuWRmB7pm4bkuK3pmpTnprvlnLrmiYDlt6XkvZzkurrlkZgh5paw5Yag55eF5q%2BS5a6e6aqM5a6k5qOA5rWL5Lq65ZGYKuaZrumAmuWMu%2BeWl%2BacuuaehOWPkeeDremXqOiviuW3peS9nOS6uuWRmB7lrprngrnljLvnlpfmnLrmnoTlt6XkvZzkurrlkZgw6I2v5bqX55m76K6w6LSt5Lmw5L2%2F55So4oCc5Zub57G76I2v5ZOB4oCd5Lq65ZGYY%2BWPkeeDreaCo%2BiAheOAgeWPr%2BeWkeaCo%2BiAheOAgeS4jeaYjuWOn%2BWboOiCuueCjuOAgeS9j%2BmZouaCo%2BiAheS4reS4pemHjeaApeaAp%2BWRvOWQuOmBk%2BaEn%2Bafk%2BeXheS%2BixjkvY7po47pmanljLrlpJbmuqLkurrlkZgY5Lit6aOO6Zmp5Yy65aSW5rqi5Lq65ZGYGOmrmOmjjumZqeWMuuWklua6ouS6uuWRmBLmrKHlr4bliIfmjqXop6bogIUe5a%2BG5YiH5o6l6Kem6ICF44CB5YWl5aKD5Lq65ZGYBuWFtuS7lhUhAjMyAjMxAjMwAjI5AjI4AjI3AjI2AjI1AjI0AjIzAjIyAjIxAjIwAjE5AjE4AjE3AjE2AjE1AjE0AjEzAjEyAjExAjEwATkBOAE3ATYBNQE0ATMBMgExAjMzFCsDIWdnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2dnZ2RkZCNG6t%2FwTLfIfWYjmIA1t2ihkSpmHY4BLjFzPgQIwtW0&__VIEWSTATEGENERATOR=66CEDE76&__EVENTVALIDATION=%2FwEdAHy0R%2FgOtgCE8mJllQ7C7%2Fk4X9ZLR5rgGocRm4vzvcbSbJ%2FHEXC05jPNYWraiRyooU6wlzXD6U6HCyo44SsC85LkCQn5Qt1eVqs10juWBbE66Ww4PLpNyJogmbbM%2BMT%2FZ9f5rj7FBBBotbrGr3q77D%2FJ8b4IkE6oHqht5eSfKjT%2BqsN3DA15cemMdivrkJvT4C0X66UykHRP8aSDrKFdF6QsNAQn%2FZRuwH119f6QR3BRxmOmQEpOU4PZeSfLxRtQNLCph8Lv2wSiPV%2BxO3Yz%2FJJwthxdVmP3CBA%2B%2FBiW9VV3bPE%2FR7jxGHf%2BHx7QYykGfTK%2F6lPDZXyJlq6lzwUkQ91bznHbQUI%2B1aZppKZCd3EQ4RqTeZngaVVX4TwQ4SDD7gOh37v2WOInk%2BVUCwcVtrxRaUtOCk7%2F6kJVqlWJKZ2TYYfB259azvK1tCAaDCzDDwwROuV4s9hVr6C0RizX6qMWa6RMlP0nkxToh9kU%2BHC2vwTHgxJacW1XlvR%2BjvpOO%2BhuU3iZ7cTlnJu9%2BfNvggqkFq%2B0HBUGpTfCFLxvknzbyAVQiy2sQne571DLwJvmi9GWkijmjCipWTTi1K5gFW0Xm%2BD1Cs6wubU%2BIyIPqtJpT6wOms0SCHf9fzaM%2BQTWatK61Dzyym7VnCSOt5hG6I2FmvkyIYWLgOzepbx5kjut1MYB6Awq9sM2yisdR3oiPtHsIrHKV04PIjRxPI35%2BD3fMRejreHJCw8om4fpFW4AqlEJsnRCYh30Ler5yR3WLiagCkbzBN9av%2FGEL3Uaemii%2FsTW22MOHnr7tuKJnTZEGM%2B9jftdrA8d%2FRSRUy8YljSMLeCFZb1fstXCMon72G42U%2FUxECRFVaTXmK694mQT2MAh7dM1ujGasUb7wzwFzxYPmjE%2FBKbFJdqVv6Y5BtVxrVsg3jpU8OxYhNcefAgaeW2v3pD7KnqYEEewMb%2FFf5fTKjwnUF%2FoxIGI5zcJyUq1S0SgNXLr2%2By%2BR3lM%2BSszgN6nL5SV4ESq%2FExuTJ69dI7pTVlyNkKzuDEa0wW90wc2sFhEric6E6MuvlqSK6el3nTJur5v4T0nMP8rbr9Ii7DB5hxJiNyZxTHMUkJIX55r7Wh5eGbSlINJDaj6zhptgzOjaRs7r82n8nyMKJ%2FV6SfX8ZbYW4vXzE9bp5fwk8mLP2sbtHlN5mZFZIHUf%2BLefcdNB19YPELiFtX%2FeRGF8AdfuNoLOQXMaPbm58basWCY6h%2FiQekvxaygssZaY%2BQIj2nR8mZ9rtqEZXvcPdy%2FBJQ2ZhzwReSq0wLaGvX0Uzo7CQM8VESAqnwKPcj0SkLc4muDVN%2BT9gDQw%2FYddneqCuKZ74RK9Gf%2Bb%2FlNXz3GU%2FzdGxj8bhbShgR7%2BCQw4E4pdEJM3ULXM0aNycy6cYzo9b3zLh9%2B5Omh%2B6H54V9m2MblFcwC8yfTNWW%2BpHgQ%2BBJXYGy8yKslc8eEQasMtskCEf7dZmzrtYEotDvD%2FGmKa6cnQOd8ximQKcBoKuM5o35cUmINR45yMBnF6s6RCeGxEwTFIJBXR3%2Fie9YnIPd4aFiynKeZ3WSPsL7cuAlQUv19qNAnSOqN6l22n9jalwniBmbKk2KoVG7tgF9aZtf8ZV1%2FVRusSGv5LJWptAJUTZ%2B7nvX2gxGokw3DmzeF%2FEDFF2FGxSyWNYPNSHHxQQeGi1A%2FbTHEf0fG%2FMkN%2FaiaIlzkGo7hikOOxRsa%2FmTQrMrYtfwrvTeQChje541qq5mcbnFxyglnMLa5F48IaVM0SQS6293uI59cREiZG7GqgwM5gn0WZt9GCVFzAVwK5gTeknG8k51DPSv84hL9JoCIFgahhPZvrQmbJ8hxLmPytgxqEEdkM%2Bg66%2FFhHeWie3Bkx1v70cTTFY4xTnhBBfVe3%2Bw04YjW0wPJuwJkywivNPVyAl68248I1FT17swNKsMxLWMU8nk77wiASLwsYLk5sa%2BtXPqD51RG3ORJwMhk8S0FK5dNEMfBv1A9JDRQDMbdk45NPr6VqZlEX8CQOkwiJt8%2FSuWciRpmVgRlB7aXqLbb7BXjU506eTSpq4l4jAj%2B2TTIMzawQZaBdnEBTQhhIDHBPjyM8rHWcRtm9%2BUxd7hg4CSkR9Anh8rmXFTwBHUvplWWd2biTVKriC6gm0Ued1c01hSTrHI9%2B%2BSdOLgWB0GxwdoE%2Bjgd6NQkXYVLdeWGcInqp35gx9k6bVtc4n0nUhHuKHr2%2FuEBtgoIpGHtf1hSv9C7jZ6N8SaqOjoy2CDKS8ppWy5BrbwiHMm%2Byo1A0gkfRIShEe9iue5q5Ga10eh8686xADtQ4aGgNy4WhTLCJnG0bi8L0Y9P0SQooC7sG8APwV1BZbsXzlG1gmkIpJj011IbXrz0uMoGTmR9GNdWxeYQhc3NOKtjeRGzYR2PKLfWtSm5i5LkJxykIdpz%2Fsj%2FPv3u13lNFcl1KUT4IaT1ztAuxWaWqeZPWxE3y1WUL7mofsvUs3hAlrQLFXZ6bYQAEI5qBhb125FvdvVk2zVtyPZAvhKgCbbV1F%2FyV5OSBzI7sXxhdGvIFQ549UYut3Pb0G0eQZT5TklLTY9HiG270UAauqmahQzVXtW8Ob3pbKMkF%2FINMARiPXHvFdwO3HduUZfN%2BDvxnwFeFeJ9MIBWR693CxbJmKtAvR5ISaWiq2G4e8LAGlaVoG19QKsTgMcRs5I%3D&hid_yinhangkalist=&hidRoleCode=371703002&hidRoleType=5&hidPro=371703&hidCity=371703002&hidArea=371703002202&hidProName=%E5%AE%9A%E9%99%B6%E5%8C%BA&hidCityName=%E6%BB%A8%E6%B2%B3%E8%A1%97%E9%81%93&hidAreaName=%E4%BB%98%E5%BA%84%E6%9D%91%E5%A7%94%E4%BC%9A&ddlXingBie=1&tbShenFenZhengHao="+sfz+"&province=371703&ShengShiQuNew%24hidPro=&ShengShiQuNew%24hidProName=&city=371703002&ShengShiQuNew%24hidCity=&ShengShiQuNew%24hisCityname=&area=371703002202&ShengShiQuNew%24hidArea=&ShengShiQuNew%24hidAreaname=&tbXingMing="+name+"&ddlMinu=1&ddlRenQun=33&ddlRenYuanZhuangTai=1&ddlFangWu=0&tbShouJiHao="+sjh+"&tbDiZhi="+address+"&ddlIsYingJian=1&tbZhunJiaCheXing="+bz+"&Button1=%E4%BF%9D%E5%AD%98";
            String[] cmds = {"curl", "-X", "POST",
                    "-H", "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,fr;q=0.7",
                    "-H", "pragma: no-cache",
                    "-H", "Cache-Control: max-age=0",
                    "-H", "Connection: keep-alive",
                    // "-H","authorization: bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" ,
                    "-H", "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                    "-H", "Content-Type: application/x-www-form-urlencoded",
                    "-H", "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36",
                    "-H", "Cookie: ASP.NET_SessionId=cpbjm1cfvy3caus553wbirwc; User_PID=5525e79c-f069-4706-a9fe-9092763b0ce8; User_Name=371703002; User_RoleType=5",
                    "-H", "origin: http://************:8691",
                    "-H", "Upgrade-Insecure-Requests: 1",
                    "-H", "referer: http://************:8691/WebAdmin/SiJi/AddNewMsg.aspx",

                    "--data-raw", data,
                    "--compressed", "http://************:8691/WebAdmin/SiJi/AddNewMsg.aspx"};

            //System.out.println(JSON.toJSONString(cmds));
            String s1 = execCurl(cmds);
            //System.out.println(s1);
            if(null != s1  && s1.contains("alert('新增成功！');parent.closethis()")){
                System.out.println("新增成功"+o.toString());
                o.put("success","新增成功");
                try {
                    FileUtil.appendUtf8String(o.toString()+",",result);
                }catch (IORuntimeException e){
                    //抛出一个运行时异常(直接停止掉程序)
                    throw new RuntimeException("运行时异常",e);
                }
            }else {
                int i = s1.indexOf("//<![CDATA[\n" +
                        "alert('");
                if(i!=-1){
                    String substring = s1.substring(i + 19, i + 50);
                    int i1 = substring.indexOf("');");
                    o.put("error",substring.substring(0,i1));
                }else {
                    o.put("error","添加失败");
                    System.out.println(s1.toString());
                }
                try {
                    FileUtil.appendUtf8String(o.toString()+",",result);
                }catch (IORuntimeException e){
                    //抛出一个运行时异常(直接停止掉程序)
                    throw new RuntimeException("运行时异常",e);
                }
            }
            try {
                Thread.sleep(200L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });
    }


    public static String execCurl(String[] cmds) {
        ProcessBuilder process = new ProcessBuilder(cmds);
        Process p;
        try {
            p = process.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(p.getInputStream()));
            StringBuilder builder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
                builder.append(System.getProperty("line.separator"));
            }
            return builder.toString();

        } catch (IOException e) {
            System.out.print("error");
            e.printStackTrace();
        }
        return null;
    }



}
