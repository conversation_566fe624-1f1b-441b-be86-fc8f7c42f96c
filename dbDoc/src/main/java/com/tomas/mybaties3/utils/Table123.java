package com.tomas.mybaties3.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * Table123 描述  分库分表计算
 *
 * <AUTHOR>
 * @create 4/12/23
 **/
public class Table123 {


    public static void main(String[] args) {


      String  sql=  "ALTER TABLE `pos_record_base_$SEQ` MODIFY COLUMN `customer_identity` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '顾客身份证' AFTER `customer`, " +
                "MODIFY COLUMN `customer_address` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '顾客地址' AFTER `customer_sex`, " +
                "MODIFY COLUMN `customer_tel` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '顾客联系方式' AFTER `customer_address`;";

        String  sql2=  "INSERT INTO `cowell_cart`.`promotion_coupon_template_ZIDX` (`promotion_id`,`business_id`,`business_name`,`coupon_id`,`coupon_name`,`coupon_type`,`start_coupon_time`,`end_coupon_time`,`coupon_channel`,`coupon_status`,`status`,`extend`,`gmt_update`,`created_by`,`updated_by`,`env`,`version`,`gmt_create`,`coupon_type_desc`,`coupon_channel_desc`,`status_desc`) VALUES (TTTID,22735,'',***********,'积分兑换300元券','9','2020-08-01 00:00:00','2024-09-01 23:59:59','06',1,1,'','2024-03-15 15:55:33','周伟','周伟','pro',1,'2024-03-15 15:55:33','积分兑券','门店核销','进行中'),(TTTID,22735,'',***********,'积分兑换25元券','9','2023-09-01 00:00:00','2027-06-30 23:59:59','06',1,1,'','2024-03-15 15:55:21','周伟','周伟','pro',1,'2024-03-15 15:55:21','积分兑券','门店核销','进行中'),(TTTID,22735,'',***********,'积分兑换3元券','9','2023-09-01 00:00:00','2027-06-30 23:59:59','06',1,1,'','2024-03-15 15:55:09','周伟','周伟','pro',1,'2024-03-15 15:55:09','积分兑券','门店核销','进行中');";

        String prromotionIds="13998465,13998452,13997500,13997496,13997464,13997458,13997456,13997454,13997451,13997323,13997319,13997311,13997299,13997297,13997294,13997284,13997278,13997268,13997262,13997255,13997246,13997244,13997239,13997236,13997233,13997232,13997227,13997190,13997180,13997179,13997178,13997177,13997176,13997155,13997146,13997144,13997143,13997142,13997141,13997098,13997092,13997086,13997075,13997071,13997069,13997040,13997033,13997032,13997028,13996996,13996988,13996984,13996956,13996884,13996865,13996861,13996844,13996762,13996761,13996713,13996704,13996695,13996327,13996324,13996322,13996312,13996309,13996308,13996277,13996275,13996272,13996257,13996251,13996246,13996177,13996091,13996086,13996082,13996074,13996070,13996062,13996060,13996057,13996052,13996050,13996047,13996038,13996031,13996020,13996015,13996004,13995986,13995984,13995982,13995957,13995954,13995949,13995033,13995032,13995031,13995030,13995029,13994858,13994807,13994796,13994350,13994323,13993490,13992839,13992838,13992740,13992739,13992478,13992474,13992472,13991867,13991866,13991865,13991864,13991857,13990659,13990657,13990655,13990638,13990556,13990544,13989000,13988974,13988869,13988517,13988319,13988315,13988306,13988302,13988285,13988275,13988267,13988229,13988200,13988173,13988165,13988152,13988146,13988136,13988130,13988111,13988092,13988089,13988081,13988051,13988009,13988005,13987998,13987990,13987974,13987935,13987927,13987788,13987770,13987727,13987717,13987515,13987511,13987502,13987488,13987479,13987472,13987468,13987459,13987452,13987448,13987445,13987412,13987402,13987393,13987381,13987376,13987364,13987357,13987330,13986682,13986680,13986678,13986674,13986671,13986664,13986660,13986655,13986640,13986251,13986247,13986244,13986238,13986235,13986233,13986230,13986228,13986218,13986215,13986211,13986207,13986205,13986202,13986199,13986195,13986194,13986190,13986187,13986172,13986168,13986160,13986158,13986155,13986148,13986144,13986141,13986137,13986127,13986125,13986120,13986117,13986116,13986106,13986099,13986096,13986084,13986070,13986065,13986037,13985652,13984991,13984987,13984800,13983724,13983723,13983722,13983720,13983719,13983718,13983717,13983716,13983712,13983707,13983706,13983705,13983703,13983702,13983701,13983700,13979949,13979944,13979916,13979914,13979766,13979764,13979761,13979757,13979755,13979732,13979726,13979721,13979717,13978681,13978676,13973149,13973148,13958938,13958937,13958913,13958912,13958911,13958910,13958909,13958908,13958907,13958906,13958905,13958863,13958862,13958861,13958860,13958859,13958858,13958829,13958828,13958827,13958826,13958825,13958824,13958803,13958802,13958801,13958800,13958799,13958798,13958797,13958796,13958759,13958758,13958757,13958756,13958755,13958754,13958671,13958670,13958669,13958668,13958667,13958666,13958665,13958664,13958663,13958617,13958616,13958615,13958614,13958613,13958612,13958611,13958610,13958581,13958580,13958579,13958578,13958577,13956014,13956013,13955594,13955593,13955592,13955587,13955586,13955582,13955581,13955580,13955572,13955571,13955526,13955525,13955524,13955519,13955518,13955517,13955505,13955504,13955503,13955500,13955499,13955498,13955496,13955495,13955494,13955492,13955491,13955490,13955489,13955488,13955487,13955486,13955485,13955484,13955481,13955480,13955479,13953938,13953821,13951718,13951716,13951712,13951521,13951510,13951507,13951496,13951489,13951488,13951485,13951483,13948624,13948468,13943308,13943307,13943306,13941040,13941039,13941038,13941035,13941034,13941033,13941031,13941030,13941029,13941027,13941026,13941025,13941019,13941018,13941017,13941016,13941015,13941014,13940994,13940993,13940991,13940981,13940980,13940979,13935357,13935356,13935355,13935321,13898394,13898393,13898392,13898391,13898390,13898389,13898388,13898387,13898386,13898385,13898374,13898373,13898372,13898368,13898367,13898366,13898365,13898364,13898363,13898362,13898361,13898360,13898359,13898358,13898357,13898356,13891393,13891391,13891389,13891362,13828789,13828785,13828784,13799270,13799269,13799265,13785526,13785290,13778398,13768809,13755956,13755951,13721035,13691282,13691280,13691277,13691275,13691274,13691273,13691267,13691266,13691265,13691263,13691262,13691261,13691233,13691229,13689461,13689460,13689459,13689458,13689457,13689456,13689455,13689454,13689453,13689452,13689450,13689448,13689447,13689442,13689435,13689432,13689428,13689404,13689383,13689381,13689378,13689375,13689374,13689372,13689370,13689368,13689364,13689363,13521742,13521730";


        String  sql3=   "UPDATE `cowell_cart`.`promotion` SET `coupon_join` = 1 WHERE `id` in ("+prromotionIds+")";

        String  sql4=   " UPDATE `cowell_cart`.`promotion_coupon_install` SET `join_way` = 3 WHERE promotion_id in ("+prromotionIds+")";
        List<String> sl1=new ArrayList<>();
        List<String> sl2=new ArrayList<>();

        System.out.println(sql3);

        System.out.println(sql4);
//        Arrays.stream(prromotionIds.split(",")).forEach(v->{
//             int number=  Integer.parseInt(v);
//            int libraryIndex = ((number / 1000) % 100)%2;
//            // 取后三位作为表索引
//            int tableIndex = number % 1000 % 128;
//
//            System.out.println("库索引: " + libraryIndex);
//            System.out.println("表索引: " + tableIndex);
//            System.out.println("当前数字: " + number);
//
//            StringBuilder  stringBuilder=new StringBuilder(sql2);
//            String newSql = stringBuilder.toString();
//            String replace = newSql.replace("TTTID", number + "").replace("ZIDX", tableIndex + "");
//
//            if(libraryIndex==0){
//                sl1.add(replace);
//            }else {
//                sl2.add(replace);
//            }
//        });
//
//        sl1.stream().forEach(v->{
//            System.out.println(v);
//        });
//        System.out.println("数字____________________________");
//        sl2.stream().forEach(v->{
//            System.out.println(v);
//        });

        for (int i = 0; i < 128;i++){





          StringBuilder newSQL=new StringBuilder(sql2);
        //  newSQL.toString().replace("TTTID",)


          //System.out.println(newSQL);
      }

    }
}
