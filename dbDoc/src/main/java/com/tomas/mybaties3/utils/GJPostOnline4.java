package com.tomas.mybaties3.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IORuntimeException;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSONObject;

import java.io.*;
import java.net.URLEncoder;
import java.util.*;

/**
 * Test 描述
 *
 * <AUTHOR>
 * @create 2021/7/28
 **/
public class GJPostOnline4 {
    public static String epath="/Users/<USER>/code/github/SpringBoot-Learning/dbDoc/src/test/java/com/tomas/mybaties3/testDemo/" ;
    public static void main(String[] args) throws IOException, InterruptedException {

        String path = "/Users/<USER>/Desktop/add.txt" ;
        String result = "/Users/<USER>/Desktop/resultadd.txt" ;
        String str = FileUtil.readUtf8String(new File(path));
        System.out.println(str);
        //String substring1 = str.substring(0, 38);
        //System.out.println(substring1);
        String[] split1 = str.split(",");
       // String[] split1 = substring1.split(",");
        Set<String> hasSet=new HashSet<>();
        List<JSONObject> errJsonObjectList=new ArrayList<>();
        List<String> parse = Arrays.asList(split1);
        for (String v:parse) {
            JSONObject o = new JSONObject();
            o.put("sfz", v.trim());
            //  System.out.println(s);authority: api-store.gaojihealth.cn
            String sfz = v.trim();
            if ( hasSet.contains(sfz)){
              continue;
            }
            String aaa = "ScriptManager1=ScriptManager1%7CbtnQuery&prov=371703&hidPro=371703&hidProName=&city=371703002&hidRoleCode=&hidRoleType=&hidCity=371703002&hidCityName=&area=&hidArea=371703002&hidAreaname=&ddlLaiYuan=99&ddlSearchType=ShenFenZhengHao&txtKeyword=" + sfz + "&ddlPageSize=20&__EVENTTARGET=&__EVENTARGUMENT=&__LASTFOCUS=&__VIEWSTATE=%2FwEPDwUKLTg3Njc3ODkxOQ9kFgICAw9kFgICHQ9kFgJmD2QWBmYPFgIeC18hSXRlbUNvdW50AgEWAmYPZBYIZg8VBQnlrprpmbbljLoM5ruo5rKz6KGX6YGTD%2BS7mOW6hOadkeWnlOS8mgnoi5fmmZ%2FmtrULMTU4NTMwMDkxNjlkAgEPFgIeBFRleHQFEjM3MTcyNzIwKioqKioqMDUyMWQCAg8VAxrpmbbpg73mlrDpn7XljZfljLoxMy0xLTIwMRIzNzE3MjcyMDE2MDEyMjA1MjEG5L%2Bu5pS5ZAIDDw8WBB4LQ29tbWFuZE5hbWUFEjM3MTcyNzIwMTYwMTIyMDUyMR4HVmlzaWJsZWdkZAIBDw8WBh4IUGFnZVNpemUCFB4QQ3VycmVudFBhZ2VJbmRleAIBHgtSZWNvcmRjb3VudAIBZGQCAg8QZGQWAWZkZKSbWf4unq0yX2GJ5ZLFqo5JhuL4uoUwInw2LtVRpe2b&__VIEWSTATEGENERATOR=A38B5F84&__EVENTVALIDATION=%2FwEdABfqJXX4%2BgCYfM0RdyJY8NUqqyCnwJzjYd2C6E1nssLrlUwSX2DWMfZZHFtbUGb2uqLcDpBiweYJVfEUYBuuQXkKaAixY%2FHaFPing3UeG%2F%2Fl3sG3Z%2FDXpvXBzNt%2BB5NoVzoJCflC3V5WqzXSO5YFsTrp8b4IkE6oHqht5eSfKjT%2Bqp%2FHEXC05jPNYWraiRyooU6wlzXD6U6HCyo44SsC85LkbDg8uk3ImiCZtsz4xP9n18N3DA15cemMdivrkJvT4C35rj7FBBBotbrGr3q77D%2FJpW5o4iisOCyLtgmBZEB%2BP48c2QsLbUwrRYunHvgA5irVIaZc5ryiwFevXa%2BaUiGLwdFqoFFl7cgjpOMZzZ1XYdwhOSATDYu4EH0hDkmNH9jODKJOlOfgp5WDchFiD5EfTPcCy8mQq4yE9ltdXmjBbLrZiRCqc8WDJESCLLHi21roD1J50NTVsO6vk7Zy3iszOc1pXpTl%2BIGLQIJyGLPyn%2BO4YwkOVOQkQ3984fkoiFV4TU5EKk6cfLsRdzleo1hT&__ASYNCPOST=true&btnQuery=%E7%A1%AE%E8%AE%A4%E6%9F%A5%E8%AF%A2";
            String[] cmds = {"curl", "-X", "POST",
                    "-H", "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,fr;q=0.7",
                    "-H", "pragma: no-cache",
                    "-H", "Cache-Control: max-age=0",
                    "-H", "Connection: keep-alive",
                    // "-H","authorization: bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" ,
                    "-H", "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                    "-H", "Content-Type: application/x-www-form-urlencoded",
                    "-H", "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36",
                    "-H", "Cookie: ASP.NET_SessionId=cpbjm1cfvy3caus553wbirwc; User_PID=5525e79c-f069-4706-a9fe-9092763b0ce8; User_Name=371703002; User_RoleType=5",
                    "-H", "origin: http://************:8691",
                    "-H", "Upgrade-Insecure-Requests: 1",
                    "-H", "referer: http://************:8691/WebAdmin/SiJi/SheQuList.aspx",

                    "--data-raw", aaa,
                    "--compressed", "http://************:8691/WebAdmin/SiJi/SheQuList.aspx"};

            //System.out.println(JSON.toJSONString(cmds));
            String s0 = execCurl(cmds);
            if (s0.contains("付庄村委会")) {
                // System.out.println(s0.toString());
                String substring = s0.substring(s0.indexOf("__VIEWSTATE"), s0.indexOf("asyncPostBackControlIDs"));
                System.out.println(substring);
                String[] split = substring.split("\\|");
         /*   System.out.println(split[1]);
            System.out.println(split[9]);*/
                String stage = null;
                String action = null;
                try {
                    stage = URLEncoder.encode(split[1].trim(), "UTF-8").trim();
                    action = URLEncoder.encode(split[9].trim(), "UTF-8").trim();
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }

                String url = "ScriptManager1=UpdatePanel1%7CRepeaterMerchant%24ctl00%24lbtnDelete&prov=371703&hidPro=371703&hidProName=&city=371703002&hidRoleCode=&hidRoleType=&hidCity=371703002&hidCityName=&area=&hidArea=371703002&hidAreaname=&ddlLaiYuan=99&ddlSearchType=ShenFenZhengHao&txtKeyword=" + sfz + "&ddlPageSize=20&__EVENTTARGET=RepeaterMerchant%24ctl00%24lbtnDelete&__EVENTARGUMENT=&__LASTFOCUS=&__VIEWSTATE=" + stage + "&__VIEWSTATEGENERATOR=A38B5F84&__EVENTVALIDATION=" + action + "&__ASYNCPOST=true&";
                String[] cmds2 = {"curl", "-X", "POST",
                        "-H", "Accept-Language: zh-CN,zh;q=0.9,en;q=0.8,fr;q=0.7",
                        "-H", "pragma: no-cache",
                        "-H", "Cache-Control: max-age=0",
                        "-H", "Connection: keep-alive",
                        // "-H","authorization: bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" ,
                        "-H", "accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.9",
                        "-H", "Content-Type: application/x-www-form-urlencoded",
                        "-H", "user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36",
                        "-H", "Cookie: ASP.NET_SessionId=cpbjm1cfvy3caus553wbirwc; User_PID=5525e79c-f069-4706-a9fe-9092763b0ce8; User_Name=371703002; User_RoleType=5",
                        "-H", "origin: http://************:8691",
                        "-H", "Upgrade-Insecure-Requests: 1",
                        "-H", "referer: http://************:8691/WebAdmin/SiJi/SheQuList.aspx",
                        "--data-raw", url,
                        "--compressed", "http://************:8691/WebAdmin/SiJi/SheQuList.aspx"};

                //System.out.println(JSON.toJSONString(cmds2));
                String s1 = execCurl(cmds2);
                if (null != s1 && s1.contains("删除用户成功!")) {
                    System.out.println("删除成功" + o.toString());
                    o.put("success", "删除成功");
                    try {
                        FileUtil.appendUtf8String(o.toString() + ",", result);
                    } catch (IORuntimeException e) {
                        //抛出一个运行时异常(直接停止掉程序)
                        throw new RuntimeException("运行时异常", e);
                    }
                } else {
                    int i = s1.indexOf("//<![CDATA[\n" +
                            "alert('");
                    if (i != -1) {
                        String substring2 = s1.substring(i + 19, i + 50);
                        int i1 = substring2.indexOf("');");
                        o.put("error", substring2.substring(0, i1));
                    } else {
                        o.put("error", "删除失败");
                    }
                    try {
                        o.put("error", "删除失败");
                        FileUtil.appendUtf8String(o.toString() + ",", result);
                    } catch (IORuntimeException e) {
                        //抛出一个运行时异常(直接停止掉程序)
                        throw new RuntimeException("运行时异常", e);
                    }
                }

            } else {
                System.out.println("异常**********************************" + s0.toString());
            }
            try {
                Thread.sleep(50L);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            hasSet.add(sfz);
        };
        //excel(errJsonObjectList);
    }


    public static String execCurl(String[] cmds) {
        ProcessBuilder process = new ProcessBuilder(cmds);
        Process p;
        try {
            p = process.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(p.getInputStream()));
            StringBuilder builder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
                builder.append(System.getProperty("line.separator"));
            }
            return builder.toString();

        } catch (IOException e) {
            System.out.print("error");
            e.printStackTrace();
        }
        return null;
    }


    public static void excel(Iterable<?> rowData ){

        // 通过工具类创建writer
        String path1 = epath+"bigWriteMapTest2222222000.xlsx";
        FileUtil.del(path1);
        BigExcelWriter writer = ExcelUtil.getBigWriter(path1);
        // 一次性写出内容，使用默认样式
        writer.writeRow(rowData, true);
        // 关闭writer，释放内存
        writer.close();
    }

}
