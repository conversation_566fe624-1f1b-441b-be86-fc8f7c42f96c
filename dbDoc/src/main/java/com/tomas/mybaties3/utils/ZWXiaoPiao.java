package com.tomas.mybaties3.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Table123 描述
 *
 * <AUTHOR>
 * @create 4/12/23
 **/
public class ZWXiaoPiao {


    public static void main(String[] args)  throws Exception{


        List<String> codes=new ArrayList<>();
       /* codes.add("4090");
        codes.add("4133");


        codes.add("4156");
        codes.add("4173");
*/
        codes.add("4032");
        codes.add("4024");
        codes.add("4025");


        try {
            // Create a BufferedReader object to read the file
            BufferedReader reader = new BufferedReader(new FileReader("/Users/<USER>/Desktop/a.json"));

            // Read each line of the file and print it to the console
            String line = reader.readLine();
            while (line != null) {

                JSONObject jsonObject=JSONObject.parseObject(line);
                System.out.println(jsonObject);
                String property = "bdata.Head.Payments"; // 要获取的属性路径
                Object value = getProperty(jsonObject, property); // 获取属性值

                System.out.println("Value of " + property + ": " + value);
                break;
               /* //System.out.println(object);

                JSONObject bdata = object.getJSONObject("bdata");

                JSONObject head = bdata.getJSONObject("Head");

                JSONArray payments = head.getJSONArray("Payments");

                for (int i = 0; i < payments.size(); i++){
                    //System.out.println(payments.get(i).toString());
                    JSONObject pay=JSONObject.parseObject(payments.get(i).toString());

                    String tendertypecode = pay.getString("TENDERTYPECODE");
                    if(!codes.contains(tendertypecode)){
                        pay.put("TRANSNUMBER",head.getString("TRANSNUMBER"));
                        System.out.println(pay);
                    }
                }*/
                //line = reader.readLine();
            }


            // Close the reader
            reader.close();
        } catch (IOException e) {
            // Handle the exception
            e.printStackTrace();
        }

    }



        public static Object getProperty(JSONObject jsonObject, String property) {
            String[] parts = property.split("\\."); // 将属性路径分解为多个部分
            Object value = jsonObject;
            for (String part : parts) {
                System.out.println(value.getClass());
                if (value instanceof JSONObject) {
                    System.out.println("aaa:" + value);
                    boolean jsonObjectOrArray = isJsonObjectOrArray(((JSONObject) value).toJSONString(), part);
                    System.out.println(jsonObjectOrArray);
                    value = ((JSONObject) value).getJSONObject(part); // 获取 JSON 对象中的属性值
                   // System.out.println(value.getClass());
                } else if (value instanceof JSONArray) {
                    int index = Integer.parseInt(part);
                    value = ((JSONArray) value).get(index); // 获取 JSON 数组中的元素
                    //System.out.println("xxx:" + value);
                    //System.out.println(value.getClass());
                } else {
                    System.out.println("zzz:" + value);
                    return null; // 如果属性路径不正确，则返回 null
                }
            }
            return value; // 返回获取的属性值
        }


    /**
     * 判断 JSON 对象中指定属性值的类型是否为 JSONObject 或者 JSONArray。
     *
     * @param jsonString JSON 字符串
     * @param property   属性名
     * @return 属性值类型为 JSONObject 或者 JSONArray 时返回 true，否则返回 false。
     */
    public static boolean isJsonObjectOrArray(String jsonString, String property) {
        JSONObject jsonObject = JSON.parseObject(jsonString);
        Object value = jsonObject.getJSONObject(property);
        System.out.println("nnn"+value);
        if (value instanceof JSONObject || value instanceof JSONArray) {
            return true;
        }

        return false;
    }

}
