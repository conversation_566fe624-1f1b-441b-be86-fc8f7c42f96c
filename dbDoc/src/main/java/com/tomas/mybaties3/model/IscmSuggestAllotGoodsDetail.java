package com.tomas.mybaties3.model;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * iscm_suggest_allot_goods_detail
 * <AUTHOR>
public class IscmSuggestAllotGoodsDetail implements Serializable {
    /**
     * 主键
     */
    private Long id;

    /**
     * 建议生成日期
     */
    private Date businessDate;

    /**
     * 建议主表id
     */
    private Long recordId;

    /**
     * 登记类型,1:近效期调拨,2:大库存、滞销商品调拨,3:断货商品调拨
     */
    private Byte allotType;

    /**
     * 登记单号
     */
    private String registerNo;

    /**
     * 登记来源 1:手工登记 2:自动登记
     */
    private Byte registerSource;

    /**
     * 登记人ID
     */
    private Long registerBy;

    /**
     * 登记人
     */
    private String registerName;

    /**
     * 登记时间
     */
    private Date registerTime;

    /**
     * 平台orgId
     */
    private Long platformOrgId;

    /**
     * 平台名称
     */
    private String platformOrgName;

    /**
     * 调出公司Id
     */
    private Long outCompanyId;

    /**
     * 调出公司MDM编码
     */
    private String outCompanyCode;

    /**
     * 调出公司名称
     */
    private String outCompanyName;

    /**
     * 调入公司id
     */
    private Long inCompanyId;

    /**
     * 调入公司MDM编码
     */
    private String inCompanyCode;

    /**
     * 调入公司名称
     */
    private String inCompanyName;

    /**
     * 调出门店 id
     */
    private Long outStoreId;

    /**
     * 调出门店MDM编码
     */
    private String outStoreCode;

    /**
     * 调出门店名称
     */
    private String outStoreName;

    /**
     * 调入门店 id
     */
    private Long inStoreId;

    /**
     * 调入门店MDM编码
     */
    private String inStoreCode;

    /**
     * 调入门店名称
     */
    private String inStoreName;

    /**
     * BDP建议唯一id
     */
    private String allotNo;

    /**
     * POS系统审批通过编号
     */
    private String posAllotNo;

    /**
     * 商品编码
     */
    private String goodsNo;

    /**
     * 商品描述
     */
    private String goodsDesc;

    /**
     * 生产批号
     */
    private String batchNo;

    /**
     * 失效日期
     */
    private Date expiryTime;

    /**
     * 通用名
     */
    private String goodsCommonName;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 剂型
     */
    private String dosageForm;

    /**
     * 单位
     */
    private String unit;

    /**
     * 商品是否有季节属性 0:否 1:是 
     */
    private Byte season;

    /**
     * 退货条件
     */
    private String returnCondition;

    /**
     * 建议调出数量
     */
    private BigDecimal suggestAllotQuantity;

    /**
     * 实际调出数量
     */
    private BigDecimal realAllotQuantity;

    /**
     * 单条调拨成本金额
     */
    private BigDecimal transferCostAmount;

    /**
     * 调入门店近30天销量
     */
    private BigDecimal inStoreThirtyDaysSales;

    /**
     * 调出门店近30天销量
     */
    private BigDecimal outStoreThirtyDaysSales;

    /**
     * 调入门店近30天销售次数
     */
    private BigDecimal inStoreThirtyDaysSaleTimes;

    /**
     * 调出门店近30天销售次数
     */
    private BigDecimal outStoreThirtyDaysSaleTimes;

    /**
     * 调入门店库存
     */
    private BigDecimal inStoreStock;

    /**
     * 调出门店库存
     */
    private BigDecimal outStoreStock;

    /**
     * 调入门店库存下限
     */
    private BigDecimal inStoreStockMin;

    /**
     * 调出门店库存下限
     */
    private BigDecimal outStoreStockMin;

    /**
     * 调入门店库存上限
     */
    private BigDecimal inStoreStockMax;

    /**
     * 调出门店库存上限
     */
    private BigDecimal outStoreStockMax;

    /**
     * 调入门店在途库存
     */
    private BigDecimal inStoreStockOnway;

    /**
     * 调出门店在途库存
     */
    private BigDecimal outStoreStockOnway;

    /**
     * 调入门店POS日均销量
     */
    private BigDecimal inStorePosDailySales;

    /**
     * 调出门店POS日均销量
     */
    private BigDecimal outStorePosDailySales;

    /**
     * 调入门店预计可销天数(建议调拨数量/pos日销量)
     */
    private BigDecimal inStoreEstimateSalesDays;

    /**
     * 调出门店预计可销天数(建议调拨数量/pos日销量)
     */
    private BigDecimal outStoreEstimateSalesDays;

    /**
     * 审批状态 0:未审批 1:审批通过 2:作废
     */
    private Byte approveStatus;

    /**
     * 审批结果说明
     */
    private String approveResult;

    /**
     * 审批人ID
     */
    private Long approveBy;

    /**
     * 审批人
     */
    private String approveName;

    /**
     * 审批时间
     */
    private Date approveTime;

    /**
     * 状态(-1删除，0正常)
     */
    private Byte status;

    /**
     * 创建时间
     */
    private Date gmtCreate;

    /**
     * 更新时间
     */
    private Date gmtUpdate;

    /**
     * 扩展字段
     */
    private String extend;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建人ID
     */
    private Long createdBy;

    /**
     * 创建人
     */
    private String createdName;

    /**
     * 更新人ID
     */
    private Long updatedBy;

    /**
     * 更新人
     */
    private String updatedName;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Date getBusinessDate() {
        return businessDate;
    }

    public void setBusinessDate(Date businessDate) {
        this.businessDate = businessDate;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public Byte getAllotType() {
        return allotType;
    }

    public void setAllotType(Byte allotType) {
        this.allotType = allotType;
    }

    public String getRegisterNo() {
        return registerNo;
    }

    public void setRegisterNo(String registerNo) {
        this.registerNo = registerNo;
    }

    public Byte getRegisterSource() {
        return registerSource;
    }

    public void setRegisterSource(Byte registerSource) {
        this.registerSource = registerSource;
    }

    public Long getRegisterBy() {
        return registerBy;
    }

    public void setRegisterBy(Long registerBy) {
        this.registerBy = registerBy;
    }

    public String getRegisterName() {
        return registerName;
    }

    public void setRegisterName(String registerName) {
        this.registerName = registerName;
    }

    public Date getRegisterTime() {
        return registerTime;
    }

    public void setRegisterTime(Date registerTime) {
        this.registerTime = registerTime;
    }

    public Long getPlatformOrgId() {
        return platformOrgId;
    }

    public void setPlatformOrgId(Long platformOrgId) {
        this.platformOrgId = platformOrgId;
    }

    public String getPlatformOrgName() {
        return platformOrgName;
    }

    public void setPlatformOrgName(String platformOrgName) {
        this.platformOrgName = platformOrgName;
    }

    public Long getOutCompanyId() {
        return outCompanyId;
    }

    public void setOutCompanyId(Long outCompanyId) {
        this.outCompanyId = outCompanyId;
    }

    public String getOutCompanyCode() {
        return outCompanyCode;
    }

    public void setOutCompanyCode(String outCompanyCode) {
        this.outCompanyCode = outCompanyCode;
    }

    public String getOutCompanyName() {
        return outCompanyName;
    }

    public void setOutCompanyName(String outCompanyName) {
        this.outCompanyName = outCompanyName;
    }

    public Long getInCompanyId() {
        return inCompanyId;
    }

    public void setInCompanyId(Long inCompanyId) {
        this.inCompanyId = inCompanyId;
    }

    public String getInCompanyCode() {
        return inCompanyCode;
    }

    public void setInCompanyCode(String inCompanyCode) {
        this.inCompanyCode = inCompanyCode;
    }

    public String getInCompanyName() {
        return inCompanyName;
    }

    public void setInCompanyName(String inCompanyName) {
        this.inCompanyName = inCompanyName;
    }

    public Long getOutStoreId() {
        return outStoreId;
    }

    public void setOutStoreId(Long outStoreId) {
        this.outStoreId = outStoreId;
    }

    public String getOutStoreCode() {
        return outStoreCode;
    }

    public void setOutStoreCode(String outStoreCode) {
        this.outStoreCode = outStoreCode;
    }

    public String getOutStoreName() {
        return outStoreName;
    }

    public void setOutStoreName(String outStoreName) {
        this.outStoreName = outStoreName;
    }

    public Long getInStoreId() {
        return inStoreId;
    }

    public void setInStoreId(Long inStoreId) {
        this.inStoreId = inStoreId;
    }

    public String getInStoreCode() {
        return inStoreCode;
    }

    public void setInStoreCode(String inStoreCode) {
        this.inStoreCode = inStoreCode;
    }

    public String getInStoreName() {
        return inStoreName;
    }

    public void setInStoreName(String inStoreName) {
        this.inStoreName = inStoreName;
    }

    public String getAllotNo() {
        return allotNo;
    }

    public void setAllotNo(String allotNo) {
        this.allotNo = allotNo;
    }

    public String getPosAllotNo() {
        return posAllotNo;
    }

    public void setPosAllotNo(String posAllotNo) {
        this.posAllotNo = posAllotNo;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsDesc() {
        return goodsDesc;
    }

    public void setGoodsDesc(String goodsDesc) {
        this.goodsDesc = goodsDesc;
    }

    public String getBatchNo() {
        return batchNo;
    }

    public void setBatchNo(String batchNo) {
        this.batchNo = batchNo;
    }

    public Date getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(Date expiryTime) {
        this.expiryTime = expiryTime;
    }

    public String getGoodsCommonName() {
        return goodsCommonName;
    }

    public void setGoodsCommonName(String goodsCommonName) {
        this.goodsCommonName = goodsCommonName;
    }

    public String getManufacturer() {
        return manufacturer;
    }

    public void setManufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
    }

    public String getDosageForm() {
        return dosageForm;
    }

    public void setDosageForm(String dosageForm) {
        this.dosageForm = dosageForm;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public Byte getSeason() {
        return season;
    }

    public void setSeason(Byte season) {
        this.season = season;
    }

    public String getReturnCondition() {
        return returnCondition;
    }

    public void setReturnCondition(String returnCondition) {
        this.returnCondition = returnCondition;
    }

    public BigDecimal getSuggestAllotQuantity() {
        return suggestAllotQuantity;
    }

    public void setSuggestAllotQuantity(BigDecimal suggestAllotQuantity) {
        this.suggestAllotQuantity = suggestAllotQuantity;
    }

    public BigDecimal getRealAllotQuantity() {
        return realAllotQuantity;
    }

    public void setRealAllotQuantity(BigDecimal realAllotQuantity) {
        this.realAllotQuantity = realAllotQuantity;
    }

    public BigDecimal getTransferCostAmount() {
        return transferCostAmount;
    }

    public void setTransferCostAmount(BigDecimal transferCostAmount) {
        this.transferCostAmount = transferCostAmount;
    }

    public BigDecimal getInStoreThirtyDaysSales() {
        return inStoreThirtyDaysSales;
    }

    public void setInStoreThirtyDaysSales(BigDecimal inStoreThirtyDaysSales) {
        this.inStoreThirtyDaysSales = inStoreThirtyDaysSales;
    }

    public BigDecimal getOutStoreThirtyDaysSales() {
        return outStoreThirtyDaysSales;
    }

    public void setOutStoreThirtyDaysSales(BigDecimal outStoreThirtyDaysSales) {
        this.outStoreThirtyDaysSales = outStoreThirtyDaysSales;
    }

    public BigDecimal getInStoreThirtyDaysSaleTimes() {
        return inStoreThirtyDaysSaleTimes;
    }

    public void setInStoreThirtyDaysSaleTimes(BigDecimal inStoreThirtyDaysSaleTimes) {
        this.inStoreThirtyDaysSaleTimes = inStoreThirtyDaysSaleTimes;
    }

    public BigDecimal getOutStoreThirtyDaysSaleTimes() {
        return outStoreThirtyDaysSaleTimes;
    }

    public void setOutStoreThirtyDaysSaleTimes(BigDecimal outStoreThirtyDaysSaleTimes) {
        this.outStoreThirtyDaysSaleTimes = outStoreThirtyDaysSaleTimes;
    }

    public BigDecimal getInStoreStock() {
        return inStoreStock;
    }

    public void setInStoreStock(BigDecimal inStoreStock) {
        this.inStoreStock = inStoreStock;
    }

    public BigDecimal getOutStoreStock() {
        return outStoreStock;
    }

    public void setOutStoreStock(BigDecimal outStoreStock) {
        this.outStoreStock = outStoreStock;
    }

    public BigDecimal getInStoreStockMin() {
        return inStoreStockMin;
    }

    public void setInStoreStockMin(BigDecimal inStoreStockMin) {
        this.inStoreStockMin = inStoreStockMin;
    }

    public BigDecimal getOutStoreStockMin() {
        return outStoreStockMin;
    }

    public void setOutStoreStockMin(BigDecimal outStoreStockMin) {
        this.outStoreStockMin = outStoreStockMin;
    }

    public BigDecimal getInStoreStockMax() {
        return inStoreStockMax;
    }

    public void setInStoreStockMax(BigDecimal inStoreStockMax) {
        this.inStoreStockMax = inStoreStockMax;
    }

    public BigDecimal getOutStoreStockMax() {
        return outStoreStockMax;
    }

    public void setOutStoreStockMax(BigDecimal outStoreStockMax) {
        this.outStoreStockMax = outStoreStockMax;
    }

    public BigDecimal getInStoreStockOnway() {
        return inStoreStockOnway;
    }

    public void setInStoreStockOnway(BigDecimal inStoreStockOnway) {
        this.inStoreStockOnway = inStoreStockOnway;
    }

    public BigDecimal getOutStoreStockOnway() {
        return outStoreStockOnway;
    }

    public void setOutStoreStockOnway(BigDecimal outStoreStockOnway) {
        this.outStoreStockOnway = outStoreStockOnway;
    }

    public BigDecimal getInStorePosDailySales() {
        return inStorePosDailySales;
    }

    public void setInStorePosDailySales(BigDecimal inStorePosDailySales) {
        this.inStorePosDailySales = inStorePosDailySales;
    }

    public BigDecimal getOutStorePosDailySales() {
        return outStorePosDailySales;
    }

    public void setOutStorePosDailySales(BigDecimal outStorePosDailySales) {
        this.outStorePosDailySales = outStorePosDailySales;
    }

    public BigDecimal getInStoreEstimateSalesDays() {
        return inStoreEstimateSalesDays;
    }

    public void setInStoreEstimateSalesDays(BigDecimal inStoreEstimateSalesDays) {
        this.inStoreEstimateSalesDays = inStoreEstimateSalesDays;
    }

    public BigDecimal getOutStoreEstimateSalesDays() {
        return outStoreEstimateSalesDays;
    }

    public void setOutStoreEstimateSalesDays(BigDecimal outStoreEstimateSalesDays) {
        this.outStoreEstimateSalesDays = outStoreEstimateSalesDays;
    }

    public Byte getApproveStatus() {
        return approveStatus;
    }

    public void setApproveStatus(Byte approveStatus) {
        this.approveStatus = approveStatus;
    }

    public String getApproveResult() {
        return approveResult;
    }

    public void setApproveResult(String approveResult) {
        this.approveResult = approveResult;
    }

    public Long getApproveBy() {
        return approveBy;
    }

    public void setApproveBy(Long approveBy) {
        this.approveBy = approveBy;
    }

    public String getApproveName() {
        return approveName;
    }

    public void setApproveName(String approveName) {
        this.approveName = approveName;
    }

    public Date getApproveTime() {
        return approveTime;
    }

    public void setApproveTime(Date approveTime) {
        this.approveTime = approveTime;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public Date getGmtCreate() {
        return gmtCreate;
    }

    public void setGmtCreate(Date gmtCreate) {
        this.gmtCreate = gmtCreate;
    }

    public Date getGmtUpdate() {
        return gmtUpdate;
    }

    public void setGmtUpdate(Date gmtUpdate) {
        this.gmtUpdate = gmtUpdate;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Long getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(Long createdBy) {
        this.createdBy = createdBy;
    }

    public String getCreatedName() {
        return createdName;
    }

    public void setCreatedName(String createdName) {
        this.createdName = createdName;
    }

    public Long getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(Long updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getUpdatedName() {
        return updatedName;
    }

    public void setUpdatedName(String updatedName) {
        this.updatedName = updatedName;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        IscmSuggestAllotGoodsDetail other = (IscmSuggestAllotGoodsDetail) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getBusinessDate() == null ? other.getBusinessDate() == null : this.getBusinessDate().equals(other.getBusinessDate()))
            && (this.getRecordId() == null ? other.getRecordId() == null : this.getRecordId().equals(other.getRecordId()))
            && (this.getAllotType() == null ? other.getAllotType() == null : this.getAllotType().equals(other.getAllotType()))
            && (this.getRegisterNo() == null ? other.getRegisterNo() == null : this.getRegisterNo().equals(other.getRegisterNo()))
            && (this.getRegisterSource() == null ? other.getRegisterSource() == null : this.getRegisterSource().equals(other.getRegisterSource()))
            && (this.getRegisterBy() == null ? other.getRegisterBy() == null : this.getRegisterBy().equals(other.getRegisterBy()))
            && (this.getRegisterName() == null ? other.getRegisterName() == null : this.getRegisterName().equals(other.getRegisterName()))
            && (this.getRegisterTime() == null ? other.getRegisterTime() == null : this.getRegisterTime().equals(other.getRegisterTime()))
            && (this.getPlatformOrgId() == null ? other.getPlatformOrgId() == null : this.getPlatformOrgId().equals(other.getPlatformOrgId()))
            && (this.getPlatformOrgName() == null ? other.getPlatformOrgName() == null : this.getPlatformOrgName().equals(other.getPlatformOrgName()))
            && (this.getOutCompanyId() == null ? other.getOutCompanyId() == null : this.getOutCompanyId().equals(other.getOutCompanyId()))
            && (this.getOutCompanyCode() == null ? other.getOutCompanyCode() == null : this.getOutCompanyCode().equals(other.getOutCompanyCode()))
            && (this.getOutCompanyName() == null ? other.getOutCompanyName() == null : this.getOutCompanyName().equals(other.getOutCompanyName()))
            && (this.getInCompanyId() == null ? other.getInCompanyId() == null : this.getInCompanyId().equals(other.getInCompanyId()))
            && (this.getInCompanyCode() == null ? other.getInCompanyCode() == null : this.getInCompanyCode().equals(other.getInCompanyCode()))
            && (this.getInCompanyName() == null ? other.getInCompanyName() == null : this.getInCompanyName().equals(other.getInCompanyName()))
            && (this.getOutStoreId() == null ? other.getOutStoreId() == null : this.getOutStoreId().equals(other.getOutStoreId()))
            && (this.getOutStoreCode() == null ? other.getOutStoreCode() == null : this.getOutStoreCode().equals(other.getOutStoreCode()))
            && (this.getOutStoreName() == null ? other.getOutStoreName() == null : this.getOutStoreName().equals(other.getOutStoreName()))
            && (this.getInStoreId() == null ? other.getInStoreId() == null : this.getInStoreId().equals(other.getInStoreId()))
            && (this.getInStoreCode() == null ? other.getInStoreCode() == null : this.getInStoreCode().equals(other.getInStoreCode()))
            && (this.getInStoreName() == null ? other.getInStoreName() == null : this.getInStoreName().equals(other.getInStoreName()))
            && (this.getAllotNo() == null ? other.getAllotNo() == null : this.getAllotNo().equals(other.getAllotNo()))
            && (this.getPosAllotNo() == null ? other.getPosAllotNo() == null : this.getPosAllotNo().equals(other.getPosAllotNo()))
            && (this.getGoodsNo() == null ? other.getGoodsNo() == null : this.getGoodsNo().equals(other.getGoodsNo()))
            && (this.getGoodsDesc() == null ? other.getGoodsDesc() == null : this.getGoodsDesc().equals(other.getGoodsDesc()))
            && (this.getBatchNo() == null ? other.getBatchNo() == null : this.getBatchNo().equals(other.getBatchNo()))
            && (this.getExpiryTime() == null ? other.getExpiryTime() == null : this.getExpiryTime().equals(other.getExpiryTime()))
            && (this.getGoodsCommonName() == null ? other.getGoodsCommonName() == null : this.getGoodsCommonName().equals(other.getGoodsCommonName()))
            && (this.getManufacturer() == null ? other.getManufacturer() == null : this.getManufacturer().equals(other.getManufacturer()))
            && (this.getDosageForm() == null ? other.getDosageForm() == null : this.getDosageForm().equals(other.getDosageForm()))
            && (this.getUnit() == null ? other.getUnit() == null : this.getUnit().equals(other.getUnit()))
            && (this.getSeason() == null ? other.getSeason() == null : this.getSeason().equals(other.getSeason()))
            && (this.getReturnCondition() == null ? other.getReturnCondition() == null : this.getReturnCondition().equals(other.getReturnCondition()))
            && (this.getSuggestAllotQuantity() == null ? other.getSuggestAllotQuantity() == null : this.getSuggestAllotQuantity().equals(other.getSuggestAllotQuantity()))
            && (this.getRealAllotQuantity() == null ? other.getRealAllotQuantity() == null : this.getRealAllotQuantity().equals(other.getRealAllotQuantity()))
            && (this.getTransferCostAmount() == null ? other.getTransferCostAmount() == null : this.getTransferCostAmount().equals(other.getTransferCostAmount()))
            && (this.getInStoreThirtyDaysSales() == null ? other.getInStoreThirtyDaysSales() == null : this.getInStoreThirtyDaysSales().equals(other.getInStoreThirtyDaysSales()))
            && (this.getOutStoreThirtyDaysSales() == null ? other.getOutStoreThirtyDaysSales() == null : this.getOutStoreThirtyDaysSales().equals(other.getOutStoreThirtyDaysSales()))
            && (this.getInStoreThirtyDaysSaleTimes() == null ? other.getInStoreThirtyDaysSaleTimes() == null : this.getInStoreThirtyDaysSaleTimes().equals(other.getInStoreThirtyDaysSaleTimes()))
            && (this.getOutStoreThirtyDaysSaleTimes() == null ? other.getOutStoreThirtyDaysSaleTimes() == null : this.getOutStoreThirtyDaysSaleTimes().equals(other.getOutStoreThirtyDaysSaleTimes()))
            && (this.getInStoreStock() == null ? other.getInStoreStock() == null : this.getInStoreStock().equals(other.getInStoreStock()))
            && (this.getOutStoreStock() == null ? other.getOutStoreStock() == null : this.getOutStoreStock().equals(other.getOutStoreStock()))
            && (this.getInStoreStockMin() == null ? other.getInStoreStockMin() == null : this.getInStoreStockMin().equals(other.getInStoreStockMin()))
            && (this.getOutStoreStockMin() == null ? other.getOutStoreStockMin() == null : this.getOutStoreStockMin().equals(other.getOutStoreStockMin()))
            && (this.getInStoreStockMax() == null ? other.getInStoreStockMax() == null : this.getInStoreStockMax().equals(other.getInStoreStockMax()))
            && (this.getOutStoreStockMax() == null ? other.getOutStoreStockMax() == null : this.getOutStoreStockMax().equals(other.getOutStoreStockMax()))
            && (this.getInStoreStockOnway() == null ? other.getInStoreStockOnway() == null : this.getInStoreStockOnway().equals(other.getInStoreStockOnway()))
            && (this.getOutStoreStockOnway() == null ? other.getOutStoreStockOnway() == null : this.getOutStoreStockOnway().equals(other.getOutStoreStockOnway()))
            && (this.getInStorePosDailySales() == null ? other.getInStorePosDailySales() == null : this.getInStorePosDailySales().equals(other.getInStorePosDailySales()))
            && (this.getOutStorePosDailySales() == null ? other.getOutStorePosDailySales() == null : this.getOutStorePosDailySales().equals(other.getOutStorePosDailySales()))
            && (this.getInStoreEstimateSalesDays() == null ? other.getInStoreEstimateSalesDays() == null : this.getInStoreEstimateSalesDays().equals(other.getInStoreEstimateSalesDays()))
            && (this.getOutStoreEstimateSalesDays() == null ? other.getOutStoreEstimateSalesDays() == null : this.getOutStoreEstimateSalesDays().equals(other.getOutStoreEstimateSalesDays()))
            && (this.getApproveStatus() == null ? other.getApproveStatus() == null : this.getApproveStatus().equals(other.getApproveStatus()))
            && (this.getApproveResult() == null ? other.getApproveResult() == null : this.getApproveResult().equals(other.getApproveResult()))
            && (this.getApproveBy() == null ? other.getApproveBy() == null : this.getApproveBy().equals(other.getApproveBy()))
            && (this.getApproveName() == null ? other.getApproveName() == null : this.getApproveName().equals(other.getApproveName()))
            && (this.getApproveTime() == null ? other.getApproveTime() == null : this.getApproveTime().equals(other.getApproveTime()))
            && (this.getStatus() == null ? other.getStatus() == null : this.getStatus().equals(other.getStatus()))
            && (this.getGmtCreate() == null ? other.getGmtCreate() == null : this.getGmtCreate().equals(other.getGmtCreate()))
            && (this.getGmtUpdate() == null ? other.getGmtUpdate() == null : this.getGmtUpdate().equals(other.getGmtUpdate()))
            && (this.getExtend() == null ? other.getExtend() == null : this.getExtend().equals(other.getExtend()))
            && (this.getVersion() == null ? other.getVersion() == null : this.getVersion().equals(other.getVersion()))
            && (this.getCreatedBy() == null ? other.getCreatedBy() == null : this.getCreatedBy().equals(other.getCreatedBy()))
            && (this.getCreatedName() == null ? other.getCreatedName() == null : this.getCreatedName().equals(other.getCreatedName()))
            && (this.getUpdatedBy() == null ? other.getUpdatedBy() == null : this.getUpdatedBy().equals(other.getUpdatedBy()))
            && (this.getUpdatedName() == null ? other.getUpdatedName() == null : this.getUpdatedName().equals(other.getUpdatedName()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getBusinessDate() == null) ? 0 : getBusinessDate().hashCode());
        result = prime * result + ((getRecordId() == null) ? 0 : getRecordId().hashCode());
        result = prime * result + ((getAllotType() == null) ? 0 : getAllotType().hashCode());
        result = prime * result + ((getRegisterNo() == null) ? 0 : getRegisterNo().hashCode());
        result = prime * result + ((getRegisterSource() == null) ? 0 : getRegisterSource().hashCode());
        result = prime * result + ((getRegisterBy() == null) ? 0 : getRegisterBy().hashCode());
        result = prime * result + ((getRegisterName() == null) ? 0 : getRegisterName().hashCode());
        result = prime * result + ((getRegisterTime() == null) ? 0 : getRegisterTime().hashCode());
        result = prime * result + ((getPlatformOrgId() == null) ? 0 : getPlatformOrgId().hashCode());
        result = prime * result + ((getPlatformOrgName() == null) ? 0 : getPlatformOrgName().hashCode());
        result = prime * result + ((getOutCompanyId() == null) ? 0 : getOutCompanyId().hashCode());
        result = prime * result + ((getOutCompanyCode() == null) ? 0 : getOutCompanyCode().hashCode());
        result = prime * result + ((getOutCompanyName() == null) ? 0 : getOutCompanyName().hashCode());
        result = prime * result + ((getInCompanyId() == null) ? 0 : getInCompanyId().hashCode());
        result = prime * result + ((getInCompanyCode() == null) ? 0 : getInCompanyCode().hashCode());
        result = prime * result + ((getInCompanyName() == null) ? 0 : getInCompanyName().hashCode());
        result = prime * result + ((getOutStoreId() == null) ? 0 : getOutStoreId().hashCode());
        result = prime * result + ((getOutStoreCode() == null) ? 0 : getOutStoreCode().hashCode());
        result = prime * result + ((getOutStoreName() == null) ? 0 : getOutStoreName().hashCode());
        result = prime * result + ((getInStoreId() == null) ? 0 : getInStoreId().hashCode());
        result = prime * result + ((getInStoreCode() == null) ? 0 : getInStoreCode().hashCode());
        result = prime * result + ((getInStoreName() == null) ? 0 : getInStoreName().hashCode());
        result = prime * result + ((getAllotNo() == null) ? 0 : getAllotNo().hashCode());
        result = prime * result + ((getPosAllotNo() == null) ? 0 : getPosAllotNo().hashCode());
        result = prime * result + ((getGoodsNo() == null) ? 0 : getGoodsNo().hashCode());
        result = prime * result + ((getGoodsDesc() == null) ? 0 : getGoodsDesc().hashCode());
        result = prime * result + ((getBatchNo() == null) ? 0 : getBatchNo().hashCode());
        result = prime * result + ((getExpiryTime() == null) ? 0 : getExpiryTime().hashCode());
        result = prime * result + ((getGoodsCommonName() == null) ? 0 : getGoodsCommonName().hashCode());
        result = prime * result + ((getManufacturer() == null) ? 0 : getManufacturer().hashCode());
        result = prime * result + ((getDosageForm() == null) ? 0 : getDosageForm().hashCode());
        result = prime * result + ((getUnit() == null) ? 0 : getUnit().hashCode());
        result = prime * result + ((getSeason() == null) ? 0 : getSeason().hashCode());
        result = prime * result + ((getReturnCondition() == null) ? 0 : getReturnCondition().hashCode());
        result = prime * result + ((getSuggestAllotQuantity() == null) ? 0 : getSuggestAllotQuantity().hashCode());
        result = prime * result + ((getRealAllotQuantity() == null) ? 0 : getRealAllotQuantity().hashCode());
        result = prime * result + ((getTransferCostAmount() == null) ? 0 : getTransferCostAmount().hashCode());
        result = prime * result + ((getInStoreThirtyDaysSales() == null) ? 0 : getInStoreThirtyDaysSales().hashCode());
        result = prime * result + ((getOutStoreThirtyDaysSales() == null) ? 0 : getOutStoreThirtyDaysSales().hashCode());
        result = prime * result + ((getInStoreThirtyDaysSaleTimes() == null) ? 0 : getInStoreThirtyDaysSaleTimes().hashCode());
        result = prime * result + ((getOutStoreThirtyDaysSaleTimes() == null) ? 0 : getOutStoreThirtyDaysSaleTimes().hashCode());
        result = prime * result + ((getInStoreStock() == null) ? 0 : getInStoreStock().hashCode());
        result = prime * result + ((getOutStoreStock() == null) ? 0 : getOutStoreStock().hashCode());
        result = prime * result + ((getInStoreStockMin() == null) ? 0 : getInStoreStockMin().hashCode());
        result = prime * result + ((getOutStoreStockMin() == null) ? 0 : getOutStoreStockMin().hashCode());
        result = prime * result + ((getInStoreStockMax() == null) ? 0 : getInStoreStockMax().hashCode());
        result = prime * result + ((getOutStoreStockMax() == null) ? 0 : getOutStoreStockMax().hashCode());
        result = prime * result + ((getInStoreStockOnway() == null) ? 0 : getInStoreStockOnway().hashCode());
        result = prime * result + ((getOutStoreStockOnway() == null) ? 0 : getOutStoreStockOnway().hashCode());
        result = prime * result + ((getInStorePosDailySales() == null) ? 0 : getInStorePosDailySales().hashCode());
        result = prime * result + ((getOutStorePosDailySales() == null) ? 0 : getOutStorePosDailySales().hashCode());
        result = prime * result + ((getInStoreEstimateSalesDays() == null) ? 0 : getInStoreEstimateSalesDays().hashCode());
        result = prime * result + ((getOutStoreEstimateSalesDays() == null) ? 0 : getOutStoreEstimateSalesDays().hashCode());
        result = prime * result + ((getApproveStatus() == null) ? 0 : getApproveStatus().hashCode());
        result = prime * result + ((getApproveResult() == null) ? 0 : getApproveResult().hashCode());
        result = prime * result + ((getApproveBy() == null) ? 0 : getApproveBy().hashCode());
        result = prime * result + ((getApproveName() == null) ? 0 : getApproveName().hashCode());
        result = prime * result + ((getApproveTime() == null) ? 0 : getApproveTime().hashCode());
        result = prime * result + ((getStatus() == null) ? 0 : getStatus().hashCode());
        result = prime * result + ((getGmtCreate() == null) ? 0 : getGmtCreate().hashCode());
        result = prime * result + ((getGmtUpdate() == null) ? 0 : getGmtUpdate().hashCode());
        result = prime * result + ((getExtend() == null) ? 0 : getExtend().hashCode());
        result = prime * result + ((getVersion() == null) ? 0 : getVersion().hashCode());
        result = prime * result + ((getCreatedBy() == null) ? 0 : getCreatedBy().hashCode());
        result = prime * result + ((getCreatedName() == null) ? 0 : getCreatedName().hashCode());
        result = prime * result + ((getUpdatedBy() == null) ? 0 : getUpdatedBy().hashCode());
        result = prime * result + ((getUpdatedName() == null) ? 0 : getUpdatedName().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", businessDate=").append(businessDate);
        sb.append(", recordId=").append(recordId);
        sb.append(", allotType=").append(allotType);
        sb.append(", registerNo=").append(registerNo);
        sb.append(", registerSource=").append(registerSource);
        sb.append(", registerBy=").append(registerBy);
        sb.append(", registerName=").append(registerName);
        sb.append(", registerTime=").append(registerTime);
        sb.append(", platformOrgId=").append(platformOrgId);
        sb.append(", platformOrgName=").append(platformOrgName);
        sb.append(", outCompanyId=").append(outCompanyId);
        sb.append(", outCompanyCode=").append(outCompanyCode);
        sb.append(", outCompanyName=").append(outCompanyName);
        sb.append(", inCompanyId=").append(inCompanyId);
        sb.append(", inCompanyCode=").append(inCompanyCode);
        sb.append(", inCompanyName=").append(inCompanyName);
        sb.append(", outStoreId=").append(outStoreId);
        sb.append(", outStoreCode=").append(outStoreCode);
        sb.append(", outStoreName=").append(outStoreName);
        sb.append(", inStoreId=").append(inStoreId);
        sb.append(", inStoreCode=").append(inStoreCode);
        sb.append(", inStoreName=").append(inStoreName);
        sb.append(", allotNo=").append(allotNo);
        sb.append(", posAllotNo=").append(posAllotNo);
        sb.append(", goodsNo=").append(goodsNo);
        sb.append(", goodsDesc=").append(goodsDesc);
        sb.append(", batchNo=").append(batchNo);
        sb.append(", expiryTime=").append(expiryTime);
        sb.append(", goodsCommonName=").append(goodsCommonName);
        sb.append(", manufacturer=").append(manufacturer);
        sb.append(", dosageForm=").append(dosageForm);
        sb.append(", unit=").append(unit);
        sb.append(", season=").append(season);
        sb.append(", returnCondition=").append(returnCondition);
        sb.append(", suggestAllotQuantity=").append(suggestAllotQuantity);
        sb.append(", realAllotQuantity=").append(realAllotQuantity);
        sb.append(", transferCostAmount=").append(transferCostAmount);
        sb.append(", inStoreThirtyDaysSales=").append(inStoreThirtyDaysSales);
        sb.append(", outStoreThirtyDaysSales=").append(outStoreThirtyDaysSales);
        sb.append(", inStoreThirtyDaysSaleTimes=").append(inStoreThirtyDaysSaleTimes);
        sb.append(", outStoreThirtyDaysSaleTimes=").append(outStoreThirtyDaysSaleTimes);
        sb.append(", inStoreStock=").append(inStoreStock);
        sb.append(", outStoreStock=").append(outStoreStock);
        sb.append(", inStoreStockMin=").append(inStoreStockMin);
        sb.append(", outStoreStockMin=").append(outStoreStockMin);
        sb.append(", inStoreStockMax=").append(inStoreStockMax);
        sb.append(", outStoreStockMax=").append(outStoreStockMax);
        sb.append(", inStoreStockOnway=").append(inStoreStockOnway);
        sb.append(", outStoreStockOnway=").append(outStoreStockOnway);
        sb.append(", inStorePosDailySales=").append(inStorePosDailySales);
        sb.append(", outStorePosDailySales=").append(outStorePosDailySales);
        sb.append(", inStoreEstimateSalesDays=").append(inStoreEstimateSalesDays);
        sb.append(", outStoreEstimateSalesDays=").append(outStoreEstimateSalesDays);
        sb.append(", approveStatus=").append(approveStatus);
        sb.append(", approveResult=").append(approveResult);
        sb.append(", approveBy=").append(approveBy);
        sb.append(", approveName=").append(approveName);
        sb.append(", approveTime=").append(approveTime);
        sb.append(", status=").append(status);
        sb.append(", gmtCreate=").append(gmtCreate);
        sb.append(", gmtUpdate=").append(gmtUpdate);
        sb.append(", extend=").append(extend);
        sb.append(", version=").append(version);
        sb.append(", createdBy=").append(createdBy);
        sb.append(", createdName=").append(createdName);
        sb.append(", updatedBy=").append(updatedBy);
        sb.append(", updatedName=").append(updatedName);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }

    public static void main(String[] args) {
        String substring = "DBJXQ-A11L-*************".split("-")[2].substring(0, 6);

        System.out.println("DBJXQ-A11L-*************".split("-")[2].substring(0, 6));


        System.out.println(substring.concat("-").concat(substring));

    }
}
