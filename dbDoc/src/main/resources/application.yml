spring:
  datasource:
    name: test
    url: *************************************************************************************************************************************
    username: ttjb
    password: yjkj305!@#-db-test        # 使用druid数据源
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.tomas.mybatis


logging:
  level:
    org:
      mybatis:
        spring: DEBUG
      springframework: DEBUG
    com:
      tomas:
        mybatis:
          dao: DEBUG
      ibatis:
       common.jdbc.SimpleDataSource: DEBUG
       common.jdbc.ScriptRunner: DEBUG
       sqlmap.engine.impl.SqlMapClientDelegate: DEBUG
    java.sql:
     Connection: DEBUG
     Statement: DEBUG
     PreparedStatement: DEBUG
