package com.tomas.mybaties3.hutollexcel;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.hutool.poi.excel.style.StyleUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.poi.ss.usermodel.*;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;
import java.util.concurrent.*;

public class TestCollects {
    private final Logger log = LoggerFactory.getLogger(TestCollects.class);
   public static String path="/Users/<USER>/code/github/SpringBoot-Learning/dbDoc/src/test/java/com/tomas/mybaties3/testDemo/" ;


    public static void main(String[] args) {
        ExcelReader reader = ExcelUtil.getReader(path + "111.xlsx");
        Set<Long> bianma=new LinkedHashSet<>();
        Set<Long> bianma1=new LinkedHashSet<>();
        Set<Long> bianma2=new LinkedHashSet<>();
        Set<Long> bianma3=new LinkedHashSet<>();
        Set<Long> bianma4=new LinkedHashSet<>();
        Set<String> mc=new LinkedHashSet<>();
        Set<String> mc1=new LinkedHashSet<>();
        Set<String> mc2=new LinkedHashSet<>();
        Set<String> mc3=new LinkedHashSet<>();
        Set<String> mc4=new LinkedHashSet<>();

        List<Map<String, Object>> list = reader.readAll();
        List<List<?>> rows = CollUtil.newArrayList();
        for (Map<String, Object>  map:list){


            String yex=  JSONObject.toJSONString(map);

            JSONObject jsonObject = JSONObject.parseObject(yex);

            bianma.add(jsonObject.getLong("大类编码"));
            bianma1.add(jsonObject.getLong("大类编码"));

            bianma.add(jsonObject.getLong("中类编码"));
            bianma2.add(jsonObject.getLong("中类编码"));

            bianma.add(jsonObject.getLong("小类编码"));
            bianma3.add(jsonObject.getLong("小类编码"));

            bianma.add(jsonObject.getLong("子类编码"));
            bianma4.add(jsonObject.getLong("子类编码"));

            //mc.add(jsonObject.getString("大类名称").toString().trim());

            //mc.add(jsonObject.getString("中类名称").toString().trim());

           // mc.add(jsonObject.getString("小类名称").toString().trim());

           // mc.add(jsonObject.getString("子类名称").toString().trim());

            //mc1.add(jsonObject.getString("大类名称").toString().trim());

            //mc2.add(jsonObject.getString("中类名称").toString().trim());

            //mc3.add(jsonObject.getString("小类名称").toString().trim());
            //mc4.add(jsonObject.getString("子类名称").toString().trim());


            StringBuffer sb1=new StringBuffer();
            sb1.append("INSERT INTO `cowell_cart`.`category_new_temp`(`id`, `category_name`, `parent_id`, `sort_value`, `status`, `gmt_create`, `gmt_update`, `create_by`, `update_by`, `version`, `extend`, `is_delete`)" +
                    " VALUES ("+jsonObject.getLong("大类编码")+", '"+jsonObject.getString("大类名称").toString().trim()+"', 0, "+jsonObject.getLong("大类编码")+", 0, '2023-10-23 06:33:03', '2023-10-23 06:33:11', '王代军', '王代军', 0, NULL, 0);");
            mc1.add(sb1.toString());
            StringBuffer sb2=new StringBuffer();
            sb2.append("INSERT INTO `cowell_cart`.`category_new_temp`(`id`, `category_name`, `parent_id`, `sort_value`, `status`, `gmt_create`, `gmt_update`, `create_by`, `update_by`, `version`, `extend`, `is_delete`)" +
                    " VALUES ("+jsonObject.getLong("中类编码")+", '"+jsonObject.getString("中类名称").toString().trim()+"', "+jsonObject.getLong("大类编码")+", "+jsonObject.getLong("中类编码")+", 0, '2023-10-23 06:33:03', '2023-10-23 06:33:11', '王代军', '王代军', 0, NULL, 0);");
            mc2.add(sb2.toString());
            StringBuffer sb3=new StringBuffer();
            sb3.append("INSERT INTO `cowell_cart`.`category_new_temp`(`id`, `category_name`, `parent_id`, `sort_value`, `status`, `gmt_create`, `gmt_update`, `create_by`, `update_by`, `version`, `extend`, `is_delete`)" +
                    " VALUES ("+jsonObject.getLong("小类编码")+", '"+jsonObject.getString("小类名称").toString().trim()+"', "+jsonObject.getLong("中类编码")+", "+jsonObject.getLong("小类编码")+", 0, '2023-10-23 06:33:03', '2023-10-23 06:33:11', '王代军', '王代军', 0, NULL, 0);");
            mc3.add(sb3.toString());
            StringBuffer sb4=new StringBuffer();
            sb4.append("INSERT INTO `cowell_cart`.`category_new_temp`(`id`, `category_name`, `parent_id`, `sort_value`, `status`, `gmt_create`, `gmt_update`, `create_by`, `update_by`, `version`, `extend`, `is_delete`)" +
                    " VALUES ("+jsonObject.getLong("子类编码")+", '"+jsonObject.getString("子类名称").toString().trim()+"', "+jsonObject.getLong("小类编码")+", "+jsonObject.getLong("子类编码")+", 0, '2023-10-23 06:33:03', '2023-10-23 06:33:11', '王代军', '王代军', 0, NULL, 0);");
            mc4.add(sb4.toString());



        }
      /*  System.out.println(mc.size());
        System.out.println(bianma.size());

        //System.out.println(mc1.size());
        System.out.println(mc2.size());
        //System.out.println(mc3.size());
        //System.out.println(mc4.size());
        //System.out.println(bianma1.size());

        System.out.println(bianma2.size());
        //System.out.println(bianma3.size());
        //System.out.println(bianma4.size());

       */
        mc.addAll(mc1);
        mc.addAll(mc2);
        mc.addAll(mc3);
        mc.addAll(mc4);
        //System.out.println(mc1);
        //System.out.println(mc2);
        //System.out.println(mc3);
        //System.out.println(mc4);
        mc.stream().forEach(v->{
            System.out.println(v);
        });

        //System.out.println();

    }
    @Before()  //这个方法在每个方法执行之前都会执行一遍
    public void setup() {
       // path = Class.class.getClass().getResource("/").getPath();
    }

    @Test
    public void getHello() throws Exception {
        System.out.println("TTTTTTTTTTTTTTTTTTTTTTTTT");
         short a= Short.valueOf("0").shortValue();
        System.out.println(a==0);
    }



    @Test
    @Ignore
    public void read2() {
        ExcelReader reader = ExcelUtil.getReader(path + "111.xlsx");
        List<Map<String, Object>> list = reader.readAll();
        List<List<?>> rows = CollUtil.newArrayList();
        for (Map<String, Object>  map:list){


          String yex=  JSONObject.toJSONString(map);

            List<Object> row1 = CollUtil.newArrayList(yex);

            System.out.println(yex);
            rows.add(ObjectUtil.clone(row1));

        }


        /*String filePath = path+"bigWriteTest2.xlsx";
        FileUtil.del(filePath);
        // 通过工具类创建writer
        BigExcelWriter writer = ExcelUtil.getBigWriter(filePath);

//		// 跳过当前行，即第一行，非必须，在此演示用
//		writer.passCurrentRow();
//		// 合并单元格后的标题行，使用默认标题样式
//		writer.merge(row1.size() - 1, "大数据测试标题");
        // 一次性写出内容，使用默认样式
        writer.write(rows);
//		writer.autoSizeColumn(0, true);
        // 关闭writer，释放内存
        writer.close();*/

    }



    @Test
    public void testHutoolExport() throws Exception {
        System.out.println("testHutoolExport");

    }
    @Test
    @Ignore
    public void writeTest2() {
        List<String> row = CollUtil.newArrayList("序号", "分类名称", "下班时间", "加班时长", "餐补", "车补次数", "车补", "总计");
        BigExcelWriter overtimeWriter = ExcelUtil.getBigWriter(path+"111.xlsx");
        overtimeWriter.write(row);
        overtimeWriter.close();
    }

    @Test
    @Ignore
    public void writeTest() {
        List<?> row1 = CollUtil.newArrayList("aaaaa", "bb", "cc", "dd", DateUtil.date(), 3.22676575765);
        List<?> row2 = CollUtil.newArrayList("aa1", "bb1", "cc1", "dd1", DateUtil.date(), 250.7676);
        List<?> row3 = CollUtil.newArrayList("aa2", "bb2", "cc2", "dd2", DateUtil.date(), 0.111);
        List<?> row4 = CollUtil.newArrayList("aa3", "bb3", "cc3", "dd3", DateUtil.date(), 35);
        List<?> row5 = CollUtil.newArrayList("aa4", "bb4", "cc4", "dd4", DateUtil.date(), 28.00);

        List<List<?>> rows = CollUtil.newArrayList(row1, row2, row3, row4, row5);
        for(int i=0; i < 400000; i++) {
            //超大列表写出测试
            rows.add(ObjectUtil.clone(row1));
        }

        String filePath = path+"bigWriteTest.xlsx";
        FileUtil.del(filePath);
        // 通过工具类创建writer
        BigExcelWriter writer = ExcelUtil.getBigWriter(filePath);

//		// 跳过当前行，即第一行，非必须，在此演示用
//		writer.passCurrentRow();
//		// 合并单元格后的标题行，使用默认标题样式
//		writer.merge(row1.size() - 1, "大数据测试标题");
        // 一次性写出内容，使用默认样式
        writer.write(rows);
//		writer.autoSizeColumn(0, true);
        // 关闭writer，释放内存
        writer.close();
    }

    @Test
    @Ignore
    public void mergeTest() {
        List<?> row1 = CollUtil.newArrayList("aa", "bb", "cc", "dd", DateUtil.date(), 3.22676575765);
        List<?> row2 = CollUtil.newArrayList("aa1", "bb1", "cc1", "dd1", DateUtil.date(), 250.7676);
        List<?> row3 = CollUtil.newArrayList("aa2", "bb2", "cc2", "dd2", DateUtil.date(), 0.111);
        List<?> row4 = CollUtil.newArrayList("aa3", "bb3", "cc3", "dd3", DateUtil.date(), 35);
        List<?> row5 = CollUtil.newArrayList("aa4", "bb4", "cc4", "dd4", DateUtil.date(), 28.00);

        List<List<?>> rows = CollUtil.newArrayList(row1, row2, row3, row4, row5);

        // 通过工具类创建writer
        BigExcelWriter writer = ExcelUtil.getBigWriter(path+"mergeTest.xlsx");
        CellStyle style = writer.getStyleSet().getHeadCellStyle();
        StyleUtil.setColor(style, IndexedColors.RED, FillPatternType.SOLID_FOREGROUND);

        // 跳过当前行，即第一行，非必须，在此演示用
        writer.passCurrentRow();
        // 合并单元格后的标题行，使用默认标题样式
        writer.merge(row1.size() - 1, "测试标题");
        // 一次性写出内容，使用默认样式
        writer.write(rows);

        // 合并单元格后的标题行，使用默认标题样式
       // writer.merge(7, 10, 4, 10, "测试Merge", true);

        // 关闭writer，释放内存
        writer.close();
    }

    @Test
    @Ignore
    public void writeMapTest() {
        Map<String, Object> row1 = new LinkedHashMap<>();
        row1.put("姓名", "张三");
        row1.put("年龄", 23);
        row1.put("成绩", 88.32);
        row1.put("是否合格", true);
        row1.put("考试日期", DateUtil.date());

        Map<String, Object> row2 = new LinkedHashMap<>();
        row2.put("姓名", "李四");
        row2.put("年龄", 33);
        row2.put("成绩", 59.50);
        row2.put("是否合格", false);
        row2.put("考试日期", DateUtil.date());

        ArrayList<Map<String, Object>> rows = CollUtil.newArrayList(row1, row2);

        // 通过工具类创建writer
        String path1 = path+"bigWriteMapTest.xlsx";
        FileUtil.del(path1);
        BigExcelWriter writer = ExcelUtil.getBigWriter(path1);

        //设置内容字体
        Font font = writer.createFont();
        font.setBold(true);
        font.setColor(Font.COLOR_RED);
        font.setItalic(true);
        writer.getStyleSet().setFont(font, true);

        // 合并单元格后的标题行，使用默认标题样式
        writer.merge(row1.size() - 1, "一班成绩单");
        // 一次性写出内容，使用默认样式
        writer.write(rows);
        // 关闭writer，释放内存
        writer.close();
    }

    @Test
    @Ignore
    public void writeMapTest2() {
        Map<String, Object> row1 = MapUtil.newHashMap(true);
        row1.put("姓名", "张三");
        row1.put("年龄", 23);
        row1.put("成绩", 88.32);
        row1.put("是否合格", true);
        row1.put("考试日期", DateUtil.date());

        Map<String, Object> row2 = MapUtil.newHashMap(true);
        row2.put("姓名", "张三1");
        row2.put("年龄", 231);
        row2.put("成绩", 88.312);
        row2.put("是否合格", true);
        row2.put("考试日期", DateUtil.date());

        // 通过工具类创建writer
        String path1 = path+"bigWriteMapTest2.xlsx";
        FileUtil.del(path1);
        BigExcelWriter writer = ExcelUtil.getBigWriter(path1);

        // 一次性写出内容，使用默认样式
        writer.writeRow(row1, true);
        // 关闭writer，释放内存
        writer.close();
    }

    @Test
    @Ignore
    public void writeBeanTest() {
        com.tomas.mybaties3.model.TestBean bean1 = new com.tomas.mybaties3.model.TestBean();
        bean1.setName("张三");
        bean1.setAge(22);
        bean1.setPass(true);
        bean1.setScore(66.30);
        bean1.setExamDate(DateUtil.date());

        com.tomas.mybaties3.model.TestBean bean2 = new com.tomas.mybaties3.model.TestBean();
        bean2.setName("李四");
        bean2.setAge(28);
        bean2.setPass(false);
        bean2.setScore(38.50);
        bean2.setExamDate(DateUtil.date());

        List<com.tomas.mybaties3.model.TestBean> rows = CollUtil.newArrayList(bean1, bean2);
        // 通过工具类创建writer
        String file = path+"bigWriteBeanTest1.xlsx";
        FileUtil.del(file);
        BigExcelWriter writer = ExcelUtil.getBigWriter(file);
        //自定义标题
      /*  writer.addHeaderAlias("name", "姓名");
        writer.addHeaderAlias("age", "年龄");
        writer.addHeaderAlias("score", "分数");
        writer.addHeaderAlias("isPass", "是否通过");
        writer.addHeaderAlias("examDate", "考试时间");
        writer.setOnlyAlias(true);*/

        /**
         * 导出dvc建议商品列表
         */
        LinkedHashMap<String,String> DVC_GOODS_LIST = new LinkedHashMap<String,String>(){
            private static final long serialVersionUID = -6514836291678556529L;
            {
                put("name","姓名");
                put("age","年龄");
                put("score","分数");
                put("isPass","是否通过");
                put("examDate","考试时间");
            }
        };

       // writer.merge(DVC_GOODS_LIST.size() - 1, "明细");
        //Row row = writer.getOrCreateRow(0);
        //row.setHeight((short) 2000);
       /* CellStyle cellStyle = writer.getOrCreateCellStyle(0,0);
        StyleUtil.setColor(cellStyle, (short) 13, FillPatternType.SOLID_FOREGROUND);
        StyleUtil.setAlign(cellStyle, HorizontalAlignment.LEFT, VerticalAlignment.CENTER);*/

        setCellStyle(writer,1,1,false);

        writer.setHeaderAlias(DVC_GOODS_LIST);
        writer.setOnlyAlias(true);
       // writer.write(resultList, true);
        // 合并单元格后的标题行，使用默认标题样式
        //writer.merge(4, "一班成绩单");
        // 一次性写出内容，使用默认样式
        writer.write(rows, true);
        // 关闭writer，释放内存
        writer.close();
    }

    @Test
    @Ignore
    public void writeCellValueTest() {
        String path1 = path+"cellValueTest.xlsx";
        FileUtil.del(path1);
        BigExcelWriter writer = new BigExcelWriter(path1);
        writer.writeCellValue(3, 5, "aaa");
        writer.close();
    }

    @Test
    @Ignore
    public void closeTest() {
        final Map<String, ?> map1 = MapUtil.of("id", "123456");
        final Map<String, ?> map2 = MapUtil.of("id", "123457");
        final List<?> data = Arrays.asList(map1, map2);
        final String destFilePath = path+"closeTest.xlsx";//略
        FileUtil.del(destFilePath);
        try (ExcelWriter writer = ExcelUtil.getBigWriter(destFilePath)) {
            writer.write(data).flush();
        }
    }

    @Test
    @Ignore
    public void issue1210() {
        // 通过工具类创建writer
        String path1 = path+"issue1210.xlsx";
        FileUtil.del(path1);
        BigExcelWriter writer = ExcelUtil.getBigWriter(path1);
        writer.addHeaderAlias("id", "SN");
        writer.addHeaderAlias("userName", "User Name");
        writer.setOnlyAlias(true);

        List<Map<String, Object>> list = new ArrayList<>();
        list.add(new HashMap<String, Object>() {
            private static final long serialVersionUID = 1L;

            {
                put("id", 1);
                put("ids", 1);
                put("userName", "aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa");
            }});

        list.add(new HashMap<String, Object>() {
            private static final long serialVersionUID = 1L;

            {
                put("id", 2);
                put("ids", 1);
                put("userName", "bbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbbb");
            }});
        writer.write(list, true);
        //writer.autoSizeColumnAll();
        writer.close();
    }


    @Test
    @Ignore
    public void testExecutor() {
        ArrayList<String> list = new ArrayList<>();
        for (int i = 0; i < 10000; i++) {
            list.add("第" + i + "个");
        }
        long start = System.currentTimeMillis();
        int nThreads = 5;
        int size = list.size();
        ExecutorService executorService = Executors.newFixedThreadPool(nThreads);
        ArrayList<Future<Integer>> futures = new ArrayList<>(nThreads);
        for (int i = 0; i < nThreads; i++) {
            List<String> list1 = list.subList(size / nThreads * i, size / nThreads * (i + 1));
            int finalI = i + 1;
            Callable<Integer> task1 = () -> {
                for (String str : list1) {
                   // log.info("线程{}正在处理{}", finalI, str);
                }
                return 1;
            };
            futures.add(executorService.submit(task1));
        }
        if (futures != null && !futures.isEmpty()){
            for (Future<Integer> future : futures) {
                try {
                    log.info("done={}",future.get());
                    //System.out.println("多线程处理后"+result);
                } catch (InterruptedException | ExecutionException e) {
                    log.error("多线程处理异常",e);
                }
            }
        }
        // 关闭线程池
        executorService.shutdown();
        log.info("线程任务执行结束");
        log.info("执行任务消耗了 ：" + (System.currentTimeMillis() - start) + "毫秒");
    }





    /**
     * 自定义设置单元格样式
     * @param writer 	hutool-Excel写入器
     * @param x 		x_坐标
     * @param y 		y_坐标
     * @param isTrue 	true ->设置为绿色;  false ->设置为红色;
     */
    private void setCellStyle(ExcelWriter writer, int x, int y, Boolean isTrue) {
        CellStyle cellStyle = writer.createCellStyle(x, y);
        // 顶边栏
        cellStyle.setBorderTop(BorderStyle.THIN);
        cellStyle.setTopBorderColor(IndexedColors.BLACK.getIndex());
        // 右边栏
        cellStyle.setBorderRight(BorderStyle.THIN);
        cellStyle.setRightBorderColor(IndexedColors.BLACK.getIndex());
        // 底边栏
        cellStyle.setBorderBottom(BorderStyle.THIN);
        cellStyle.setBottomBorderColor(IndexedColors.BLACK.getIndex());
        // 左边栏
        cellStyle.setBorderLeft(BorderStyle.THIN);
        cellStyle.setLeftBorderColor(IndexedColors.BLACK.getIndex());
        // 填充前景色(两个一起使用)
        cellStyle.setFillForegroundColor(isTrue ? IndexedColors.BRIGHT_GREEN1.getIndex() : IndexedColors.PINK1.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
    }


}
