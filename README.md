# Spring Boot基础教程（2.x版本）

本项目内容为[《Spring Boot基础教程》](http://blog.didispace.com/Spring-Boot%E5%9F%BA%E7%A1%80%E6%95%99%E7%A8%8B/)的程序样例。

**专题目标**：打造全网内容最全，比收费教程更好的Spring Boot免费教程！

**如何支持**：
1. 关注我的公众号”**程序猿DD**“
2. 点个`Star`并`Follow`我
3. 把该仓库分享给更多的朋友

如果您对文字类教程不感冒或者想要通过综合案例学习Spring，那么给您推荐这个我觉得目前内容与价格最良心的视频课程：["玩转Spring全家桶"](https://time.geekbang.org/course/intro/100023501?code=d1se%2F7ugeBEyuU%2FIYp1ynfSZa6ulbGhhDK%2Fkpn3-lFc%3D)

> **关注公众号：“程序猿DD”**，领取我整理的免费学习资料。<br>

## 特别赞助商

<table>
      <tbody>
        <tr>
          <td align="center" valign="middle">
            <a href="https://coding.net/?utm_source=zhaiyongchao&utm_medium=banner&utm_campaign=march2019" target="_blank">
              <img width="300" src="https://github.com/dyc87112/SpringBoot-Learning/blob/master/sponsor/git-springboot-sponsor-1-500X166.jpg?raw=true">
            </a>
          </td>
          <td align="center" valign="middle">
            <a href="https://www.aliyun.com/1111/2019/group-buying-share?ptCode=1946814D2840EE4FEC0971C48B9B5785647C88CF896EF535&userCode=wxfqkr0o&share_source=copy_link" target="_blank">
              <img width="300" src="http://blog.didispace.com/assets/adv/aliyun_360_120.jpg">
            </a>
          </td> 
          <td align="center" valign="middle">
            <a href="http://gk.link/a/103EK" target="_blank">
              <img width="300" src="https://github.com/dyc87112/SpringBoot-Learning/blob/master/sponsor/git-springboot-sponsor-4.jpg?raw=true">
            </a>
          </td> 
        </tr>
        <tr>
          <td align="center" valign="middle">
            <a href="https://cloud.tencent.com/redirect.php?redirect=1027&cps_key=f6a8af1297bfac40b9d10ffa1270029a&from=console" target="_blank">
              <img width="300" src="https://github.com/dyc87112/SpringBoot-Learning/blob/master/sponsor/git-springboot-sponsor-5.jpg?raw=true">
            </a>
          </td> 
        </tr>
      </tbody>
</table>

> 如果您也想赞助支持并出现在上表中的话，可以通过邮件联系我：`<EMAIL>`

## 教程目录（2.x版本）

### 基础知识

- [Spring Boot 2.x基础教程：版本关系](http://blog.didispace.com/spring-cloud-alibaba-version/)
- [Spring Boot 2.x基础教程：快速入门](http://blog.didispace.com/spring-boot-learning-21-1-1/)
- [Spring Boot 2.x基础教程：工程结构推荐](http://blog.didispace.com/spring-boot-learning-21-1-2/)
- [Spring Boot 2.x基础教程：配置文件详解](http://blog.didispace.com/spring-boot-learning-21-1-3/)

### Web开发

- [Spring Boot 2.x基础教程：构建RESTful API与单元测试](http://blog.didispace.com/spring-boot-learning-21-2-1/)
- [Spring Boot 2.x基础教程：使用Swagger2构建强大的API文档](http://blog.didispace.com/spring-boot-learning-21-2-2/)
- [Spring Boot 2.x基础教程：JSR-303实现请求参数校验](http://blog.didispace.com/spring-boot-learning-21-2-3/)
- [Spring Boot 2.x基础教程：Swagger接口分类与各元素排序问题详解](http://blog.didispace.com/spring-boot-learning-21-2-4/)
- [Spring Boot 2.x基础教程：Swagger静态文档的生成](http://blog.didispace.com/spring-boot-learning-21-2-5/)

连载中...Star关注支持以下，随时获得更新信息！

### 1.x到2.x

- [Spring Boot 2.0 正式发布，升还是不升呢？](http://blog.didispace.com/spring-boot-2-release/)
- [Spring Boot 2.0 新特性和发展方向](http://blog.didispace.com/Spring-Boot-2-0-%E6%96%B0%E7%89%B9%E6%80%A7%E5%92%8C%E5%8F%91%E5%B1%95%E6%96%B9%E5%90%91/)
- [Spring Boot 2.0 与 Java 9](http://blog.didispace.com/Spring-Boot-2.0%E4%B8%8EJava-9/)
- [Spring Boot 2.0 新特性（一）：配置绑定 2.0 全解析](http://blog.didispace.com/Spring-Boot-2-0-feature-1-relaxed-binding-2/)
- [Spring Boot 2.0 新特性（二）：新增事件ApplicationStartedEvent](http://blog.didispace.com/Spring-Boot-2-0-feature-2-ApplicationStartedEvent/)

## 推荐内容

- [我的博客](http://blog.didispace.com)：分享平时学习和实践过的技术内容
- [知识星球](https://t.xiaomiquan.com/zfEiY3v)：聊聊技术人的斜杠生活
- [GitHub](https://github.com/dyc87112/SpringBoot-Learning)：Star支持一下呗
- [Gitee](https://gitee.com/didispace/SpringBoot-Learning)：Star支持一下呗
- [Spring问答社区](http://www.spring4all.com/)：如果您有什么问题，可以去这里发帖
- [Spring Boot基础教程](http://blog.didispace.com/Spring-Boot%E5%9F%BA%E7%A1%80%E6%95%99%E7%A8%8B/)：全网Star最多的免费Spring Boot基础教程
- [Spring Cloud基础教程](http://blog.didispace.com/Spring-Cloud%E5%9F%BA%E7%A1%80%E6%95%99%E7%A8%8B/)：全网最早最全的免费Spring Cloud基础教程

## 我的公众号

<img src="http://blog.didispace.com/css/images/weixin.jpg" style="width:150px;height:150px;" />

## 我出版的书

![输入图片说明](https://git.oschina.net/uploads/images/2017/0416/233656_dd3bce94_437188.png "在这里输入图片标题")
