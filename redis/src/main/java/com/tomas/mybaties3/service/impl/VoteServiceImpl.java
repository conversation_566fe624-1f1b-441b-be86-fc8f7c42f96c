package com.tomas.mybaties3.service.impl;

import com.tomas.mybaties3.config.RedisCacheAnnotation;
import com.tomas.mybaties3.service.VoteService;
import com.tomas.mybaties3.utils.vo.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * VoteServiceImpl 描述
 *
 * <AUTHOR>
 * @create 2021/1/21
 **/
@Slf4j
@Service("voteService")
public class VoteServiceImpl implements VoteService {

    public static final String POS_APP_KEY = "POS_YUN_";

    public final static String ITEM_SEARCH_COMMON_SPULIST=POS_APP_KEY+"ITEM_SEARCH_COMMON_SPULIST";

    @Override
    @RedisCacheAnnotation(key = ITEM_SEARCH_COMMON_SPULIST, expiredTime = 86400 ,exclude = "resource")
    public Object getObjectFromRedis(String key, Long id ,Resource resource) {
        log.info("进入方法了");
        return "tomas测试";
    }
}
