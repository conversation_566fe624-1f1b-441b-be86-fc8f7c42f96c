package com.tomas.mybaties3.config;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @desc  Redis缓存注解
 * <AUTHOR>
 * @data 2022-11-11 17:26:11
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
public @interface RedisCacheAnnotation {

    /**
     * 缓存key
     */
    String key() default "";

    /**
     *  过期时间，默认5分钟 300s
     * @return
     */
    int expiredTime() default 300;

    /**
     * 排查参数名称(方法级别一级参数名称)
     * @return
     */
    String[] exclude() default {};

}

