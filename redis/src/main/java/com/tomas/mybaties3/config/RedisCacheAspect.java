package com.tomas.mybaties3.config;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.aspectj.lang.reflect.MethodSignature;
import org.redisson.api.RBucket;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 核心操作 Redis 缓存注解的 切片方法，拦截执行，确认是否要执行。
 * @Configuration 自动
 *  <AUTHOR>
 *  @data 2022-11-11 17:26:11
 */
@Aspect
@Component
public class RedisCacheAspect {

    private static  final Logger log = LoggerFactory.getLogger(RedisCacheAspect.class);

    public static final String POS_APP_KEY = "POS_YUN_";

    @Autowired
    private RedissonClient redissonClient;

    @Pointcut("@annotation(com.tomas.mybaties3.config.RedisCacheAnnotation)")
    public void annotationPointcut() { }

    @Around("annotationPointcut()")
    public Object doAround(ProceedingJoinPoint pjp) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) pjp.getSignature();
        RedisCacheAnnotation redisCacheAnnotation = methodSignature.getMethod().getDeclaredAnnotation(RedisCacheAnnotation.class);
        String key = redisCacheAnnotation.key();
        if (StringUtils.isNotBlank(key) && key.contains(POS_APP_KEY)){
            RBucket<Object> cacheBucket=null;
            String redisKey=null;
            try {
                String paramKey = buildParamKey(pjp, redisCacheAnnotation);
                //如果方法没有入参,暂时不缓存
                if(StringUtils.isNotBlank(paramKey)){
                    redisKey = key + DigestUtils.md5Hex(paramKey);
                    cacheBucket = redissonClient.getBucket(redisKey);
                    Object cacheObj = cacheBucket.get();
                    if (Objects.nonNull(cacheObj)) {
                        log.info("命中缓存,缓存key={}", redisKey);
                        return cacheObj;
                    }
                }
            }catch (Exception e) {
                log.warn("RedisCacheAspect切面缓存异常",e);
            }
            Object obj = pjp.proceed();
            addToCache(cacheBucket,redisKey,obj,redisCacheAnnotation.expiredTime());
            return obj;
        }else {
            return  pjp.proceed();
        }
    }

    private String buildParamKey(ProceedingJoinPoint pjp ,RedisCacheAnnotation redisCacheAnnotation){
        Object[] args = pjp.getArgs();
        String[] names = ((CodeSignature) pjp.getSignature()).getParameterNames();
        HashSet<String> excludeNames = new HashSet<>(Arrays.asList(redisCacheAnnotation.exclude()));
        StringBuffer params=new StringBuffer("");
        if(Objects.nonNull(args)){
            for (int i = 0; i < args.length; i++) {
                if(Objects.nonNull(args[i]) && Objects.nonNull(names[i]) && (!excludeNames.isEmpty()&& !excludeNames.contains(names[i]))  ){
                    //log.info( names[i]+ToStringBuilder.reflectionToString(args[i], ToStringStyle.JSON_STYLE));
                    params.append( names[i]+ToStringBuilder.reflectionToString(args[i], ToStringStyle.JSON_STYLE));
                }

            }
        }
        return params.toString();
    }

    private void addToCache(RBucket<Object> cacheBucket, String redisKey, Object obj, int expiredTime){
        try {
            if(Objects.nonNull(cacheBucket) && Objects.nonNull(obj)){
                cacheBucket.set(obj, expiredTime, TimeUnit.SECONDS);
                log.info("执行方法,添加缓存, 缓存key={}", redisKey);
            }
        }catch (Exception e){
            log.warn("设置缓存异常",e);
        }
    }



}
