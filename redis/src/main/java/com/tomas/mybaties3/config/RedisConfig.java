package com.tomas.mybaties3.config;

import com.fasterxml.jackson.annotation.JsonAutoDetect;
import com.fasterxml.jackson.annotation.PropertyAccessor;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.Redisson;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.redisson.config.ClusterServersConfig;
import org.redisson.config.Config;
import org.redisson.config.ReadMode;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.data.redis.RedisProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.Jackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;

@Slf4j
@Configuration
@ConditionalOnClass(RedisOperations.class)
@EnableConfigurationProperties(RedisProperties.class)
//@Import({ LettuceConnectionConfiguration.class, JedisConnectionConfiguration.class })
public class RedisConfig {

/*    @Bean
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(
            RedisConnectionFactory redisConnectionFactory) throws UnknownHostException {
        RedisTemplate<String, Object> template = new RedisTemplate<>();
        template.setConnectionFactory(redisConnectionFactory);
        return template;
    }*/

    @Bean
    @ConditionalOnMissingBean(name = "redisTemplate")
    public RedisTemplate<String, Object> redisTemplate(RedisConnectionFactory factory) {
        RedisTemplate<String, Object> template = new RedisTemplate<String, Object>();
        template.setConnectionFactory(factory);
        Jackson2JsonRedisSerializer jackson2JsonRedisSerializer = new Jackson2JsonRedisSerializer(Object.class);
        ObjectMapper om = new ObjectMapper();
        om.setVisibility(PropertyAccessor.ALL, JsonAutoDetect.Visibility.ANY);
        om.enableDefaultTyping(ObjectMapper.DefaultTyping.NON_FINAL);
        jackson2JsonRedisSerializer.setObjectMapper(om);
        StringRedisSerializer stringRedisSerializer = new StringRedisSerializer();
        // key采用String的序列化方式
        template.setKeySerializer(stringRedisSerializer);
        // hash的key也采用String的序列化方式
        template.setHashKeySerializer(stringRedisSerializer);
        // value序列化方式采用jackson
        template.setValueSerializer(jackson2JsonRedisSerializer);
        // hash的value序列化方式采用jackson
        template.setHashValueSerializer(jackson2JsonRedisSerializer);
        template.afterPropertiesSet();
        return template;
    }


    @Bean
    RedissonClient redisson() throws Exception {

      /*  Config config = new Config();
       // config.useSingleServer().setAddress(new StringBuilder()
         //       .append("redis://common_microservice_redis-01_test.cowelltech.com").append(":").append(6379).toString());
        ClusterServersConfig clusterConfig = config.useClusterServers()
                .setScanInterval(Integer.valueOf(2000))
                .setKeepAlive(true);
        clusterConfig.setPingConnectionInterval(Integer.valueOf(3000));
        clusterConfig.setReadMode(ReadMode.MASTER);
       *//* if(StringUtils.isNotBlank("123456")){
            clusterConfig.setPassword("123456");
        }*//*
        clusterConfig.addNodeAddress("redis://common_microservice_redis-01_test.cowelltech.com:6379");
        config.setCodec(new JsonJacksonCodec());
        log.info("cluster redisson readMode=======");*/
      /*  Config config = new Config();
        config.useSingleServer()
                .setAddress("redis://127.0.0.1:6379")
                .setPassword("123456")
                .setDatabase(0);
        return Redisson.create(config);*/

        Config config = new Config();
        // 新增配置
        config.setCodec(new JsonJacksonCodec());
        ClusterServersConfig clusterConfig = config.useClusterServers()
                .setScanInterval(20000).setKeepAlive(true)
                .setMasterConnectionPoolSize(20).setMasterConnectionMinimumIdleSize(10)
                .setSlaveConnectionPoolSize(20).setSlaveConnectionMinimumIdleSize(10)
                .setReadMode(ReadMode.MASTER);
        clusterConfig.setPingConnectionInterval(3000);
        clusterConfig.addNodeAddress("redis://common_microservice_redis-01_test.cowelltech.com:6379");
        log.info("cluster redisson readMode=======");
        return Redisson.create(config);
    }
}
