package com.tomas.mybaties3.rest;

import com.alibaba.fastjson.JSON;
import com.tomas.mybaties3.config.RedisUtil;
import com.tomas.mybaties3.service.VoteService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@RestController
@RequestMapping("/redis")
public class HelloController {


    @Autowired
    RedisUtil redisUtil;

    @Autowired
    RedissonClient redissonClient;


    @Autowired
    VoteService voteService;
    AtomicInteger atomicInteger=new AtomicInteger(0);
    @RequestMapping("user/maxId")
    public Object index(Integer maxId) {
        Map<String,Object> setBatch=new HashMap(maxId);
        /*for (int i = 0; i < maxId; i++){
            if (i%1000==0){
                setBatch.put(String.valueOf("zk"+i),System.nanoTime());
                redisUtil.setBatch(setBatch);
                setBatch.clear();
            }else {
                setBatch.put(String.valueOf("zk"+i),System.nanoTime());
            }
        }*/
        //edissonClient
        //MARKETING_STOP_PROMOTION_GOODS_NEW:
        return "操作成功";
    }

    @RequestMapping("user/getUid")
    public Object jdbc(String key) {
        RBatch batch = redissonClient.createBatch();
        for (int i = 0; i <2 ; i++) {
            RBucketAsync<Object> bucket = batch.getBucket(key+"_"+i);
            bucket.setAsync("test"+i,1, TimeUnit.HOURS);
        }
        BatchResult<?> execute = batch.execute();
        System.out.println(JSON.toJSONString(execute.getResponses()));
        return JSON.toJSONString(execute.getResponses());
        //return redisUtil.get("zk" + uid);
    }


    @RequestMapping("user/testBatch")
    public Object testBatch(String key) throws ExecutionException, InterruptedException {

        RBatch batch = redissonClient.createBatch();
        for (int i = 0; i <2 ; i++) {
           batch.getBucket(key+"_"+i).getAsync();
           // bucket.getAsync();
            //System.out.println(async.get());
        }
        BatchResult<?> execute = batch.execute();
        //.getAsync()
        System.out.println(JSON.toJSONString(execute.getResponses()));
        return "ok";
    }

    @RequestMapping("atomicLong")
    public Object atomicLong(String key, Long id) throws ExecutionException, InterruptedException {

        log.info("key={} id={}",key ,id);
        RAtomicLong atomicLong = redissonClient.getAtomicLong("SCIB_TOMAS_" + id);
        long l = atomicLong.incrementAndGet();
        log.info(" l={}",l);
        if (l>1){
            log.info("key={} id={} l={}",key ,id,l);
        }
        atomicLong.expire(30,TimeUnit.SECONDS);
        return l;
    }





}
