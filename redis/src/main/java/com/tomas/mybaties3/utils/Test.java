package com.tomas.mybaties3.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.tomas.mybaties3.utils.vo.OrgTreeDTO;

import java.util.ArrayList;
import java.util.List;

/**
 * Test 描述
 *
 * <AUTHOR>
 * @create 2021/7/28
 **/
public class Test {

        public static void main(String[] args) {
            String jsonString = "[{\"id\": 3, \"name\": \"高济健康\", \"shortName\": \"高济健康\", \"orgPath\": \"1/3\", \"type\": 100, \"parentId\": 1, \"children\": [{\"id\": 59, \"name\": \"成都一部\", \"shortName\": \"成都一部\", \"orgPath\": \"1/3/16/59\", \"type\": 500, \"parentId\": 16, \"children\": [], \"outId\": 15262, \"sapcode\": \"3000\", \"joinType\": \"01\"}, {\"id\": 71381, \"name\": \"巴中心健大药房有限公司\", \"shortName\": \"巴中心健大药房有限公司\", \"orgPath\": \"1/3/16/71381\", \"type\": 500, \"parentId\": 16, \"children\": [], \"outId\": 633969, \"sapcode\": \"W003\", \"joinType\": \"02\"}, {\"id\": 72589, \"name\": \"四川龙一药品零售连锁有限公司\", \"shortName\": \"四川龙一药品零售连锁有限公司\", \"orgPath\": \"1/3/16/72589\", \"type\": 500, \"parentId\": 16, \"children\": [{\"id\": 73607, \"name\": \"龙一仓\", \"shortName\": \"龙一仓\", \"orgPath\": \"1/3/16/72589/72600/73607\", \"type\": 800, \"parentId\": 72600, \"children\": [], \"outId\": 3686663250760, \"sapcode\": \"3681\", \"joinType\": \"01\"}], \"outId\": 650760, \"sapcode\": \"3680\", \"joinType\": \"01\"}]}]";

            List<OrgTreeDTO> orgTreeList = JSON.parseObject(jsonString, new TypeReference<List<OrgTreeDTO>>() {});

            List<Long> outIdList = new ArrayList<>();
            outIdList.add(15264L);
            outIdList.add(650760L);
            List<OrgTreeDTO> result = new ArrayList<>();
            findMatchingNodes(orgTreeList, outIdList, result);

            // Print or process the result list
            for (OrgTreeDTO node : result) {
                System.out.println(node.getName());
            }
        }

        private static void findMatchingNodes(List<OrgTreeDTO> nodes, List<Long> outIdList, List<OrgTreeDTO> result) {
            //System.out.println(nodes);
            if (nodes == null || nodes.isEmpty()) {
                return;
            }

            for (OrgTreeDTO node : nodes) {
                //System.out.println(node);
                if (node.getType() != null && node.getType() == 500 && node.getOutId() != null && outIdList.contains(node.getOutId())) {
                    if (hasChildOfType800(node)) {
                        System.out.println("hasChildOfType800"+node.toString());
                        result.add(node);
                    }
                }

                if (node.getChildren() != null && !node.getChildren().isEmpty()) {
                    System.out.println("findMatchingNodes"+node.getChildren().toString());
                    findMatchingNodes(node.getChildren(), outIdList, result);
                }
            }
        }

        private static boolean hasChildOfType800(OrgTreeDTO node) {
            if (node.getChildren() == null || node.getChildren().isEmpty()) {
                return false;
            }

            for (OrgTreeDTO child : node.getChildren()) {
                System.out.println("child"+child.toString());
                if (child.getType() != null && child.getType() == 800 && child.getOutId() != null) {
                    return true;
                }
            }

            return false;
        }

}
