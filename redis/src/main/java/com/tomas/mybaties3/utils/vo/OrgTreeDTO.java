//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.tomas.mybaties3.utils.vo;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import javax.validation.constraints.Size;

public class OrgTreeDTO implements Serializable {
    private static final long serialVersionUID = 4009818114592029335L;
    private Long id;

    private String name;

    private String shortName;
    private String orgPath;
    private String orgNamePath;
    private Integer type;
    private Integer flag;
    private Long parentId;
    private Integer level;
    private Integer userBelongCount;
    private Boolean checkFlag;
    private List<OrgTreeDTO> children;
    private Long outId;
    private String guidkey;
    private String codeitemid;
    private String codeItemSimple;
    private String sapcode;
    private Integer syncMdm;
    private Long mdmUpdateTime;
    private Boolean pressFlag = false;

    /**
     * 是否动态，1是 0否
     */
    private Integer dynamicStatus;

    private Integer existStatus;

    /**
     * 市场地位
     */
    private String marPosition;

    /**
     * 运营管理区域
     */
    private String operarea;

    public OrgTreeDTO() {
    }

    public Long getOutId() {
        return this.outId;
    }

    public void setOutId(Long outId) {
        this.outId = outId;
    }

    public Long getId() {
        return this.id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getShortName() {
        return this.shortName;
    }

    public void setShortName(String shortName) {
        this.shortName = shortName == null ? null : shortName.trim();
    }

    public String getOrgPath() {
        return this.orgPath;
    }

    public void setOrgPath(String orgPath) {
        this.orgPath = orgPath == null ? null : orgPath.trim();
    }

    public String getOrgNamePath() {
        return this.orgNamePath;
    }

    public void setOrgNamePath(String orgNamePath) {
        this.orgNamePath = orgNamePath;
    }

    public Integer getType() {
        return this.type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public Long getParentId() {
        return this.parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Integer getLevel() {
        return this.level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    public Boolean getPressFlag() {
        return this.pressFlag;
    }

    public void setPressFlag(Boolean pressFlag) {
        this.pressFlag = pressFlag;
    }

    public Integer getUserBelongCount() {
        return this.userBelongCount;
    }

    public void setUserBelongCount(Integer userBelongCount) {
        this.userBelongCount = userBelongCount;
    }

    public Boolean getCheckFlag() {
        return this.checkFlag;
    }

    public void setCheckFlag(Boolean checkFlag) {
        this.checkFlag = checkFlag;
    }

    public List<OrgTreeDTO> getChildren() {
        return this.children;
    }

    public void setChildren(List<OrgTreeDTO> children) {
        this.children = children;
    }

    public String getCodeItemSimple() {
        return this.codeItemSimple;
    }

    public void setCodeItemSimple(String codeItemSimple) {
        this.codeItemSimple = codeItemSimple;
    }

    public static OrgTreeDTO toTree(OrgTreeDTO rootOrgTree, List<OrgTreeDTO> list) {
        if (rootOrgTree == null) {
            return null;
        } else {
            List<OrgTreeDTO> children = new ArrayList();
            Iterator var3 = list.iterator();

            while(var3.hasNext()) {
                OrgTreeDTO orgTree = (OrgTreeDTO)var3.next();
                if (orgTree.getParentId().equals(rootOrgTree.getId())) {
                    orgTree = toTree(orgTree, list);
                    orgTree.setPressFlag(true);
                    children.add(orgTree);
                }
            }

            rootOrgTree.setChildren(children);
            return rootOrgTree;
        }
    }

    public String toString() {
        return "OrgTreeDTO{id=" + this.id + ", name='" + this.name + '\'' + ", shortName='" + this.shortName + '\'' + ", orgPath='" + this.orgPath + '\'' + ", orgNamePath='" + this.orgNamePath + '\'' + ", type=" + this.type + ", parentId=" + this.parentId + ", level=" + this.level + ", userBelongCount=" + this.userBelongCount + ", checkFlag=" + this.checkFlag + ", children=" + this.children + ", pressFlag=" + this.pressFlag + '}';
    }

    public String getGuidkey() {
        return this.guidkey;
    }

    public void setGuidkey(String guidkey) {
        this.guidkey = guidkey;
    }

    public String getCodeitemid() {
        return this.codeitemid;
    }

    public void setCodeitemid(String codeitemid) {
        this.codeitemid = codeitemid;
    }

    public String getSapcode() {
        return this.sapcode;
    }

    public void setSapcode(String sapcode) {
        this.sapcode = sapcode;
    }

    public Integer getSyncMdm() {
        return this.syncMdm;
    }

    public void setSyncMdm(Integer syncMdm) {
        this.syncMdm = syncMdm;
    }

    public Long getMdmUpdateTime() {
        return this.mdmUpdateTime;
    }

    public void setMdmUpdateTime(Long mdmUpdateTime) {
        this.mdmUpdateTime = mdmUpdateTime;
    }

    public Integer getFlag() {
        return this.flag;
    }

    public void setFlag(Integer flag) {
        this.flag = flag;
    }

    public Integer getDynamicStatus() {
        return dynamicStatus;
    }

    public void setDynamicStatus(Integer dynamicStatus) {
        this.dynamicStatus = dynamicStatus;
    }

    public Integer getExistStatus() {
        return existStatus;
    }

    public void setExistStatus(Integer existStatus) {
        this.existStatus = existStatus;
    }

    public String getMarPosition() {
        return marPosition;
    }

    public void setMarPosition(String marPosition) {
        this.marPosition = marPosition;
    }

    public String getOperarea() {
        return operarea;
    }

    public void setOperarea(String operarea) {
        this.operarea = operarea;
    }
}
