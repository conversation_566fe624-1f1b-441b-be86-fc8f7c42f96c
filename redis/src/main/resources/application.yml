server:
  port: 8081
spring:
    # Redis数据库索引（默认为0）
  redis:
      #url: redis://127.0.0.1:6379
      database: 2
    # Redis服务器地址
      host: common_microservice_redis-01_test.cowelltech.com
    # Redis服务器连接端口
      port: 6379
    # Redis服务器连接密码（默认为空）
    #  password: 123456
    # 连接池中的最小空闲连接
    # 连接超时时间（毫秒）
      timeout: 1000
      jedis:
        pool:
          max-idle: 200
          max-wait:
          min-idle: 10

logging:
  level:
    org:
      mybatis:
        spring: INFO
      springframework: INFO
      ibatis:
       common.jdbc.SimpleDataSource: INFO
       common.jdbc.ScriptRunner: INFO
       sqlmap.engine.impl.SqlMapClientDelegate: INFO
    java.sql:
     Connection: INFO
     Statement: INFO
     PreparedStatement: INFO

