-- Generated INSERT statements for hd_web_component_field table

-- Field 1: cashPointsAmount
INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-points',
'cashPointsAmount',
'积分抵现金额',
'cashPointsAmount',
'',
'string',
'',
'',
'积分抵现-积分抵现金额',
1,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

-- Field 2: cashPointsMaxAmount
INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-points',
'cashPointsMaxAmount',
'积分抵现最高金额',
'cashPointsMaxAmount',
'',
'string',
'',
'',
'积分抵现-积分抵现最高金额',
2,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

-- Field 3: cashPointsPerCash
INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-points',
'cashPointsPerCash',
'积分抵现规则',
'cashPointsPerCash',
'',
'string',
'',
'',
'积分抵现-积分抵现规则',
3,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

-- Field 4: pointsCashReasons
INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-points',
'pointsCashReasons',
'不参与积分抵现原因',
'pointsCashReasons',
'',
'string',
'',
'',
'积分抵现-不参与积分抵现原因',
4,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

-- Field 5: cashPoints
INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-points',
'cashPoints',
'本单抵扣积分',
'cashPoints',
'',
'string',
'',
'',
'积分抵现-本单抵扣积分',
5,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

-- Field 6: bonusPoints
INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-points',
'bonusPoints',
'本单赠送积分',
'bonusPoints',
'',
'string',
'',
'',
'积分抵现-本单赠送积分',
6,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

-- Field 7: pointsReasons
INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-points',
'pointsReasons',
'不参与积分赠送原因',
'pointsReasons',
'',
'string',
'',
'',
'积分抵现-不参与积分赠送原因',
7,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

-- Field 8: cashPointsMaxPoints
INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-points',
'cashPointsMaxPoints',
'最高金额需抵扣积分',
'cashPointsMaxPoints',
'',
'string',
'',
'',
'积分抵现-最高金额需抵扣积分',
8,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');
