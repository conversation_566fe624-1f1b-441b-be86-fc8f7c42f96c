INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-coupon',
'disabled',
'是否可用',
'couponList[*].disabled',
'',
'int',
'',
'',
'代金券-是否可用',
1,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');


参考这个 对表格中的字段 批量生成 insert 语句 排序就按 表格顺序   其他字段参考 demo sql

说明 对应字段 file_name     "代金券-"+说明  对应字段  description

| 字段名               | 数据类型          | 说明                 |
|----------------------|-------------------|----------------------|
| disabled             | int               | 是否可用             |
| couponTitle          | string            | 券名称               |
| fav                  | string            | 本单优惠金额         |
| couponUseDesc        | string            | 代金券规则           |
| reasonType           | List<string>      | 不可用原因           |
| couponFav            | string            | 公允价值             |
| couponCategory       | int               | 优惠券种类           |
| couponType           | int               | 优惠券类型           |
| goodsNoList          | List<string>      | 可用券商品           |
| couponPromotionDesc  | string            | 叠加规则             |
| couponCode           | string            | 券编码               |


-- 以下是生成的SQL语句

-- disabled已在示例中

INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-coupon',
'couponTitle',
'券名称',
'couponList[*].couponTitle',
'',
'string',
'',
'',
'代金券-券名称',
2,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-coupon',
'fav',
'本单优惠金额',
'couponList[*].fav',
'',
'string',
'',
'',
'代金券-本单优惠金额',
3,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-coupon',
'couponUseDesc',
'代金券规则',
'couponList[*].couponUseDesc',
'',
'string',
'',
'',
'代金券-代金券规则',
4,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-coupon',
'reasonType',
'不可用原因',
'couponList[*].reasonType',
'',
'List<string>',
'',
'',
'代金券-不可用原因',
5,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-coupon',
'couponFav',
'公允价值',
'couponList[*].couponFav',
'',
'string',
'',
'',
'代金券-公允价值',
6,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-coupon',
'couponCategory',
'优惠券种类',
'couponList[*].couponCategory',
'',
'int',
'',
'',
'代金券-优惠券种类',
7,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-coupon',
'couponType',
'优惠券类型',
'couponList[*].couponType',
'',
'int',
'',
'',
'代金券-优惠券类型',
8,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-coupon',
'goodsNoList',
'可用券商品',
'couponList[*].goodsNoList',
'',
'List<string>',
'',
'',
'代金券-可用券商品',
9,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-coupon',
'couponPromotionDesc',
'叠加规则',
'couponList[*].couponPromotionDesc',
'',
'string',
'',
'',
'代金券-叠加规则',
10,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');

INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-coupon',
'couponCode',
'券编码',
'couponList[*].couponCode',
'',
'string',
'',
'',
'代金券-券编码',
11,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');