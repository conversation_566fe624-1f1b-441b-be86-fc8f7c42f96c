INSERT INTO `cowell_cart`.`hd_web_component_field` (
`component_code`,
`field_code`,
`field_name`,
`json_path`,
`assigned_group`,
`field_type`,
`default_value`,
`validation_rules`,
`description`,
`sort_order`,
`required_flag`,
`edit_flag`,
`visible_flag`,
`status`,
`extend`,
`creater`,
`created_by`,
`updater`,
`updated_by`,
`gmt_create`,
`gmt_update`)
VALUES
(
'gj-promotion-points',
'cashPointsAmount',
'积分抵现金额',
'cashPointsAmount',
'',
'string',
'',
'',
'积分抵现-积分抵现金额',
1,
1,
0,
1,
1,
'',
'admin',
0,
'admin',
0,
'2025-01-20 14:26:50',
'2025-03-24 14:04:01');



参考这个 对表格中的字段 批量生成 insert 语句 排序就按 表格顺序   其他字段参考 demo sql

说明 对应字段 file_name     "代金券-"+说明  对应字段  description

以下是转换后的 md 表格格式：

| 字段名                  | 数据类型          | 说明                     |
|-------------------------|-------------------|--------------------------|
| cashPointsAmount        | String            | 积分抵现金额             |
| cashPointsMaxAmount     | String            | 积分抵现最高金额         |
| cashPointsPerCash       | String            | 积分抵现规则             |
| pointsCashReasons       | List<String>      | 不参与积分抵现原因       |
| cashPoints              | String            | 本单抵扣积分             |
| bonusPoints             | String            | 本单赠送积分             |
| pointsReasons           | List<String>      | 不参与积分赠送原因       |
| cashPointsMaxPoints     | String            | 最高金额需抵扣积分       |