package com.tomas.mybaties3.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 投票记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class Order implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户Id 商户订单号
     */

    private String orderId;

    /**
     * 用户Id 商户订单号
     */

    private String thirdPartyOrderNo;


    /**
     * 用户Id 商户订单号
     */

    private String businessId;


    private String storeId;

    private Date payTime;


}
