package com.tomas.mybaties3.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 经营目录commonDto
 */
@Data
public class ManageCommonDTO implements AmisDataInInterface, Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "门店Id")
    private Long storeId;

    @ApiModelProperty(value = "big")
    private BigDecimal big;

    @ApiModelProperty(value = "门店Code")
    private String storeCode;

    @ApiModelProperty(value = "店型")
    private String storeType;

    @ApiModelProperty(value = "店型描述")
    private String storeTypeDesc;

    @ApiModelProperty(value = "大类id")
    private String category;

    @ApiModelProperty(value = "大类名称")
    private String categoryName;

    @ApiModelProperty(value = "中类id")
    private String middleCategory;

    @ApiModelProperty(value = "中类名称")
    private String middleCategoryName;

    @ApiModelProperty(value = "小类编码")
    private String smallCategory;

    @ApiModelProperty(value = "小类名称")
    private String smallCategoryName;

    @ApiModelProperty(value = "子类编码")
    private String subCategory;

    @ApiModelProperty(value = "子类名称")
    private String subCategoryName;

    @ApiModelProperty(value = "商品编码")
    private String goodsNo;
    @ApiModelProperty(value = "商品名")
    private String goodsName;

    @ApiModelProperty(value = "建议经营状态")
    private String suggestManageStatus;

    @ApiModelProperty(value = "建议经营状态名称")
    private String suggestManageStatusName;

    @ApiModelProperty(value = "我的经营状态")
    private String manageStatus;

    @ApiModelProperty(value = "我的经营状态名称")
    private String manageStatusName;

    @ApiModelProperty(value = "商品大类是rx/otc")
    private String rxOtc;

    @ApiModelProperty(value = "条码")
    private String barCode;

    @ApiModelProperty(value = "商品通用名")
    private String goodsCommonName;

    @ApiModelProperty(value = "单位")
    private String goodsunit;

    @ApiModelProperty(value = "商品描述")
    private String description;

    @ApiModelProperty(value = "规格")
    private String jhiSpecification;

    @ApiModelProperty(value = "剂型")
    private String dosageformsid;

    @ApiModelProperty(value = "生产厂家")
    private String factoryid;

    @ApiModelProperty(value = "经营属性")
    private String goodsline;

    @ApiModelProperty(value = "必备标识(0非必备)")
    private Integer necessaryTag;

    @ApiModelProperty(value = "必备标识名")
    private String necessaryTagName;

    @ApiModelProperty(value = "销售属性")
    private String pushlevel;

    @ApiModelProperty(value = "库存数量")
    private String stockQuantity;

    @ApiModelProperty(value = "近30天销售数量")
    private String salesLast30d;

    @ApiModelProperty(value = "近60天销售数量")
    private String salesLast60d;

    @ApiModelProperty(value = "近90天销售数量")
    private String salesLast90d;

    @ApiModelProperty(value = "同城市近90天门店动销率")
    private String cityStoreSalesRate90d;

    @ApiModelProperty(value = "同店型近90天门店动销率")
    private String storeTypeSalesRate90d;

    @ApiModelProperty(value = "滞销天数")
    private String daysUnsold;

    @ApiModelProperty(value = "城市内商品综合贡献排名")
    private String cityProductContributionRank;

    @ApiModelProperty(value = "城市内同成分/子类综合贡献排名")
    private String citySubcategoryContributionRank;

    @ApiModelProperty(value = "成分")
    private String component;

    @ApiModelProperty(value = "行是否可编辑")
    private Boolean editAble = true;

}
