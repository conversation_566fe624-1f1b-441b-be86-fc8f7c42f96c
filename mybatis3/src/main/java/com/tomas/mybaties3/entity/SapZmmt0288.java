package com.tomas.mybaties3.entity;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR> ZMMR0055创建PO日志表
 */
public class SapZmmt0288 implements Serializable {
    private Long id;

    /**
     * 集团
     */
    private String mandt;

    /**
     * CHAR 格式的大写的 GUID
     */
    private String zguid;

    /**
     * 工厂
     */
    private String werks;

    /**
     * 物料编号
     */
    private String matnr;

    /**
     * 采购组织
     */
    private String ekorg;

    /**
     * 采购凭证类型
     */
    private String bsart;

    /**
     * 公司代码
     */
    private String bukrs;

    /**
     * 采购组
     */
    private String ekgrp;

    /**
     * DC描述
     */
    private String name1Dc;

    /**
     * 下单供应商
     */
    private String lifnrXd;

    /**
     * 下单供应商描述
     */
    private String name1Xd;

    /**
     * 采购下单数量
     */
    private BigDecimal mengeXd;

    /**
     * 采购单位
     */
    private String meinsXd;

    /**
     * 下单单价
     */
    private BigDecimal netprXd;

    /**
     * 下单含税价
     */
    private BigDecimal brtwrXd;

    /**
     * 下单净价总金额
     */
    private BigDecimal netsumXd;

    /**
     * 下单含税总金额
     */
    private BigDecimal brtsumXd;

    /**
     * 价格单位
     */
    private Integer peinhXd;

    /**
     * 下单税码
     */
    private String taxXd;

    /**
     * 税率文本
     */
    private String taxXdT;

    /**
     * 台账系统合同编号
     */
    private String konnr;

    /**
     * 台账系统合同行号
     */
    private String ktpnr;

    /**
     * 处理意见编码
     */
    private String zplcode;

    /**
     * 补货处理意见备注
     */
    private String zplsug;

    /**
     * 厂家描述
     */
    private String zcjms;

    /**
     * 付款条件代码
     */
    private String zterm;

    /**
     * 付款条件描述
     */
    private String ztermT;

    /**
     * 采购包装设置
     */
    private String zcgbzsz;

    /**
     * 计算建议数量
     */
    private BigDecimal mengeSug;

    /**
     * 基本计量单位
     */
    private String meinsSug;

    /**
     * 中包装建议数量
     */
    private BigDecimal mengeZbz;

    /**
     * 箱包装建议数量
     */
    private BigDecimal mengeXbz;

    /**
     * 最终建议量
     */
    private BigDecimal mengeZz;

    /**
     * 基本计量单位
     */
    private String meinsZz;

    /**
     * 中包装量
     */
    private String zzbzl;

    /**
     * 箱包装量
     */
    private BigDecimal zzcqty;

    /**
     * 向上进位系数
     */
    private BigDecimal zxsjwxs;

    /**
     * 向下进位系数
     */
    private BigDecimal zxxjwxs;

    /**
     * 科目分配类别
     */
    private String knttp;

    /**
     * 成本中心
     */
    private String kostl;

    /**
     * 业务员编号
     */
    private String zuserid;

    /**
     * 业务员姓名
     */
    private String zywxm;

    /**
     * 转换系数
     */
    private String zzhxs;

    /**
     * 额外补充订货量_(人工)
     */
    private BigDecimal zbcdhl;

    /**
     * 门店组
     */
    private String zmdz;

    /**
     * 门店组描述
     */
    private String zmdzT;

    /**
     * 审批级别
     */
    private Short zspjb;

    /**
     * 采购组织
     */
    private String ekorg02;

    /**
     * 商品仓库订单类型值
     */
    private String zspckddlx;

    /**
     * 供货DC
     */
    private String zghdc;

    /**
     * 箱包装量
     */
    private BigDecimal zzcqtyBdp;

    /**
     * 中包装量
     */
    private BigDecimal zzbzlBdp;

    /**
     * 计划交货日期
     */
    private String zjhjh;

    /**
     * 库存地点
     */
    private String lgort;

    /**
     * 包装规格
     */
    private String zbzgg;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建日期
     */
    private String zcrdate;

    /**
     * 创建时间
     */
    private String zcrtime;

    /**
     * 创建人
     */
    private String zcrname;

    /**
     * 修改日期
     */
    private String zchdate;

    /**
     * 修改时间
     */
    private String zchtime;

    /**
     * 修改人
     */
    private String zchname;

    /**
     * 采购凭证编号
     */
    private String ebeln;

    /**
     * 采购凭证的项目编号
     */
    private String ebelp;

    /**
     * 采购申请单号
     */
    private String purreqno;

    /**
     * 采购申请单号行项目
     */
    private String storelineno;

    /**
     * 订单类型
     */
    private String zbyzd2;

    /**
     * 存销比
     */
    private BigDecimal zstockUse;

    /**
     * DC库存
     */
    private BigDecimal labstDcJs;

    /**
     * 未清采购
     */
    private BigDecimal mengeWqJs;

    /**
     * 前30天累计销量
     */
    private BigDecimal zzxl30Js;

    /**
     * 门店库存
     */
    private BigDecimal zmdqtyJs;

    /**
     * 特殊订单标识
     */
    private String ztsddbs;

    /**
     * 特殊订单原单号
     */
    private String ztsddydh;

    /**
     * 前序订单单号
     */
    private String zzqxdjh;

    /**
     * 前序单据行号
     */
    private String zzqxdhh;

    /**
     * 集团内低价推荐
     */
    private String zjtndjtj;

    /**
     * 参与计算未清
     */
    private BigDecimal zcyjswq;

    /**
     * 退仓未清
     */
    private BigDecimal ztcwq;

    /**
     * DC可用库存
     */
    private BigDecimal zdckykc;

    /**
     * 未清采购
     */
    private BigDecimal mengeWq;

    /**
     * 备货提前期调整天数
     */
    private Integer tzts;

    /**
     * 备货提前期调整天数有效期至
     */
    private String tztsYxqz;

    /**
     * 供应商前置期T
     */
    private Integer zgysqzq;

    /**
     * 商品前置期(天)
     */
    private Integer zspqzq;

    /**
     * 商品前置期天数有效期起始日期
     */
    private String zspqzqqsrq;

    /**
     * 商品前置期天数有效期终止日期
     */
    private String zspqzqzzrq;

    /**
     * 额外补充库存天数
     */
    private Long zbckcts;

    /**
     * 补充库存天数有效期起始日期
     */
    private String zbckctsqsrq;

    /**
     * 补充库存天数有效期终止日期
     */
    private String zbckctszzrq;

    /**
     * 额外补充订货量有效期起始日期
     */
    private String zbcdhlqsrq;

    /**
     * 额外补充订货量有效期终止日期
     */
    private String zbcdhlzzrq;

    /**
     * 是否计算
     */
    private String zsfjs;

    /**
     * 最小请货倍数
     */
    private Long zxqhbs;

    /**
     * 安全库存补充天数
     */
    private Long zaqkcbcts;

    /**
     * 是否取整
     */
    private String zsfqz;

    /**
     * 特殊销售起始日期
     */
    private String ztsxsqsrq;

    /**
     * 特殊销售结束日期
     */
    private String ztsxsjsrq;

    /**
     * 特殊销售权重占比%
     */
    private Short ztsxsqzzb;

    /**
     * 库存上限
     */
    private Long zkcsx;

    /**
     * 库存下限
     */
    private Long zkcxx;

    /**
     * 采购谈判经理工号
     */
    private String zcgtpjl;

    /**
     * 采购谈判经理姓名
     */
    private String zcgtpjlxm;

    /**
     * 有建议未下单天数
     */
    private Integer zyjywxdts;

    /**
     * 最新一次的采购含税价
     */
    private BigDecimal brtwrLast;

    /**
     * 最新一次的采购价格单位
     */
    private Integer peinhLast;

    /**
     * 连锁仓可用库存
     */
    private BigDecimal ztzdckc;

    /**
     * 大仓库存_停用编码
     */
    private BigDecimal zstockDc;

    /**
     * 门店库存_停用编码
     */
    private BigDecimal zstockStore;

    /**
     * 连锁仓库存_停用编码
     */
    private BigDecimal zzcInactiveDcStock;

    /**
     * 门店占用库存_订货量
     */
    private BigDecimal zstockTotal;

    /**
     * 门店占用库存_订货点
     */
    private Long zfixTbs;

    /**
     * 门店预期库存量
     */
    private Long zsumRV;

    /**
     * 连锁仓预期库存量
     */
    private Long zdcInvUpper;

    /**
     * 连锁仓占用库存_订货点
     */
    private BigDecimal ztzdcdkc1;

    /**
     * 连锁仓占用库存_订货量
     */
    private BigDecimal ztzdcdkc2;

    /**
     * BDP未清采购
     */
    private BigDecimal zmengeWq7;

    /**
     * 未满足最大请货量
     */
    private BigDecimal zmaxApply;

    /**
     * BDP库存上限
     */
    private BigDecimal zinvUpper;

    /**
     * BDP建议订货量
     */
    private BigDecimal zmengeSug;

    /**
     * 订货点
     */
    private Long zdhdB;

    /**
     * 订货间隔天数
     */
    private Integer zdcL;

    /**
     * 商品等级
     */
    private String zgoodsL;

    /**
     * 安全库存数
     */
    private Long zsStock;

    /**
     * 原初补货量
     */
    private Long zraw;

    /**
     * 不计算存销比标识
     */
    private String znoncxb;

    /**
     * 加盟店库存
     */
    private BigDecimal zjmStoreStock;

    /**
     * 订货点
     */
    private BigDecimal zdhd;

    /**
     * 合同类型标识
     */
    private String zhtlxbs;

    /**
     * 偏移原因类型
     */
    private String reasonType;

    /**
     * 处理意见编码
     */
    private String zpyyy;

    /**
     * 下单含税价
     */
    private BigDecimal brtwrJy;

    /**
     * 合同类型标识
     */
    private String zhtlxbsJy;

    /**
     * 内采申请未清
     */
    private BigDecimal zncsqwq;

    private static final long serialVersionUID = 1L;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getMandt() {
        return mandt;
    }

    public void setMandt(String mandt) {
        this.mandt = mandt;
    }

    public String getZguid() {
        return zguid;
    }

    public void setZguid(String zguid) {
        this.zguid = zguid;
    }

    public String getWerks() {
        return werks;
    }

    public void setWerks(String werks) {
        this.werks = werks;
    }

    public String getMatnr() {
        return matnr;
    }

    public void setMatnr(String matnr) {
        this.matnr = matnr;
    }

    public String getEkorg() {
        return ekorg;
    }

    public void setEkorg(String ekorg) {
        this.ekorg = ekorg;
    }

    public String getBsart() {
        return bsart;
    }

    public void setBsart(String bsart) {
        this.bsart = bsart;
    }

    public String getBukrs() {
        return bukrs;
    }

    public void setBukrs(String bukrs) {
        this.bukrs = bukrs;
    }

    public String getEkgrp() {
        return ekgrp;
    }

    public void setEkgrp(String ekgrp) {
        this.ekgrp = ekgrp;
    }

    public String getName1Dc() {
        return name1Dc;
    }

    public void setName1Dc(String name1Dc) {
        this.name1Dc = name1Dc;
    }

    public String getLifnrXd() {
        return lifnrXd;
    }

    public void setLifnrXd(String lifnrXd) {
        this.lifnrXd = lifnrXd;
    }

    public String getName1Xd() {
        return name1Xd;
    }

    public void setName1Xd(String name1Xd) {
        this.name1Xd = name1Xd;
    }

    public BigDecimal getMengeXd() {
        return mengeXd;
    }

    public void setMengeXd(BigDecimal mengeXd) {
        this.mengeXd = mengeXd;
    }

    public String getMeinsXd() {
        return meinsXd;
    }

    public void setMeinsXd(String meinsXd) {
        this.meinsXd = meinsXd;
    }

    public BigDecimal getNetprXd() {
        return netprXd;
    }

    public void setNetprXd(BigDecimal netprXd) {
        this.netprXd = netprXd;
    }

    public BigDecimal getBrtwrXd() {
        return brtwrXd;
    }

    public void setBrtwrXd(BigDecimal brtwrXd) {
        this.brtwrXd = brtwrXd;
    }

    public BigDecimal getNetsumXd() {
        return netsumXd;
    }

    public void setNetsumXd(BigDecimal netsumXd) {
        this.netsumXd = netsumXd;
    }

    public BigDecimal getBrtsumXd() {
        return brtsumXd;
    }

    public void setBrtsumXd(BigDecimal brtsumXd) {
        this.brtsumXd = brtsumXd;
    }

    public Integer getPeinhXd() {
        return peinhXd;
    }

    public void setPeinhXd(Integer peinhXd) {
        this.peinhXd = peinhXd;
    }

    public String getTaxXd() {
        return taxXd;
    }

    public void setTaxXd(String taxXd) {
        this.taxXd = taxXd;
    }

    public String getTaxXdT() {
        return taxXdT;
    }

    public void setTaxXdT(String taxXdT) {
        this.taxXdT = taxXdT;
    }

    public String getKonnr() {
        return konnr;
    }

    public void setKonnr(String konnr) {
        this.konnr = konnr;
    }

    public String getKtpnr() {
        return ktpnr;
    }

    public void setKtpnr(String ktpnr) {
        this.ktpnr = ktpnr;
    }

    public String getZplcode() {
        return zplcode;
    }

    public void setZplcode(String zplcode) {
        this.zplcode = zplcode;
    }

    public String getZplsug() {
        return zplsug;
    }

    public void setZplsug(String zplsug) {
        this.zplsug = zplsug;
    }

    public String getZcjms() {
        return zcjms;
    }

    public void setZcjms(String zcjms) {
        this.zcjms = zcjms;
    }

    public String getZterm() {
        return zterm;
    }

    public void setZterm(String zterm) {
        this.zterm = zterm;
    }

    public String getZtermT() {
        return ztermT;
    }

    public void setZtermT(String ztermT) {
        this.ztermT = ztermT;
    }

    public String getZcgbzsz() {
        return zcgbzsz;
    }

    public void setZcgbzsz(String zcgbzsz) {
        this.zcgbzsz = zcgbzsz;
    }

    public BigDecimal getMengeSug() {
        return mengeSug;
    }

    public void setMengeSug(BigDecimal mengeSug) {
        this.mengeSug = mengeSug;
    }

    public String getMeinsSug() {
        return meinsSug;
    }

    public void setMeinsSug(String meinsSug) {
        this.meinsSug = meinsSug;
    }

    public BigDecimal getMengeZbz() {
        return mengeZbz;
    }

    public void setMengeZbz(BigDecimal mengeZbz) {
        this.mengeZbz = mengeZbz;
    }

    public BigDecimal getMengeXbz() {
        return mengeXbz;
    }

    public void setMengeXbz(BigDecimal mengeXbz) {
        this.mengeXbz = mengeXbz;
    }

    public BigDecimal getMengeZz() {
        return mengeZz;
    }

    public void setMengeZz(BigDecimal mengeZz) {
        this.mengeZz = mengeZz;
    }

    public String getMeinsZz() {
        return meinsZz;
    }

    public void setMeinsZz(String meinsZz) {
        this.meinsZz = meinsZz;
    }

    public String getZzbzl() {
        return zzbzl;
    }

    public void setZzbzl(String zzbzl) {
        this.zzbzl = zzbzl;
    }

    public BigDecimal getZzcqty() {
        return zzcqty;
    }

    public void setZzcqty(BigDecimal zzcqty) {
        this.zzcqty = zzcqty;
    }

    public BigDecimal getZxsjwxs() {
        return zxsjwxs;
    }

    public void setZxsjwxs(BigDecimal zxsjwxs) {
        this.zxsjwxs = zxsjwxs;
    }

    public BigDecimal getZxxjwxs() {
        return zxxjwxs;
    }

    public void setZxxjwxs(BigDecimal zxxjwxs) {
        this.zxxjwxs = zxxjwxs;
    }

    public String getKnttp() {
        return knttp;
    }

    public void setKnttp(String knttp) {
        this.knttp = knttp;
    }

    public String getKostl() {
        return kostl;
    }

    public void setKostl(String kostl) {
        this.kostl = kostl;
    }

    public String getZuserid() {
        return zuserid;
    }

    public void setZuserid(String zuserid) {
        this.zuserid = zuserid;
    }

    public String getZywxm() {
        return zywxm;
    }

    public void setZywxm(String zywxm) {
        this.zywxm = zywxm;
    }

    public String getZzhxs() {
        return zzhxs;
    }

    public void setZzhxs(String zzhxs) {
        this.zzhxs = zzhxs;
    }

    public BigDecimal getZbcdhl() {
        return zbcdhl;
    }

    public void setZbcdhl(BigDecimal zbcdhl) {
        this.zbcdhl = zbcdhl;
    }

    public String getZmdz() {
        return zmdz;
    }

    public void setZmdz(String zmdz) {
        this.zmdz = zmdz;
    }

    public String getZmdzT() {
        return zmdzT;
    }

    public void setZmdzT(String zmdzT) {
        this.zmdzT = zmdzT;
    }

    public Short getZspjb() {
        return zspjb;
    }

    public void setZspjb(Short zspjb) {
        this.zspjb = zspjb;
    }

    public String getEkorg02() {
        return ekorg02;
    }

    public void setEkorg02(String ekorg02) {
        this.ekorg02 = ekorg02;
    }

    public String getZspckddlx() {
        return zspckddlx;
    }

    public void setZspckddlx(String zspckddlx) {
        this.zspckddlx = zspckddlx;
    }

    public String getZghdc() {
        return zghdc;
    }

    public void setZghdc(String zghdc) {
        this.zghdc = zghdc;
    }

    public BigDecimal getZzcqtyBdp() {
        return zzcqtyBdp;
    }

    public void setZzcqtyBdp(BigDecimal zzcqtyBdp) {
        this.zzcqtyBdp = zzcqtyBdp;
    }

    public BigDecimal getZzbzlBdp() {
        return zzbzlBdp;
    }

    public void setZzbzlBdp(BigDecimal zzbzlBdp) {
        this.zzbzlBdp = zzbzlBdp;
    }

    public String getZjhjh() {
        return zjhjh;
    }

    public void setZjhjh(String zjhjh) {
        this.zjhjh = zjhjh;
    }

    public String getLgort() {
        return lgort;
    }

    public void setLgort(String lgort) {
        this.lgort = lgort;
    }

    public String getZbzgg() {
        return zbzgg;
    }

    public void setZbzgg(String zbzgg) {
        this.zbzgg = zbzgg;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getZcrdate() {
        return zcrdate;
    }

    public void setZcrdate(String zcrdate) {
        this.zcrdate = zcrdate;
    }

    public String getZcrtime() {
        return zcrtime;
    }

    public void setZcrtime(String zcrtime) {
        this.zcrtime = zcrtime;
    }

    public String getZcrname() {
        return zcrname;
    }

    public void setZcrname(String zcrname) {
        this.zcrname = zcrname;
    }

    public String getZchdate() {
        return zchdate;
    }

    public void setZchdate(String zchdate) {
        this.zchdate = zchdate;
    }

    public String getZchtime() {
        return zchtime;
    }

    public void setZchtime(String zchtime) {
        this.zchtime = zchtime;
    }

    public String getZchname() {
        return zchname;
    }

    public void setZchname(String zchname) {
        this.zchname = zchname;
    }

    public String getEbeln() {
        return ebeln;
    }

    public void setEbeln(String ebeln) {
        this.ebeln = ebeln;
    }

    public String getEbelp() {
        return ebelp;
    }

    public void setEbelp(String ebelp) {
        this.ebelp = ebelp;
    }

    public String getPurreqno() {
        return purreqno;
    }

    public void setPurreqno(String purreqno) {
        this.purreqno = purreqno;
    }

    public String getStorelineno() {
        return storelineno;
    }

    public void setStorelineno(String storelineno) {
        this.storelineno = storelineno;
    }

    public String getZbyzd2() {
        return zbyzd2;
    }

    public void setZbyzd2(String zbyzd2) {
        this.zbyzd2 = zbyzd2;
    }

    public BigDecimal getZstockUse() {
        return zstockUse;
    }

    public void setZstockUse(BigDecimal zstockUse) {
        this.zstockUse = zstockUse;
    }

    public BigDecimal getLabstDcJs() {
        return labstDcJs;
    }

    public void setLabstDcJs(BigDecimal labstDcJs) {
        this.labstDcJs = labstDcJs;
    }

    public BigDecimal getMengeWqJs() {
        return mengeWqJs;
    }

    public void setMengeWqJs(BigDecimal mengeWqJs) {
        this.mengeWqJs = mengeWqJs;
    }

    public BigDecimal getZzxl30Js() {
        return zzxl30Js;
    }

    public void setZzxl30Js(BigDecimal zzxl30Js) {
        this.zzxl30Js = zzxl30Js;
    }

    public BigDecimal getZmdqtyJs() {
        return zmdqtyJs;
    }

    public void setZmdqtyJs(BigDecimal zmdqtyJs) {
        this.zmdqtyJs = zmdqtyJs;
    }

    public String getZtsddbs() {
        return ztsddbs;
    }

    public void setZtsddbs(String ztsddbs) {
        this.ztsddbs = ztsddbs;
    }

    public String getZtsddydh() {
        return ztsddydh;
    }

    public void setZtsddydh(String ztsddydh) {
        this.ztsddydh = ztsddydh;
    }

    public String getZzqxdjh() {
        return zzqxdjh;
    }

    public void setZzqxdjh(String zzqxdjh) {
        this.zzqxdjh = zzqxdjh;
    }

    public String getZzqxdhh() {
        return zzqxdhh;
    }

    public void setZzqxdhh(String zzqxdhh) {
        this.zzqxdhh = zzqxdhh;
    }

    public String getZjtndjtj() {
        return zjtndjtj;
    }

    public void setZjtndjtj(String zjtndjtj) {
        this.zjtndjtj = zjtndjtj;
    }

    public BigDecimal getZcyjswq() {
        return zcyjswq;
    }

    public void setZcyjswq(BigDecimal zcyjswq) {
        this.zcyjswq = zcyjswq;
    }

    public BigDecimal getZtcwq() {
        return ztcwq;
    }

    public void setZtcwq(BigDecimal ztcwq) {
        this.ztcwq = ztcwq;
    }

    public BigDecimal getZdckykc() {
        return zdckykc;
    }

    public void setZdckykc(BigDecimal zdckykc) {
        this.zdckykc = zdckykc;
    }

    public BigDecimal getMengeWq() {
        return mengeWq;
    }

    public void setMengeWq(BigDecimal mengeWq) {
        this.mengeWq = mengeWq;
    }

    public Integer getTzts() {
        return tzts;
    }

    public void setTzts(Integer tzts) {
        this.tzts = tzts;
    }

    public String getTztsYxqz() {
        return tztsYxqz;
    }

    public void setTztsYxqz(String tztsYxqz) {
        this.tztsYxqz = tztsYxqz;
    }

    public Integer getZgysqzq() {
        return zgysqzq;
    }

    public void setZgysqzq(Integer zgysqzq) {
        this.zgysqzq = zgysqzq;
    }

    public Integer getZspqzq() {
        return zspqzq;
    }

    public void setZspqzq(Integer zspqzq) {
        this.zspqzq = zspqzq;
    }

    public String getZspqzqqsrq() {
        return zspqzqqsrq;
    }

    public void setZspqzqqsrq(String zspqzqqsrq) {
        this.zspqzqqsrq = zspqzqqsrq;
    }

    public String getZspqzqzzrq() {
        return zspqzqzzrq;
    }

    public void setZspqzqzzrq(String zspqzqzzrq) {
        this.zspqzqzzrq = zspqzqzzrq;
    }

    public Long getZbckcts() {
        return zbckcts;
    }

    public void setZbckcts(Long zbckcts) {
        this.zbckcts = zbckcts;
    }

    public String getZbckctsqsrq() {
        return zbckctsqsrq;
    }

    public void setZbckctsqsrq(String zbckctsqsrq) {
        this.zbckctsqsrq = zbckctsqsrq;
    }

    public String getZbckctszzrq() {
        return zbckctszzrq;
    }

    public void setZbckctszzrq(String zbckctszzrq) {
        this.zbckctszzrq = zbckctszzrq;
    }

    public String getZbcdhlqsrq() {
        return zbcdhlqsrq;
    }

    public void setZbcdhlqsrq(String zbcdhlqsrq) {
        this.zbcdhlqsrq = zbcdhlqsrq;
    }

    public String getZbcdhlzzrq() {
        return zbcdhlzzrq;
    }

    public void setZbcdhlzzrq(String zbcdhlzzrq) {
        this.zbcdhlzzrq = zbcdhlzzrq;
    }

    public String getZsfjs() {
        return zsfjs;
    }

    public void setZsfjs(String zsfjs) {
        this.zsfjs = zsfjs;
    }

    public Long getZxqhbs() {
        return zxqhbs;
    }

    public void setZxqhbs(Long zxqhbs) {
        this.zxqhbs = zxqhbs;
    }

    public Long getZaqkcbcts() {
        return zaqkcbcts;
    }

    public void setZaqkcbcts(Long zaqkcbcts) {
        this.zaqkcbcts = zaqkcbcts;
    }

    public String getZsfqz() {
        return zsfqz;
    }

    public void setZsfqz(String zsfqz) {
        this.zsfqz = zsfqz;
    }

    public String getZtsxsqsrq() {
        return ztsxsqsrq;
    }

    public void setZtsxsqsrq(String ztsxsqsrq) {
        this.ztsxsqsrq = ztsxsqsrq;
    }

    public String getZtsxsjsrq() {
        return ztsxsjsrq;
    }

    public void setZtsxsjsrq(String ztsxsjsrq) {
        this.ztsxsjsrq = ztsxsjsrq;
    }

    public Short getZtsxsqzzb() {
        return ztsxsqzzb;
    }

    public void setZtsxsqzzb(Short ztsxsqzzb) {
        this.ztsxsqzzb = ztsxsqzzb;
    }

    public Long getZkcsx() {
        return zkcsx;
    }

    public void setZkcsx(Long zkcsx) {
        this.zkcsx = zkcsx;
    }

    public Long getZkcxx() {
        return zkcxx;
    }

    public void setZkcxx(Long zkcxx) {
        this.zkcxx = zkcxx;
    }

    public String getZcgtpjl() {
        return zcgtpjl;
    }

    public void setZcgtpjl(String zcgtpjl) {
        this.zcgtpjl = zcgtpjl;
    }

    public String getZcgtpjlxm() {
        return zcgtpjlxm;
    }

    public void setZcgtpjlxm(String zcgtpjlxm) {
        this.zcgtpjlxm = zcgtpjlxm;
    }

    public Integer getZyjywxdts() {
        return zyjywxdts;
    }

    public void setZyjywxdts(Integer zyjywxdts) {
        this.zyjywxdts = zyjywxdts;
    }

    public BigDecimal getBrtwrLast() {
        return brtwrLast;
    }

    public void setBrtwrLast(BigDecimal brtwrLast) {
        this.brtwrLast = brtwrLast;
    }

    public Integer getPeinhLast() {
        return peinhLast;
    }

    public void setPeinhLast(Integer peinhLast) {
        this.peinhLast = peinhLast;
    }

    public BigDecimal getZtzdckc() {
        return ztzdckc;
    }

    public void setZtzdckc(BigDecimal ztzdckc) {
        this.ztzdckc = ztzdckc;
    }

    public BigDecimal getZstockDc() {
        return zstockDc;
    }

    public void setZstockDc(BigDecimal zstockDc) {
        this.zstockDc = zstockDc;
    }

    public BigDecimal getZstockStore() {
        return zstockStore;
    }

    public void setZstockStore(BigDecimal zstockStore) {
        this.zstockStore = zstockStore;
    }

    public BigDecimal getZzcInactiveDcStock() {
        return zzcInactiveDcStock;
    }

    public void setZzcInactiveDcStock(BigDecimal zzcInactiveDcStock) {
        this.zzcInactiveDcStock = zzcInactiveDcStock;
    }

    public BigDecimal getZstockTotal() {
        return zstockTotal;
    }

    public void setZstockTotal(BigDecimal zstockTotal) {
        this.zstockTotal = zstockTotal;
    }

    public Long getZfixTbs() {
        return zfixTbs;
    }

    public void setZfixTbs(Long zfixTbs) {
        this.zfixTbs = zfixTbs;
    }

    public Long getZsumRV() {
        return zsumRV;
    }

    public void setZsumRV(Long zsumRV) {
        this.zsumRV = zsumRV;
    }

    public Long getZdcInvUpper() {
        return zdcInvUpper;
    }

    public void setZdcInvUpper(Long zdcInvUpper) {
        this.zdcInvUpper = zdcInvUpper;
    }

    public BigDecimal getZtzdcdkc1() {
        return ztzdcdkc1;
    }

    public void setZtzdcdkc1(BigDecimal ztzdcdkc1) {
        this.ztzdcdkc1 = ztzdcdkc1;
    }

    public BigDecimal getZtzdcdkc2() {
        return ztzdcdkc2;
    }

    public void setZtzdcdkc2(BigDecimal ztzdcdkc2) {
        this.ztzdcdkc2 = ztzdcdkc2;
    }

    public BigDecimal getZmengeWq7() {
        return zmengeWq7;
    }

    public void setZmengeWq7(BigDecimal zmengeWq7) {
        this.zmengeWq7 = zmengeWq7;
    }

    public BigDecimal getZmaxApply() {
        return zmaxApply;
    }

    public void setZmaxApply(BigDecimal zmaxApply) {
        this.zmaxApply = zmaxApply;
    }

    public BigDecimal getZinvUpper() {
        return zinvUpper;
    }

    public void setZinvUpper(BigDecimal zinvUpper) {
        this.zinvUpper = zinvUpper;
    }

    public BigDecimal getZmengeSug() {
        return zmengeSug;
    }

    public void setZmengeSug(BigDecimal zmengeSug) {
        this.zmengeSug = zmengeSug;
    }

    public Long getZdhdB() {
        return zdhdB;
    }

    public void setZdhdB(Long zdhdB) {
        this.zdhdB = zdhdB;
    }

    public Integer getZdcL() {
        return zdcL;
    }

    public void setZdcL(Integer zdcL) {
        this.zdcL = zdcL;
    }

    public String getZgoodsL() {
        return zgoodsL;
    }

    public void setZgoodsL(String zgoodsL) {
        this.zgoodsL = zgoodsL;
    }

    public Long getZsStock() {
        return zsStock;
    }

    public void setZsStock(Long zsStock) {
        this.zsStock = zsStock;
    }

    public Long getZraw() {
        return zraw;
    }

    public void setZraw(Long zraw) {
        this.zraw = zraw;
    }

    public String getZnoncxb() {
        return znoncxb;
    }

    public void setZnoncxb(String znoncxb) {
        this.znoncxb = znoncxb;
    }

    public BigDecimal getZjmStoreStock() {
        return zjmStoreStock;
    }

    public void setZjmStoreStock(BigDecimal zjmStoreStock) {
        this.zjmStoreStock = zjmStoreStock;
    }

    public BigDecimal getZdhd() {
        return zdhd;
    }

    public void setZdhd(BigDecimal zdhd) {
        this.zdhd = zdhd;
    }

    public String getZhtlxbs() {
        return zhtlxbs;
    }

    public void setZhtlxbs(String zhtlxbs) {
        this.zhtlxbs = zhtlxbs;
    }

    public String getReasonType() {
        return reasonType;
    }

    public void setReasonType(String reasonType) {
        this.reasonType = reasonType;
    }

    public String getZpyyy() {
        return zpyyy;
    }

    public void setZpyyy(String zpyyy) {
        this.zpyyy = zpyyy;
    }

    public BigDecimal getBrtwrJy() {
        return brtwrJy;
    }

    public void setBrtwrJy(BigDecimal brtwrJy) {
        this.brtwrJy = brtwrJy;
    }

    public String getZhtlxbsJy() {
        return zhtlxbsJy;
    }

    public void setZhtlxbsJy(String zhtlxbsJy) {
        this.zhtlxbsJy = zhtlxbsJy;
    }

    public BigDecimal getZncsqwq() {
        return zncsqwq;
    }

    public void setZncsqwq(BigDecimal zncsqwq) {
        this.zncsqwq = zncsqwq;
    }

    @Override
    public boolean equals(Object that) {
        if (this == that) {
            return true;
        }
        if (that == null) {
            return false;
        }
        if (getClass() != that.getClass()) {
            return false;
        }
        SapZmmt0288 other = (SapZmmt0288) that;
        return (this.getId() == null ? other.getId() == null : this.getId().equals(other.getId()))
            && (this.getMandt() == null ? other.getMandt() == null : this.getMandt().equals(other.getMandt()))
            && (this.getZguid() == null ? other.getZguid() == null : this.getZguid().equals(other.getZguid()))
            && (this.getWerks() == null ? other.getWerks() == null : this.getWerks().equals(other.getWerks()))
            && (this.getMatnr() == null ? other.getMatnr() == null : this.getMatnr().equals(other.getMatnr()))
            && (this.getEkorg() == null ? other.getEkorg() == null : this.getEkorg().equals(other.getEkorg()))
            && (this.getBsart() == null ? other.getBsart() == null : this.getBsart().equals(other.getBsart()))
            && (this.getBukrs() == null ? other.getBukrs() == null : this.getBukrs().equals(other.getBukrs()))
            && (this.getEkgrp() == null ? other.getEkgrp() == null : this.getEkgrp().equals(other.getEkgrp()))
            && (this.getName1Dc() == null ? other.getName1Dc() == null : this.getName1Dc().equals(other.getName1Dc()))
            && (this.getLifnrXd() == null ? other.getLifnrXd() == null : this.getLifnrXd().equals(other.getLifnrXd()))
            && (this.getName1Xd() == null ? other.getName1Xd() == null : this.getName1Xd().equals(other.getName1Xd()))
            && (this.getMengeXd() == null ? other.getMengeXd() == null : this.getMengeXd().equals(other.getMengeXd()))
            && (this.getMeinsXd() == null ? other.getMeinsXd() == null : this.getMeinsXd().equals(other.getMeinsXd()))
            && (this.getNetprXd() == null ? other.getNetprXd() == null : this.getNetprXd().equals(other.getNetprXd()))
            && (this.getBrtwrXd() == null ? other.getBrtwrXd() == null : this.getBrtwrXd().equals(other.getBrtwrXd()))
            && (this.getNetsumXd() == null ? other.getNetsumXd() == null : this.getNetsumXd().equals(other.getNetsumXd()))
            && (this.getBrtsumXd() == null ? other.getBrtsumXd() == null : this.getBrtsumXd().equals(other.getBrtsumXd()))
            && (this.getPeinhXd() == null ? other.getPeinhXd() == null : this.getPeinhXd().equals(other.getPeinhXd()))
            && (this.getTaxXd() == null ? other.getTaxXd() == null : this.getTaxXd().equals(other.getTaxXd()))
            && (this.getTaxXdT() == null ? other.getTaxXdT() == null : this.getTaxXdT().equals(other.getTaxXdT()))
            && (this.getKonnr() == null ? other.getKonnr() == null : this.getKonnr().equals(other.getKonnr()))
            && (this.getKtpnr() == null ? other.getKtpnr() == null : this.getKtpnr().equals(other.getKtpnr()))
            && (this.getZplcode() == null ? other.getZplcode() == null : this.getZplcode().equals(other.getZplcode()))
            && (this.getZplsug() == null ? other.getZplsug() == null : this.getZplsug().equals(other.getZplsug()))
            && (this.getZcjms() == null ? other.getZcjms() == null : this.getZcjms().equals(other.getZcjms()))
            && (this.getZterm() == null ? other.getZterm() == null : this.getZterm().equals(other.getZterm()))
            && (this.getZtermT() == null ? other.getZtermT() == null : this.getZtermT().equals(other.getZtermT()))
            && (this.getZcgbzsz() == null ? other.getZcgbzsz() == null : this.getZcgbzsz().equals(other.getZcgbzsz()))
            && (this.getMengeSug() == null ? other.getMengeSug() == null : this.getMengeSug().equals(other.getMengeSug()))
            && (this.getMeinsSug() == null ? other.getMeinsSug() == null : this.getMeinsSug().equals(other.getMeinsSug()))
            && (this.getMengeZbz() == null ? other.getMengeZbz() == null : this.getMengeZbz().equals(other.getMengeZbz()))
            && (this.getMengeXbz() == null ? other.getMengeXbz() == null : this.getMengeXbz().equals(other.getMengeXbz()))
            && (this.getMengeZz() == null ? other.getMengeZz() == null : this.getMengeZz().equals(other.getMengeZz()))
            && (this.getMeinsZz() == null ? other.getMeinsZz() == null : this.getMeinsZz().equals(other.getMeinsZz()))
            && (this.getZzbzl() == null ? other.getZzbzl() == null : this.getZzbzl().equals(other.getZzbzl()))
            && (this.getZzcqty() == null ? other.getZzcqty() == null : this.getZzcqty().equals(other.getZzcqty()))
            && (this.getZxsjwxs() == null ? other.getZxsjwxs() == null : this.getZxsjwxs().equals(other.getZxsjwxs()))
            && (this.getZxxjwxs() == null ? other.getZxxjwxs() == null : this.getZxxjwxs().equals(other.getZxxjwxs()))
            && (this.getKnttp() == null ? other.getKnttp() == null : this.getKnttp().equals(other.getKnttp()))
            && (this.getKostl() == null ? other.getKostl() == null : this.getKostl().equals(other.getKostl()))
            && (this.getZuserid() == null ? other.getZuserid() == null : this.getZuserid().equals(other.getZuserid()))
            && (this.getZywxm() == null ? other.getZywxm() == null : this.getZywxm().equals(other.getZywxm()))
            && (this.getZzhxs() == null ? other.getZzhxs() == null : this.getZzhxs().equals(other.getZzhxs()))
            && (this.getZbcdhl() == null ? other.getZbcdhl() == null : this.getZbcdhl().equals(other.getZbcdhl()))
            && (this.getZmdz() == null ? other.getZmdz() == null : this.getZmdz().equals(other.getZmdz()))
            && (this.getZmdzT() == null ? other.getZmdzT() == null : this.getZmdzT().equals(other.getZmdzT()))
            && (this.getZspjb() == null ? other.getZspjb() == null : this.getZspjb().equals(other.getZspjb()))
            && (this.getEkorg02() == null ? other.getEkorg02() == null : this.getEkorg02().equals(other.getEkorg02()))
            && (this.getZspckddlx() == null ? other.getZspckddlx() == null : this.getZspckddlx().equals(other.getZspckddlx()))
            && (this.getZghdc() == null ? other.getZghdc() == null : this.getZghdc().equals(other.getZghdc()))
            && (this.getZzcqtyBdp() == null ? other.getZzcqtyBdp() == null : this.getZzcqtyBdp().equals(other.getZzcqtyBdp()))
            && (this.getZzbzlBdp() == null ? other.getZzbzlBdp() == null : this.getZzbzlBdp().equals(other.getZzbzlBdp()))
            && (this.getZjhjh() == null ? other.getZjhjh() == null : this.getZjhjh().equals(other.getZjhjh()))
            && (this.getLgort() == null ? other.getLgort() == null : this.getLgort().equals(other.getLgort()))
            && (this.getZbzgg() == null ? other.getZbzgg() == null : this.getZbzgg().equals(other.getZbzgg()))
            && (this.getRemark() == null ? other.getRemark() == null : this.getRemark().equals(other.getRemark()))
            && (this.getZcrdate() == null ? other.getZcrdate() == null : this.getZcrdate().equals(other.getZcrdate()))
            && (this.getZcrtime() == null ? other.getZcrtime() == null : this.getZcrtime().equals(other.getZcrtime()))
            && (this.getZcrname() == null ? other.getZcrname() == null : this.getZcrname().equals(other.getZcrname()))
            && (this.getZchdate() == null ? other.getZchdate() == null : this.getZchdate().equals(other.getZchdate()))
            && (this.getZchtime() == null ? other.getZchtime() == null : this.getZchtime().equals(other.getZchtime()))
            && (this.getZchname() == null ? other.getZchname() == null : this.getZchname().equals(other.getZchname()))
            && (this.getEbeln() == null ? other.getEbeln() == null : this.getEbeln().equals(other.getEbeln()))
            && (this.getEbelp() == null ? other.getEbelp() == null : this.getEbelp().equals(other.getEbelp()))
            && (this.getPurreqno() == null ? other.getPurreqno() == null : this.getPurreqno().equals(other.getPurreqno()))
            && (this.getStorelineno() == null ? other.getStorelineno() == null : this.getStorelineno().equals(other.getStorelineno()))
            && (this.getZbyzd2() == null ? other.getZbyzd2() == null : this.getZbyzd2().equals(other.getZbyzd2()))
            && (this.getZstockUse() == null ? other.getZstockUse() == null : this.getZstockUse().equals(other.getZstockUse()))
            && (this.getLabstDcJs() == null ? other.getLabstDcJs() == null : this.getLabstDcJs().equals(other.getLabstDcJs()))
            && (this.getMengeWqJs() == null ? other.getMengeWqJs() == null : this.getMengeWqJs().equals(other.getMengeWqJs()))
            && (this.getZzxl30Js() == null ? other.getZzxl30Js() == null : this.getZzxl30Js().equals(other.getZzxl30Js()))
            && (this.getZmdqtyJs() == null ? other.getZmdqtyJs() == null : this.getZmdqtyJs().equals(other.getZmdqtyJs()))
            && (this.getZtsddbs() == null ? other.getZtsddbs() == null : this.getZtsddbs().equals(other.getZtsddbs()))
            && (this.getZtsddydh() == null ? other.getZtsddydh() == null : this.getZtsddydh().equals(other.getZtsddydh()))
            && (this.getZzqxdjh() == null ? other.getZzqxdjh() == null : this.getZzqxdjh().equals(other.getZzqxdjh()))
            && (this.getZzqxdhh() == null ? other.getZzqxdhh() == null : this.getZzqxdhh().equals(other.getZzqxdhh()))
            && (this.getZjtndjtj() == null ? other.getZjtndjtj() == null : this.getZjtndjtj().equals(other.getZjtndjtj()))
            && (this.getZcyjswq() == null ? other.getZcyjswq() == null : this.getZcyjswq().equals(other.getZcyjswq()))
            && (this.getZtcwq() == null ? other.getZtcwq() == null : this.getZtcwq().equals(other.getZtcwq()))
            && (this.getZdckykc() == null ? other.getZdckykc() == null : this.getZdckykc().equals(other.getZdckykc()))
            && (this.getMengeWq() == null ? other.getMengeWq() == null : this.getMengeWq().equals(other.getMengeWq()))
            && (this.getTzts() == null ? other.getTzts() == null : this.getTzts().equals(other.getTzts()))
            && (this.getTztsYxqz() == null ? other.getTztsYxqz() == null : this.getTztsYxqz().equals(other.getTztsYxqz()))
            && (this.getZgysqzq() == null ? other.getZgysqzq() == null : this.getZgysqzq().equals(other.getZgysqzq()))
            && (this.getZspqzq() == null ? other.getZspqzq() == null : this.getZspqzq().equals(other.getZspqzq()))
            && (this.getZspqzqqsrq() == null ? other.getZspqzqqsrq() == null : this.getZspqzqqsrq().equals(other.getZspqzqqsrq()))
            && (this.getZspqzqzzrq() == null ? other.getZspqzqzzrq() == null : this.getZspqzqzzrq().equals(other.getZspqzqzzrq()))
            && (this.getZbckcts() == null ? other.getZbckcts() == null : this.getZbckcts().equals(other.getZbckcts()))
            && (this.getZbckctsqsrq() == null ? other.getZbckctsqsrq() == null : this.getZbckctsqsrq().equals(other.getZbckctsqsrq()))
            && (this.getZbckctszzrq() == null ? other.getZbckctszzrq() == null : this.getZbckctszzrq().equals(other.getZbckctszzrq()))
            && (this.getZbcdhlqsrq() == null ? other.getZbcdhlqsrq() == null : this.getZbcdhlqsrq().equals(other.getZbcdhlqsrq()))
            && (this.getZbcdhlzzrq() == null ? other.getZbcdhlzzrq() == null : this.getZbcdhlzzrq().equals(other.getZbcdhlzzrq()))
            && (this.getZsfjs() == null ? other.getZsfjs() == null : this.getZsfjs().equals(other.getZsfjs()))
            && (this.getZxqhbs() == null ? other.getZxqhbs() == null : this.getZxqhbs().equals(other.getZxqhbs()))
            && (this.getZaqkcbcts() == null ? other.getZaqkcbcts() == null : this.getZaqkcbcts().equals(other.getZaqkcbcts()))
            && (this.getZsfqz() == null ? other.getZsfqz() == null : this.getZsfqz().equals(other.getZsfqz()))
            && (this.getZtsxsqsrq() == null ? other.getZtsxsqsrq() == null : this.getZtsxsqsrq().equals(other.getZtsxsqsrq()))
            && (this.getZtsxsjsrq() == null ? other.getZtsxsjsrq() == null : this.getZtsxsjsrq().equals(other.getZtsxsjsrq()))
            && (this.getZtsxsqzzb() == null ? other.getZtsxsqzzb() == null : this.getZtsxsqzzb().equals(other.getZtsxsqzzb()))
            && (this.getZkcsx() == null ? other.getZkcsx() == null : this.getZkcsx().equals(other.getZkcsx()))
            && (this.getZkcxx() == null ? other.getZkcxx() == null : this.getZkcxx().equals(other.getZkcxx()))
            && (this.getZcgtpjl() == null ? other.getZcgtpjl() == null : this.getZcgtpjl().equals(other.getZcgtpjl()))
            && (this.getZcgtpjlxm() == null ? other.getZcgtpjlxm() == null : this.getZcgtpjlxm().equals(other.getZcgtpjlxm()))
            && (this.getZyjywxdts() == null ? other.getZyjywxdts() == null : this.getZyjywxdts().equals(other.getZyjywxdts()))
            && (this.getBrtwrLast() == null ? other.getBrtwrLast() == null : this.getBrtwrLast().equals(other.getBrtwrLast()))
            && (this.getPeinhLast() == null ? other.getPeinhLast() == null : this.getPeinhLast().equals(other.getPeinhLast()))
            && (this.getZtzdckc() == null ? other.getZtzdckc() == null : this.getZtzdckc().equals(other.getZtzdckc()))
            && (this.getZstockDc() == null ? other.getZstockDc() == null : this.getZstockDc().equals(other.getZstockDc()))
            && (this.getZstockStore() == null ? other.getZstockStore() == null : this.getZstockStore().equals(other.getZstockStore()))
            && (this.getZzcInactiveDcStock() == null ? other.getZzcInactiveDcStock() == null : this.getZzcInactiveDcStock().equals(other.getZzcInactiveDcStock()))
            && (this.getZstockTotal() == null ? other.getZstockTotal() == null : this.getZstockTotal().equals(other.getZstockTotal()))
            && (this.getZfixTbs() == null ? other.getZfixTbs() == null : this.getZfixTbs().equals(other.getZfixTbs()))
            && (this.getZsumRV() == null ? other.getZsumRV() == null : this.getZsumRV().equals(other.getZsumRV()))
            && (this.getZdcInvUpper() == null ? other.getZdcInvUpper() == null : this.getZdcInvUpper().equals(other.getZdcInvUpper()))
            && (this.getZtzdcdkc1() == null ? other.getZtzdcdkc1() == null : this.getZtzdcdkc1().equals(other.getZtzdcdkc1()))
            && (this.getZtzdcdkc2() == null ? other.getZtzdcdkc2() == null : this.getZtzdcdkc2().equals(other.getZtzdcdkc2()))
            && (this.getZmengeWq7() == null ? other.getZmengeWq7() == null : this.getZmengeWq7().equals(other.getZmengeWq7()))
            && (this.getZmaxApply() == null ? other.getZmaxApply() == null : this.getZmaxApply().equals(other.getZmaxApply()))
            && (this.getZinvUpper() == null ? other.getZinvUpper() == null : this.getZinvUpper().equals(other.getZinvUpper()))
            && (this.getZmengeSug() == null ? other.getZmengeSug() == null : this.getZmengeSug().equals(other.getZmengeSug()))
            && (this.getZdhdB() == null ? other.getZdhdB() == null : this.getZdhdB().equals(other.getZdhdB()))
            && (this.getZdcL() == null ? other.getZdcL() == null : this.getZdcL().equals(other.getZdcL()))
            && (this.getZgoodsL() == null ? other.getZgoodsL() == null : this.getZgoodsL().equals(other.getZgoodsL()))
            && (this.getZsStock() == null ? other.getZsStock() == null : this.getZsStock().equals(other.getZsStock()))
            && (this.getZraw() == null ? other.getZraw() == null : this.getZraw().equals(other.getZraw()))
            && (this.getZnoncxb() == null ? other.getZnoncxb() == null : this.getZnoncxb().equals(other.getZnoncxb()))
            && (this.getZjmStoreStock() == null ? other.getZjmStoreStock() == null : this.getZjmStoreStock().equals(other.getZjmStoreStock()))
            && (this.getZdhd() == null ? other.getZdhd() == null : this.getZdhd().equals(other.getZdhd()))
            && (this.getZhtlxbs() == null ? other.getZhtlxbs() == null : this.getZhtlxbs().equals(other.getZhtlxbs()))
            && (this.getReasonType() == null ? other.getReasonType() == null : this.getReasonType().equals(other.getReasonType()))
            && (this.getZpyyy() == null ? other.getZpyyy() == null : this.getZpyyy().equals(other.getZpyyy()))
            && (this.getBrtwrJy() == null ? other.getBrtwrJy() == null : this.getBrtwrJy().equals(other.getBrtwrJy()))
            && (this.getZhtlxbsJy() == null ? other.getZhtlxbsJy() == null : this.getZhtlxbsJy().equals(other.getZhtlxbsJy()))
            && (this.getZncsqwq() == null ? other.getZncsqwq() == null : this.getZncsqwq().equals(other.getZncsqwq()));
    }

    @Override
    public int hashCode() {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((getId() == null) ? 0 : getId().hashCode());
        result = prime * result + ((getMandt() == null) ? 0 : getMandt().hashCode());
        result = prime * result + ((getZguid() == null) ? 0 : getZguid().hashCode());
        result = prime * result + ((getWerks() == null) ? 0 : getWerks().hashCode());
        result = prime * result + ((getMatnr() == null) ? 0 : getMatnr().hashCode());
        result = prime * result + ((getEkorg() == null) ? 0 : getEkorg().hashCode());
        result = prime * result + ((getBsart() == null) ? 0 : getBsart().hashCode());
        result = prime * result + ((getBukrs() == null) ? 0 : getBukrs().hashCode());
        result = prime * result + ((getEkgrp() == null) ? 0 : getEkgrp().hashCode());
        result = prime * result + ((getName1Dc() == null) ? 0 : getName1Dc().hashCode());
        result = prime * result + ((getLifnrXd() == null) ? 0 : getLifnrXd().hashCode());
        result = prime * result + ((getName1Xd() == null) ? 0 : getName1Xd().hashCode());
        result = prime * result + ((getMengeXd() == null) ? 0 : getMengeXd().hashCode());
        result = prime * result + ((getMeinsXd() == null) ? 0 : getMeinsXd().hashCode());
        result = prime * result + ((getNetprXd() == null) ? 0 : getNetprXd().hashCode());
        result = prime * result + ((getBrtwrXd() == null) ? 0 : getBrtwrXd().hashCode());
        result = prime * result + ((getNetsumXd() == null) ? 0 : getNetsumXd().hashCode());
        result = prime * result + ((getBrtsumXd() == null) ? 0 : getBrtsumXd().hashCode());
        result = prime * result + ((getPeinhXd() == null) ? 0 : getPeinhXd().hashCode());
        result = prime * result + ((getTaxXd() == null) ? 0 : getTaxXd().hashCode());
        result = prime * result + ((getTaxXdT() == null) ? 0 : getTaxXdT().hashCode());
        result = prime * result + ((getKonnr() == null) ? 0 : getKonnr().hashCode());
        result = prime * result + ((getKtpnr() == null) ? 0 : getKtpnr().hashCode());
        result = prime * result + ((getZplcode() == null) ? 0 : getZplcode().hashCode());
        result = prime * result + ((getZplsug() == null) ? 0 : getZplsug().hashCode());
        result = prime * result + ((getZcjms() == null) ? 0 : getZcjms().hashCode());
        result = prime * result + ((getZterm() == null) ? 0 : getZterm().hashCode());
        result = prime * result + ((getZtermT() == null) ? 0 : getZtermT().hashCode());
        result = prime * result + ((getZcgbzsz() == null) ? 0 : getZcgbzsz().hashCode());
        result = prime * result + ((getMengeSug() == null) ? 0 : getMengeSug().hashCode());
        result = prime * result + ((getMeinsSug() == null) ? 0 : getMeinsSug().hashCode());
        result = prime * result + ((getMengeZbz() == null) ? 0 : getMengeZbz().hashCode());
        result = prime * result + ((getMengeXbz() == null) ? 0 : getMengeXbz().hashCode());
        result = prime * result + ((getMengeZz() == null) ? 0 : getMengeZz().hashCode());
        result = prime * result + ((getMeinsZz() == null) ? 0 : getMeinsZz().hashCode());
        result = prime * result + ((getZzbzl() == null) ? 0 : getZzbzl().hashCode());
        result = prime * result + ((getZzcqty() == null) ? 0 : getZzcqty().hashCode());
        result = prime * result + ((getZxsjwxs() == null) ? 0 : getZxsjwxs().hashCode());
        result = prime * result + ((getZxxjwxs() == null) ? 0 : getZxxjwxs().hashCode());
        result = prime * result + ((getKnttp() == null) ? 0 : getKnttp().hashCode());
        result = prime * result + ((getKostl() == null) ? 0 : getKostl().hashCode());
        result = prime * result + ((getZuserid() == null) ? 0 : getZuserid().hashCode());
        result = prime * result + ((getZywxm() == null) ? 0 : getZywxm().hashCode());
        result = prime * result + ((getZzhxs() == null) ? 0 : getZzhxs().hashCode());
        result = prime * result + ((getZbcdhl() == null) ? 0 : getZbcdhl().hashCode());
        result = prime * result + ((getZmdz() == null) ? 0 : getZmdz().hashCode());
        result = prime * result + ((getZmdzT() == null) ? 0 : getZmdzT().hashCode());
        result = prime * result + ((getZspjb() == null) ? 0 : getZspjb().hashCode());
        result = prime * result + ((getEkorg02() == null) ? 0 : getEkorg02().hashCode());
        result = prime * result + ((getZspckddlx() == null) ? 0 : getZspckddlx().hashCode());
        result = prime * result + ((getZghdc() == null) ? 0 : getZghdc().hashCode());
        result = prime * result + ((getZzcqtyBdp() == null) ? 0 : getZzcqtyBdp().hashCode());
        result = prime * result + ((getZzbzlBdp() == null) ? 0 : getZzbzlBdp().hashCode());
        result = prime * result + ((getZjhjh() == null) ? 0 : getZjhjh().hashCode());
        result = prime * result + ((getLgort() == null) ? 0 : getLgort().hashCode());
        result = prime * result + ((getZbzgg() == null) ? 0 : getZbzgg().hashCode());
        result = prime * result + ((getRemark() == null) ? 0 : getRemark().hashCode());
        result = prime * result + ((getZcrdate() == null) ? 0 : getZcrdate().hashCode());
        result = prime * result + ((getZcrtime() == null) ? 0 : getZcrtime().hashCode());
        result = prime * result + ((getZcrname() == null) ? 0 : getZcrname().hashCode());
        result = prime * result + ((getZchdate() == null) ? 0 : getZchdate().hashCode());
        result = prime * result + ((getZchtime() == null) ? 0 : getZchtime().hashCode());
        result = prime * result + ((getZchname() == null) ? 0 : getZchname().hashCode());
        result = prime * result + ((getEbeln() == null) ? 0 : getEbeln().hashCode());
        result = prime * result + ((getEbelp() == null) ? 0 : getEbelp().hashCode());
        result = prime * result + ((getPurreqno() == null) ? 0 : getPurreqno().hashCode());
        result = prime * result + ((getStorelineno() == null) ? 0 : getStorelineno().hashCode());
        result = prime * result + ((getZbyzd2() == null) ? 0 : getZbyzd2().hashCode());
        result = prime * result + ((getZstockUse() == null) ? 0 : getZstockUse().hashCode());
        result = prime * result + ((getLabstDcJs() == null) ? 0 : getLabstDcJs().hashCode());
        result = prime * result + ((getMengeWqJs() == null) ? 0 : getMengeWqJs().hashCode());
        result = prime * result + ((getZzxl30Js() == null) ? 0 : getZzxl30Js().hashCode());
        result = prime * result + ((getZmdqtyJs() == null) ? 0 : getZmdqtyJs().hashCode());
        result = prime * result + ((getZtsddbs() == null) ? 0 : getZtsddbs().hashCode());
        result = prime * result + ((getZtsddydh() == null) ? 0 : getZtsddydh().hashCode());
        result = prime * result + ((getZzqxdjh() == null) ? 0 : getZzqxdjh().hashCode());
        result = prime * result + ((getZzqxdhh() == null) ? 0 : getZzqxdhh().hashCode());
        result = prime * result + ((getZjtndjtj() == null) ? 0 : getZjtndjtj().hashCode());
        result = prime * result + ((getZcyjswq() == null) ? 0 : getZcyjswq().hashCode());
        result = prime * result + ((getZtcwq() == null) ? 0 : getZtcwq().hashCode());
        result = prime * result + ((getZdckykc() == null) ? 0 : getZdckykc().hashCode());
        result = prime * result + ((getMengeWq() == null) ? 0 : getMengeWq().hashCode());
        result = prime * result + ((getTzts() == null) ? 0 : getTzts().hashCode());
        result = prime * result + ((getTztsYxqz() == null) ? 0 : getTztsYxqz().hashCode());
        result = prime * result + ((getZgysqzq() == null) ? 0 : getZgysqzq().hashCode());
        result = prime * result + ((getZspqzq() == null) ? 0 : getZspqzq().hashCode());
        result = prime * result + ((getZspqzqqsrq() == null) ? 0 : getZspqzqqsrq().hashCode());
        result = prime * result + ((getZspqzqzzrq() == null) ? 0 : getZspqzqzzrq().hashCode());
        result = prime * result + ((getZbckcts() == null) ? 0 : getZbckcts().hashCode());
        result = prime * result + ((getZbckctsqsrq() == null) ? 0 : getZbckctsqsrq().hashCode());
        result = prime * result + ((getZbckctszzrq() == null) ? 0 : getZbckctszzrq().hashCode());
        result = prime * result + ((getZbcdhlqsrq() == null) ? 0 : getZbcdhlqsrq().hashCode());
        result = prime * result + ((getZbcdhlzzrq() == null) ? 0 : getZbcdhlzzrq().hashCode());
        result = prime * result + ((getZsfjs() == null) ? 0 : getZsfjs().hashCode());
        result = prime * result + ((getZxqhbs() == null) ? 0 : getZxqhbs().hashCode());
        result = prime * result + ((getZaqkcbcts() == null) ? 0 : getZaqkcbcts().hashCode());
        result = prime * result + ((getZsfqz() == null) ? 0 : getZsfqz().hashCode());
        result = prime * result + ((getZtsxsqsrq() == null) ? 0 : getZtsxsqsrq().hashCode());
        result = prime * result + ((getZtsxsjsrq() == null) ? 0 : getZtsxsjsrq().hashCode());
        result = prime * result + ((getZtsxsqzzb() == null) ? 0 : getZtsxsqzzb().hashCode());
        result = prime * result + ((getZkcsx() == null) ? 0 : getZkcsx().hashCode());
        result = prime * result + ((getZkcxx() == null) ? 0 : getZkcxx().hashCode());
        result = prime * result + ((getZcgtpjl() == null) ? 0 : getZcgtpjl().hashCode());
        result = prime * result + ((getZcgtpjlxm() == null) ? 0 : getZcgtpjlxm().hashCode());
        result = prime * result + ((getZyjywxdts() == null) ? 0 : getZyjywxdts().hashCode());
        result = prime * result + ((getBrtwrLast() == null) ? 0 : getBrtwrLast().hashCode());
        result = prime * result + ((getPeinhLast() == null) ? 0 : getPeinhLast().hashCode());
        result = prime * result + ((getZtzdckc() == null) ? 0 : getZtzdckc().hashCode());
        result = prime * result + ((getZstockDc() == null) ? 0 : getZstockDc().hashCode());
        result = prime * result + ((getZstockStore() == null) ? 0 : getZstockStore().hashCode());
        result = prime * result + ((getZzcInactiveDcStock() == null) ? 0 : getZzcInactiveDcStock().hashCode());
        result = prime * result + ((getZstockTotal() == null) ? 0 : getZstockTotal().hashCode());
        result = prime * result + ((getZfixTbs() == null) ? 0 : getZfixTbs().hashCode());
        result = prime * result + ((getZsumRV() == null) ? 0 : getZsumRV().hashCode());
        result = prime * result + ((getZdcInvUpper() == null) ? 0 : getZdcInvUpper().hashCode());
        result = prime * result + ((getZtzdcdkc1() == null) ? 0 : getZtzdcdkc1().hashCode());
        result = prime * result + ((getZtzdcdkc2() == null) ? 0 : getZtzdcdkc2().hashCode());
        result = prime * result + ((getZmengeWq7() == null) ? 0 : getZmengeWq7().hashCode());
        result = prime * result + ((getZmaxApply() == null) ? 0 : getZmaxApply().hashCode());
        result = prime * result + ((getZinvUpper() == null) ? 0 : getZinvUpper().hashCode());
        result = prime * result + ((getZmengeSug() == null) ? 0 : getZmengeSug().hashCode());
        result = prime * result + ((getZdhdB() == null) ? 0 : getZdhdB().hashCode());
        result = prime * result + ((getZdcL() == null) ? 0 : getZdcL().hashCode());
        result = prime * result + ((getZgoodsL() == null) ? 0 : getZgoodsL().hashCode());
        result = prime * result + ((getZsStock() == null) ? 0 : getZsStock().hashCode());
        result = prime * result + ((getZraw() == null) ? 0 : getZraw().hashCode());
        result = prime * result + ((getZnoncxb() == null) ? 0 : getZnoncxb().hashCode());
        result = prime * result + ((getZjmStoreStock() == null) ? 0 : getZjmStoreStock().hashCode());
        result = prime * result + ((getZdhd() == null) ? 0 : getZdhd().hashCode());
        result = prime * result + ((getZhtlxbs() == null) ? 0 : getZhtlxbs().hashCode());
        result = prime * result + ((getReasonType() == null) ? 0 : getReasonType().hashCode());
        result = prime * result + ((getZpyyy() == null) ? 0 : getZpyyy().hashCode());
        result = prime * result + ((getBrtwrJy() == null) ? 0 : getBrtwrJy().hashCode());
        result = prime * result + ((getZhtlxbsJy() == null) ? 0 : getZhtlxbsJy().hashCode());
        result = prime * result + ((getZncsqwq() == null) ? 0 : getZncsqwq().hashCode());
        return result;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", mandt=").append(mandt);
        sb.append(", zguid=").append(zguid);
        sb.append(", werks=").append(werks);
        sb.append(", matnr=").append(matnr);
        sb.append(", ekorg=").append(ekorg);
        sb.append(", bsart=").append(bsart);
        sb.append(", bukrs=").append(bukrs);
        sb.append(", ekgrp=").append(ekgrp);
        sb.append(", name1Dc=").append(name1Dc);
        sb.append(", lifnrXd=").append(lifnrXd);
        sb.append(", name1Xd=").append(name1Xd);
        sb.append(", mengeXd=").append(mengeXd);
        sb.append(", meinsXd=").append(meinsXd);
        sb.append(", netprXd=").append(netprXd);
        sb.append(", brtwrXd=").append(brtwrXd);
        sb.append(", netsumXd=").append(netsumXd);
        sb.append(", brtsumXd=").append(brtsumXd);
        sb.append(", peinhXd=").append(peinhXd);
        sb.append(", taxXd=").append(taxXd);
        sb.append(", taxXdT=").append(taxXdT);
        sb.append(", konnr=").append(konnr);
        sb.append(", ktpnr=").append(ktpnr);
        sb.append(", zplcode=").append(zplcode);
        sb.append(", zplsug=").append(zplsug);
        sb.append(", zcjms=").append(zcjms);
        sb.append(", zterm=").append(zterm);
        sb.append(", ztermT=").append(ztermT);
        sb.append(", zcgbzsz=").append(zcgbzsz);
        sb.append(", mengeSug=").append(mengeSug);
        sb.append(", meinsSug=").append(meinsSug);
        sb.append(", mengeZbz=").append(mengeZbz);
        sb.append(", mengeXbz=").append(mengeXbz);
        sb.append(", mengeZz=").append(mengeZz);
        sb.append(", meinsZz=").append(meinsZz);
        sb.append(", zzbzl=").append(zzbzl);
        sb.append(", zzcqty=").append(zzcqty);
        sb.append(", zxsjwxs=").append(zxsjwxs);
        sb.append(", zxxjwxs=").append(zxxjwxs);
        sb.append(", knttp=").append(knttp);
        sb.append(", kostl=").append(kostl);
        sb.append(", zuserid=").append(zuserid);
        sb.append(", zywxm=").append(zywxm);
        sb.append(", zzhxs=").append(zzhxs);
        sb.append(", zbcdhl=").append(zbcdhl);
        sb.append(", zmdz=").append(zmdz);
        sb.append(", zmdzT=").append(zmdzT);
        sb.append(", zspjb=").append(zspjb);
        sb.append(", ekorg02=").append(ekorg02);
        sb.append(", zspckddlx=").append(zspckddlx);
        sb.append(", zghdc=").append(zghdc);
        sb.append(", zzcqtyBdp=").append(zzcqtyBdp);
        sb.append(", zzbzlBdp=").append(zzbzlBdp);
        sb.append(", zjhjh=").append(zjhjh);
        sb.append(", lgort=").append(lgort);
        sb.append(", zbzgg=").append(zbzgg);
        sb.append(", remark=").append(remark);
        sb.append(", zcrdate=").append(zcrdate);
        sb.append(", zcrtime=").append(zcrtime);
        sb.append(", zcrname=").append(zcrname);
        sb.append(", zchdate=").append(zchdate);
        sb.append(", zchtime=").append(zchtime);
        sb.append(", zchname=").append(zchname);
        sb.append(", ebeln=").append(ebeln);
        sb.append(", ebelp=").append(ebelp);
        sb.append(", purreqno=").append(purreqno);
        sb.append(", storelineno=").append(storelineno);
        sb.append(", zbyzd2=").append(zbyzd2);
        sb.append(", zstockUse=").append(zstockUse);
        sb.append(", labstDcJs=").append(labstDcJs);
        sb.append(", mengeWqJs=").append(mengeWqJs);
        sb.append(", zzxl30Js=").append(zzxl30Js);
        sb.append(", zmdqtyJs=").append(zmdqtyJs);
        sb.append(", ztsddbs=").append(ztsddbs);
        sb.append(", ztsddydh=").append(ztsddydh);
        sb.append(", zzqxdjh=").append(zzqxdjh);
        sb.append(", zzqxdhh=").append(zzqxdhh);
        sb.append(", zjtndjtj=").append(zjtndjtj);
        sb.append(", zcyjswq=").append(zcyjswq);
        sb.append(", ztcwq=").append(ztcwq);
        sb.append(", zdckykc=").append(zdckykc);
        sb.append(", mengeWq=").append(mengeWq);
        sb.append(", tzts=").append(tzts);
        sb.append(", tztsYxqz=").append(tztsYxqz);
        sb.append(", zgysqzq=").append(zgysqzq);
        sb.append(", zspqzq=").append(zspqzq);
        sb.append(", zspqzqqsrq=").append(zspqzqqsrq);
        sb.append(", zspqzqzzrq=").append(zspqzqzzrq);
        sb.append(", zbckcts=").append(zbckcts);
        sb.append(", zbckctsqsrq=").append(zbckctsqsrq);
        sb.append(", zbckctszzrq=").append(zbckctszzrq);
        sb.append(", zbcdhlqsrq=").append(zbcdhlqsrq);
        sb.append(", zbcdhlzzrq=").append(zbcdhlzzrq);
        sb.append(", zsfjs=").append(zsfjs);
        sb.append(", zxqhbs=").append(zxqhbs);
        sb.append(", zaqkcbcts=").append(zaqkcbcts);
        sb.append(", zsfqz=").append(zsfqz);
        sb.append(", ztsxsqsrq=").append(ztsxsqsrq);
        sb.append(", ztsxsjsrq=").append(ztsxsjsrq);
        sb.append(", ztsxsqzzb=").append(ztsxsqzzb);
        sb.append(", zkcsx=").append(zkcsx);
        sb.append(", zkcxx=").append(zkcxx);
        sb.append(", zcgtpjl=").append(zcgtpjl);
        sb.append(", zcgtpjlxm=").append(zcgtpjlxm);
        sb.append(", zyjywxdts=").append(zyjywxdts);
        sb.append(", brtwrLast=").append(brtwrLast);
        sb.append(", peinhLast=").append(peinhLast);
        sb.append(", ztzdckc=").append(ztzdckc);
        sb.append(", zstockDc=").append(zstockDc);
        sb.append(", zstockStore=").append(zstockStore);
        sb.append(", zzcInactiveDcStock=").append(zzcInactiveDcStock);
        sb.append(", zstockTotal=").append(zstockTotal);
        sb.append(", zfixTbs=").append(zfixTbs);
        sb.append(", zsumRV=").append(zsumRV);
        sb.append(", zdcInvUpper=").append(zdcInvUpper);
        sb.append(", ztzdcdkc1=").append(ztzdcdkc1);
        sb.append(", ztzdcdkc2=").append(ztzdcdkc2);
        sb.append(", zmengeWq7=").append(zmengeWq7);
        sb.append(", zmaxApply=").append(zmaxApply);
        sb.append(", zinvUpper=").append(zinvUpper);
        sb.append(", zmengeSug=").append(zmengeSug);
        sb.append(", zdhdB=").append(zdhdB);
        sb.append(", zdcL=").append(zdcL);
        sb.append(", zgoodsL=").append(zgoodsL);
        sb.append(", zsStock=").append(zsStock);
        sb.append(", zraw=").append(zraw);
        sb.append(", znoncxb=").append(znoncxb);
        sb.append(", zjmStoreStock=").append(zjmStoreStock);
        sb.append(", zdhd=").append(zdhd);
        sb.append(", zhtlxbs=").append(zhtlxbs);
        sb.append(", reasonType=").append(reasonType);
        sb.append(", zpyyy=").append(zpyyy);
        sb.append(", brtwrJy=").append(brtwrJy);
        sb.append(", zhtlxbsJy=").append(zhtlxbsJy);
        sb.append(", zncsqwq=").append(zncsqwq);
        sb.append(", serialVersionUID=").append(serialVersionUID);
        sb.append("]");
        return sb.toString();
    }
}