package com.tomas.mybaties3.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p>
 * 投票记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VoteRecordCar implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户Id 商户订单号
     */

    private String orderNo;


    /**
     * 用户Id 商户订单号
     */

    private String errorMsg;



}
