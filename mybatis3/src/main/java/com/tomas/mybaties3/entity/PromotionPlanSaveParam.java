package com.tomas.mybaties3.entity;

import lombok.Data;

import java.util.List;

@Data
public class PromotionPlanSaveParam {
    /**
     * 方案id
     */
    private Long promotionPlanId;
    /**
     * 方案编码
     */
    private String promotionPlanCode;
    /**
     * 右上角选择的连锁id
     */
    private Long businessId;
    /**
     * 方案主题
     */
    private Integer planSubject;
    /**
     * 渠道 1 移动pos、2 云pos、3 海典pos、4 英克pos、5 线上o2o
     */
    private List<String> channels;
    /**
     * 参与人员类型 1 不限制 2 会员 3 非会员
     */
    private Integer participantType;
    /**
     * 方案描述
     */
    private String promotionPlanDesc;
    /**
     * 参与计算范围类型 1 全部 2 指定
     */
    private Integer calcStoreType;
    /**
     * 参与门店类型 1 全部 2 指定
     */
    private Integer chooseStoreType;
    /**
     * 剔除DTP商品 1 是 0否
     */
    private Integer excludeDTP;
    /**
     * 剔除商品
     */
    private List<String> excludeGoods;
    /**
     * 剔除分类集合 大中小子
     */
    private List<String> excludeCategories;
    /**
     * 剔除经营属性
     */
    private List<String> excludeManageAttr;
    /**
     * 毛利占比
     */
    private String grossProfitRate;
    /**
     * 销售额占比
     */
    private String saleroomRate;
    /**
     * 销售数量占比
     */
    private String salesVolume;
    /**
     * 大类配置
     */
    /**
     * 中类合并
     */
    private List<CateMerge> middleCategoriesMerge;
    /**
     * 品类升级
     */
    private List<CateMerge> subCategoriesMerge;
    /**
     * 促销选品数
     */
    private Integer promotionCount;
    /**
     * 方案名称
     */
    private String promotionPlanName;
    /**
     * 活动开始时间
     */
    private String promotionPlanStartTime;
    /**
     * 活动结束时间
     */
    private String promotionPlanEndTime;

    private Integer version;

    private List<String> chooseOrgIds;
    private List<String> calcOrgIds;
    private List<String> excludeOrgIds;

    private String calcBusinessName;

    /**
     * 存在促销单
     */
    private Boolean havePromotion;
    /**
     * 選品計算狀態
     */
    private Integer selectionStatus;
    /**
     * 驳回原因
     */
    private String reason;
    /**
     * 原始复制单号
     */
    private String originPlanCode;
    /**
     *
     * 是否参与优惠券 1：指定参与 0全部不参与
     */
    private Integer couponType;
    /**
     * 优惠券参与类型 1 全部参与 2部分不参与 3部分参与
     */
    private Integer couponParticipateType;

    /**
     * 延迟制单原因
     */
    private String delayReason;

    @Data
    public static class CateMerge{
        private List<String> codes;
        private String name;
    }
    @Data
    public static class StoreTree{
        private String businessCode;
        private Long businessId;
        private String businessName;
        private List<StoreInfo> stores;
    }
    @Data
    public static class StoreInfo{
        private String storeCode;
        private Long storeId;
    }

}
