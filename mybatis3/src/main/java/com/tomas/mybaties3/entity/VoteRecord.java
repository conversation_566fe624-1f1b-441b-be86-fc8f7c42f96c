package com.tomas.mybaties3.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 投票记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2019-10-28
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class VoteRecord implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;

    /**
     * 用户Id
     */
    private String userId;

    /**
     * 投票数
     */
    private Integer voteNum;


    /**
     * version
     */
    private Integer version;

    /**
     * 状态 1-正常 2-已删除
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Date createTime;


}
