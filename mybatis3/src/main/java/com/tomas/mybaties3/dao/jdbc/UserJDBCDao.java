package com.tomas.mybaties3.dao.jdbc;

import com.tomas.mybaties3.entity.VoteRecord;
import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;

import java.sql.*;

/**
 * UserJDBCDao 描述
 *
 * <AUTHOR>
 * @create 2021/1/11
 **/
@Repository("userJDBCDao")
@Data
public class UserJDBCDao {

    @Value("${spring.datasource.url}")
    protected String url;
    @Value("${spring.datasource.username}")
    protected  String username;
    @Value("${spring.datasource.password}")
    protected  String password;
    @Value("${spring.datasource.driver-class-name}")
    protected  String driver;

    protected Connection connection;





    public VoteRecord selectByUserId(String uid) throws ClassNotFoundException, SQLException {

        //jdbc执行 SQL 流程
        //1.注册 JDBC 驱动
        Class.forName(this.driver);
        //2.创建连接
        if (null==connection){
            connection = DriverManager.getConnection(url, username, password);
        }
        //3.获取执行对象
        PreparedStatement preparedStatement = connection.prepareStatement("select *  from vote where   id = ?");
        preparedStatement.setString(1, uid);
        //4.执行查询返回 resultSet
        ResultSet resultSet = preparedStatement.executeQuery();
        VoteRecord u=new VoteRecord();
        while (resultSet.next()){
           //System.out.println(JSON.toJSONString(resultSet));
            u.setId(resultSet.getLong("id"));
            u.setUserId( resultSet.getString("user_id"));
            u.setVoteNum(resultSet.getInt("vote_num"));
            u.setCreateTime(resultSet.getDate("create_time"));
        }
        return u;
    }
}
