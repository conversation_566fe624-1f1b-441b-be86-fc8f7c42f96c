package com.tomas.mybaties3.dao;

import com.tomas.mybaties3.entity.TransitGoodsInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface TransitGoodsInfoMapper {
    int insert(TransitGoodsInfo record);

    int insertSelective(TransitGoodsInfo record);

    int insertOrUpdate(TransitGoodsInfo  record);

    int insertOrUpdateList(List<TransitGoodsInfo> list);

    List<TransitGoodsInfo> queryTransitInfoByGoodsNos(@Param("businessId") Long businessId,@Param("storeId") Long storeId,@Param("goodsList") List<String> goodsList);

    int updateBatch(List<TransitGoodsInfo> List);

    int updateTransitQuantity(TransitGoodsInfo record);
}
