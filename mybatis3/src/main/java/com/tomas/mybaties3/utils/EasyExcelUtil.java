package com.tomas.mybaties3.utils;


import cn.hutool.core.io.FileUtil;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * EasyExcelUtil 描述
 *
 * <AUTHOR>
 * @create 5/12/23
 **/
public class EasyExcelUtil {
    public static String path="/Users/<USER>/Documents/code/github/SpringBoot-Learning/mybatis3/src/main/java/com/tomas/mybaties3/utils/" ;
    public static void main(String[] args) {
        String fileContent = "";
        try (BufferedReader reader = new BufferedReader(new FileReader(path+"01.txt"))) {
            StringBuilder sb = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line).append("\n");
            }
            fileContent= sb.toString();
            System.out.println(fileContent);
        } catch (IOException e) {
            e.printStackTrace();
        }

       // String al=

        List<Map<String, String>> dataList = new ArrayList<>();

        JSONArray jsonArray = JSON.parseArray(fileContent);
        for (Object obj : jsonArray) {
            if (obj instanceof JSONObject) {
                JSONObject jsonObject = (JSONObject) obj;
                JSONObject source = jsonObject.getJSONObject("_source");
                Map<String, String> data = new HashMap<>();
                for (String key : source.keySet()) {
                    data.put(key, source.getString(key));
                }
                dataList.add(data);
            }
        }
        String file = path+"bigWriteBeanTest1.xlsx";
        FileUtil.del(file);
        BigExcelWriter writer = ExcelUtil.getBigWriter(file);
        // 创建Excel工作簿
        //ExcelWriter writer = ExcelUtil.getWriter();
        // 添加表头

        writer.addHeaderAlias("goodsNo", "海典单号");
        writer.addHeaderAlias("priceTypeCode", "销售额");
        writer.addHeaderAlias("price", "成本");
        writer.addHeaderAlias("storeId", "商品数量");
        // 写入数据
        writer.write(dataList);
        // 设置自动列宽
        writer.autoSizeColumnAll();
        // 将Excel写入文件
        writer.flush();


    }


}
