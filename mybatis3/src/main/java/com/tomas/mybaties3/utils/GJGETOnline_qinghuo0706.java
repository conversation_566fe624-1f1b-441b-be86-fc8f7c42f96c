package com.tomas.mybaties3.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.tomas.mybaties3.utils.vo.MaintenanceLevelEnum;
import com.tomas.mybaties3.utils.vo.NewSpuVo;
import com.tomas.mybaties3.utils.vo.StorageConditionEnum;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * Test 描述
 *
 * <AUTHOR>
 * @create 2021/7/28
 **/
public class GJGETOnline_qinghuo0706 {
    public static void main(String[] args) throws IOException, InterruptedException {



        List<String> args1 = new ArrayList<String>();
        args1.add(1+"");
        args1.add(2+"");
        args1.add(3+"");
        args1.add(4+"");

        System.out.println(args1.subList(0,2));
        System.out.println(args1.subList(2,args1.size()));
  String text= "2023-03-23";
   //     String text= "120220824142242917860027336,120220824142243440937027336";
    String[] split = text.split(",");
        System.out.println(split.length);
        for (String s : split) {
        //  System.out.println(s);authority: api-store.gaojihealth.cn

        String[] cmds = {"curl", "-X","GET",  "-H", "authority: api-internal.gaojihealth.cn",
                "-H", "pragma: no-cache",
                "-H","from_channel: srm-web" ,
                "-H","x-xsrf-token: b75ac96d-402e-496a-82f1-4d159ecbcea8",
                "-H","sec-ch-ua-mobile: ?0" ,
               // "-H","authorization: bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" ,
                "-H","accept: */*" ,
                "-H","content-type: application/json" ,

                "-H","user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36" ,
                "-H","client-id: business-app" ,
                "-H","origin: https://api-internal.gaojihealth.cn" ,
                "-H","sec-fetch-site: same-origin" ,
                "-H","sec-fetch-mode: cors" ,
                "-H","sec-fetch-dest: empty" ,
                "-H","referer: https://api-internal.gaojihealth.cn/swagger-ui/index.html" ,
                "-H","accept-language: zh-CN,zh;q=0.9,en;q=0.8,fr;q=0.7" ,
                "-H","cookie: _ati=3845830197176; _ga=GA1.2.527327123.**********; gr_user_id=a89cdcc1-79f2-4cba-95cb-ac00d671857c; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2217bee005330270-0eedbe557db7e9-1f3f6750-2304000-17bee005331865%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%2217bee005330270-0eedbe557db7e9-1f3f6750-2304000-17bee005331865%22%7D; login=; U_T=%7B%22token_type%22%3A%22bearer%22%2C%22expires_in%22%3A7198%2C%22scope%22%3A%22business-app%22%2C%22jti%22%3A%22116b95d0-98ab-46ab-8454-5ecdf45eefe8%22%2C%22unionId%22%3A%22oWMs41CYC2IECZ2pizOZ_wbaGgJ0%22%2C%22phone%22%3A%2218511256591%22%2C%22loginName%22%3A%2218511256591%22%2C%22loginCreatedDate%22%3A%222021-02-24%2010%3A12%3A26%22%2C%22roles%22%3A%22%5B%5C%22BIZWAREYWF%5C%22%2C%5C%22YJYAGL%5C%22%2C%5C%22ZBRY%5C%22%2C%5C%22APOLLOXMKFRY%5C%22%2C%5C%22JKGLDEV%5C%22%2C%5C%22APOLLOGLY%5C%22%2C%5C%22BUHEAD%5C%22%2C%5C%22APOLLOZH%5C%22%5D%22%2C%22businessId%22%3A%2210000%22%2C%22name%22%3A%22%E7%8E%8B%E4%BB%A3%E5%86%9B%22%2C%22type%22%3A%221%22%2C%22userId%22%3A%222228752386510000%22%2C%22isInitialPass%22%3A%22false%22%2C%22status%22%3A%221%22%7D; access_token=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; refresh_token=******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; XSRF-TOKEN=729a1bb4-277c-454a-a70b-fe699e5417fb" ,
               // "--data-raw", s,
                "--compressed", "https://api-stage-internal.gaojihealth.cn/nyuwa/api/intranet/general/305/23c58280d2744f8291939a04a2bf71d1?businessId=444270&" };

            System.out.println(JSON.toJSONString(cmds));
            String s1 = execCurl(cmds);

            JSONObject object = JSON.parseObject(s1);
            JSONObject data = object.getJSONObject("data");
            List<NewSpuVo> rows = JSONArray.parseArray(data.getString("rows"), NewSpuVo.class);

            System.out.println(JSON.toJSONString(rows));
            MaintenanceLevelEnum sourceByCode = MaintenanceLevelEnum.getSourceByCode(3);
            System.out.println(StorageConditionEnum.getName(3)+"xxxx"+sourceByCode.getSource());

//            rows.stream().forEach(v->{
//                System.out.println(StorageConditionEnum.getName(3).equals(v.getStoragecond()) &&  sourceByCode.getSource().equals(v.getMainten_type()));
//            });
//            List<NewSpuVo> needCheckList = rows.stream().filter(v ->  StorageConditionEnum.getName(3).equals(v.getStoragecond()) && null != sourceByCode && sourceByCode.getSource().equals(v.getMainten_type())).collect(Collectors.toList());
//            if(CollectionUtils.isEmpty(needCheckList)){
//                System.out.println("没有满足条件的商品");
//                continue;
//            }
 //           System.out.println(JSON.toJSONString(needCheckList));
        }
    }


    public static String execCurl(String[] cmds) {
        ProcessBuilder process = new ProcessBuilder(cmds);
        Process p;
        try {
            p = process.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(p.getInputStream()));
            StringBuilder builder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
                builder.append(System.getProperty("line.separator"));
            }
            return builder.toString();

        } catch (IOException e) {
            System.out.print("error");
            e.printStackTrace();
        }
        return null;
    }

}
