package com.tomas.mybaties3.utils;

import java.util.ArrayList;
import java.util.List;

/**
 * StockMDUpdateVersion 描述
 *
 * <AUTHOR>
 * @create 2022/4/21
 **/
public class StockMDUpdateVersion {

        public static void main(String[] args) {
          //  String ss = "43715262,43715262,43915262,43915262,44115262,44315262,44515262,44715262,44915262,44915262,45115262,48515262,48615262,50215262,50315262,50415262,50515262,50515262,50615262,50715262,50815262,50915262,51015262,51115262,51115262,51215262,51315262,51415262,51515262,51615262,51715262,51815262,51915262,51915262,52015262,52115262,52115262,52215262,52315262,52415262,52515262,52515262,52615262,52715262,52715262,52815262,52915262,53015262,53115262,53715262,53715262,53815262,53915262,54115262,54215262,54315262,54415262,94298315262,94366815262,94483215262,94595715262,94624115262,94732415262,94842715262,94995615262,95079015262,95079015262,95157915262,95272515262,95372415262,95469615262,95546015262,95546015262,95644915262,95721515262,95941815262,96019015262,96126915262,96215315262,96364115262,96477215262,96523115262,96659915262,96777715262,96873715262,96970215262,231327715262,247024815262,247185415262,247254415262,247379115262,247485015262,247595815262,247644615262,766161515262,766281315262,987441815262,1350486415262,1374855915262,1452881015262,1453035215262,1458095315262,1458227915262,1468418515262,1468688215262,1501295115262,1503265515262,1525874215262,1531635915262,1586020815262,1632227215262,1930274915262,2039681415262,2039681415262,2061862015262,2077247715262,2255069015262,2306896015262,2559665415262,2646442115262,2653310815262,413624366801,413750366801,413894966801,413957366801,414080966801,414168266801,414214266801,414365066801,414478366801,414589966801,414648166801,414744866801,414863866801,414971366801,415091366801,415122566801,415210566801,415322566801,415455766801,415592066801,415643466801,415766866801,415893366801,415925566801,416041766801,416144066801,416214166801,416318966801,416475266801,416531666801,416686766801,416736266801,416874966801,416972466801,417068366801,417183766801,417261366801,417362366801,417432066801,417581666801,417612866801,417759666801,417848266801,417939066801,418035966801,418131266801,418277266801,418355366801,418473566801,418520266801,418653966801,418747666801,418861266801,418973966801,419040166801,419146166801,419222166801,419331966801,419431066801,419573966801,419612466801,419715966801,419871766801,419968766801,420032366801,420138566801,420267566801,420363066801,420487266801,420598566801,420614066801,420779266801,420870166801,420925066801,421019466801,421185466801,421223666801,421377166801,421429166801,421597766801,421620766801,421779266801,421861266801,421916266801,422067266801,422185666801,422259566801,422313466801,422413566801,422556366801,422692766801,422741566801,422895566801,422925566801,423019866801,423121666801,423262466801,423325566801,423494866801,423574866801,423677166801,423719966801,423834866801,423951566801,424051466801,424188866801,424238966801,424353366801,424426566801,424583666801,424660266801,424779566801,424837466801,424976666801,425049366801,425148866801,425268666801,425366166801,425445866801,425592966801,425655266801,425797166801,425896866801,425951466801,426078266801,426143566801,426221566801,426322066801,426471666801,426556366801,426658266801,426723266801,426894066801,426910666801,427051466801,427172366801,427238666801,427372266801,427496666801,427536266801,427626766801,427784566801,427821566801,427918166801,428018066801,428134566801,428279866801,428357166801,428485966801,428525966801,428661866801,428796066801,428811766801,428999066801,429017466801,429147066801,429240766801,429354966801,429481766801,429532866801,429655166801,429753066801,429899666801,429974766801,430043766801,430111866801,430276266801,430361966801,430474266801,430513566801,430623266801,430775966801,430898466801,430954466801,431085966801,431159766801,431216766801,431383866801,431412666801,431582766801,431643666801,431779066801,431873566801,431966766801,432073966801,432131966801,432224966801,432360166801,432461666801,432519966801,432685066801,432784566801,432819166801,432982266801,433044966801,433227266801,433360066801,433412666801,433525866801,433643366801,433759566801,433871666801,433988766801,433988766801,434090066801,434172666801,434293766801,434393366801,434489766801,434591466801,434647466801,434720466801,434813366801,451993266801,452041566801,452041566801,452172866801,452236666801,452358266801,452431966801,452524766801,452686166801,452724766801,452870366801,1040282766801,1154680666929,1154873666929,1155090766929,1155440266929,1155639266929,1155890866929,1156085366929,1375042366929,2063621166929,2118459966929,2254433266929,2254872366929,2604079866929,2621825366929,2658034666929,2719375166929,454181376879,454253276879,454332776879,454455676879,454597976879,454637176879,454716776879,454897076879,454917976879,455041376879,471880776879,471971276879,472033376879,472142576879,472285376879,472360576879,472430676879,472517376879,472669476879,472771676879,472894476879,472984976879,473077276879,473178076879,473242476879,473361976879,473485576879,957015276879,983072576879,1106676476879,1145224676879,1150075776879,1310461276879,1326076676879,1336288376879,1338223476879,1373685576879,1497281976879,1515815776879,1530844776879,1545225976879,1991038376879,2063049676879,2073644476879,2657686776879,455218877265,455365277265,455499077265,455576477265,455695077265,455786177265,455867077265,455983977265,456070477265,456150077265,456244477265,456356177265,456410077265,456567677265,456625277265,456749477265,456879677265,456940877265,457080577265,457145577265,457233277265,457342277265,457457377265,457518077265,457628777265,457792277265,457846177265,457992177265,458039877265,458188577265,458282777265,458381777265,458443577265,458513677265,458633577265,458778077265,458876277265,458938977265,459072677265,459115777265,459115777265,459233177265,459315877265,459456377265,459522977265,459649477265,459787477265,459867877265,459924677265,460033877265,460127377265,460255877265,460399177265,767391077265,767495977265,868639377265,868639377265,939432777265,1173866277265,1173866277265,1446274477265,1459426877265,1586284677265,1619839077265,1929472077265,2039829077265,2062053077265,2127870577265,2137646977265,2283479877265,2297095277265,2375291077265,2391445477265,2436052177265,2636024077265,2638830077265,2664571377265,2664631177265,2735043677265";
// String ss = "43715262,472771676879";
            String ss = "2837398331763,2831044731763,2831175231763";
            List<String> list1 = new ArrayList<String>();
            list1.add("biz-core-18-R-stockcenter cowell_stockcenter_0");
            List<String> list2 = new ArrayList<String>();
            list2.add("biz-core-18-R-stockcenter cowell_stockcenter_1");
            List<String> list3 = new ArrayList<String>();
            list3.add("biz-core-19-R-stockcenter cowell_stockcenter_0");
            List<String> list4 = new ArrayList<String>();
            list4.add("biz-core-19-R-stockcenter cowell_stockcenter_1");
            String[] split = ss.split(",");
            for (String s : split) {
                String s3 = "";
                int m2;
                if (s.length() == 8) {
                    s3 = s.substring(0,3);
                    m2 = 1;
                } else {
                    String substring = s.substring(s.length() - 10, s.length() - 5);
                    String s2 = substring.substring(0,2);
                    s3 = substring.substring(2,5);
                    int n2 = Integer.valueOf(s2);
                    m2 = n2%4+1;
                }
                int n3 = Integer.valueOf(s3);
                int m3 = n3%256;
                String sm3 = m3+"";
                if (sm3.length() == 1) {
                    sm3 = "00"+sm3;
                }
                if (sm3.length() == 2) {
                    sm3 = "0"+sm3;
                }
                String sql = String.format("update stock_goods_batch_code_%s set version = version + 1 where store_id = %s;",sm3,s);
                if (m2 == 1) {
                    list1.add(sql);
                }
                if (m2 == 2) {
                    list2.add(sql);
                }
                if (m2 == 3) {
                    list3.add(sql);
                }
                if (m2 == 4) {
                    list4.add(sql);
                }
            }

            for (String s : list1) {
                System.out.println(s);
            }
            for (String s : list2) {
                System.out.println(s);
            }
            for (String s : list3) {
                System.out.println(s);
            }
            for (String s : list4) {
                System.out.println(s);
            }
        }
}
