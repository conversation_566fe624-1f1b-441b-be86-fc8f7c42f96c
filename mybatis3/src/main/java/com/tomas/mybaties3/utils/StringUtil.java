package com.tomas.mybaties3.utils;

import cn.hutool.crypto.SecureUtil;
import cn.hutool.crypto.asymmetric.KeyType;
import cn.hutool.crypto.asymmetric.RSA;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;

import java.security.KeyPair;

public class StringUtil {

    // RSA密钥对（实际应用中应妥善保存私钥）
    private static final KeyPair rsaKeyPair = SecureUtil.generateKeyPair("RSA");
    private static final RSA rsa = new RSA(rsaKeyPair.getPrivate(), rsaKeyPair.getPublic());

    /**
     * 功    能：hash函数可以有效减少碰撞
     * 作    者：dingxl
     * 时    间：2019-03-20
     */
    public static int hash(Object k) {
        if (k == null) {
            return 0;
        }
        int h = k.hashCode();
        h ^= (h >>> 20) ^ (h >>> 12);
        return h ^ (h >>> 7) ^ (h >>> 4);
    }

    /**
     * 功    能：取模算法
     * 作    者：dingxl
     * 时    间：2019-03-20
     */
    public static int indexFor(int hashCode, int length) {
        return hashCode & (length - 1);
    }

    /**
     * 功    能：获取index
     * 作    者：dingxl
     * 时    间：2019-03-20
     */
    public static int getTabIndex(String no) {
        return indexFor(hash(no), 256);
    }

    /**
     * 功    能：获取index
     * 作    者：dingxl
     * 时    间：2019-03-20
     */
    public static int getTabIndex(String no, int threshold) {
        return indexFor(hash(no), threshold);
    }

    /**
     * 功    能：获取表名
     * 作    者：dingxl
     * 时    间：2019-03-20
     */
    public static String getTabName(String no) {
        return "_" + getTabIndex(no);
    }


    /**
     * 双层加密
     * @param data 原始数据
     * @return 加密结果，格式：RSA加密的AES密钥$AES加密的数据
     */
    public static String encrypt(String data) {
        // 1. 生成AES密钥并加密数据
        byte[] aesKey = SecureUtil.generateKey(SymmetricAlgorithm.AES.getValue()).getEncoded();
        AES aes = SecureUtil.aes(aesKey);
        String encryptedData = aes.encryptBase64(data);

        // 2. 用RSA公钥加密AES密钥
        String encryptedAesKey = rsa.encryptBase64(aesKey, KeyType.PublicKey);

        // 组合加密结果
        return encryptedAesKey + "$" + encryptedData;
    }

    /**
     * 双层解密
     * @param encryptedData 加密后的数据，格式：RSA加密的AES密钥$AES加密的数据
     * @return 解密后的原始数据
     */
    public static String decrypt(String encryptedData) {
        // 分割加密的数据
        String[] parts = encryptedData.split("\\$");
        if (parts.length != 2) {
            throw new IllegalArgumentException("无效的加密数据格式");
        }

        // 1. 用RSA私钥解密AES密钥
        byte[] aesKey = rsa.decrypt(parts[0], KeyType.PrivateKey);

        // 2. 用AES密钥解密原始数据
        AES aes = SecureUtil.aes(aesKey);
        return aes.decryptStr(parts[1]);
    }


    // 测试
    public static void main(String[] args) {
        String originalData = "这是一段需要进行双层加密的敏感数据：123456";
        System.out.println("原始数据：" + originalData);

        // 加密
        String encrypted = encrypt(originalData);
        System.out.println("加密后：" + encrypted);

        // 解密
        String decrypted = decrypt(encrypted);
        System.out.println("解密后：" + decrypted);
    }


}
