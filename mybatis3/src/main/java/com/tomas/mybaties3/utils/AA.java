package com.tomas.mybaties3.utils;

import java.util.Objects;

public class AA {



    /**
     * 手机号脱敏处理
     * 保留前3位和后4位，中间用4个星号代替
     * 例如：13800138000 脱敏后为 138****8000
     *
     * @param phone 原始手机号
     * @return 脱敏后的手机号，如果输入为空或格式不正确则返回原字符串
     */
    public static String desensitizerPhone(String phone) {
        // 检查手机号是否为空
        if (Objects.isNull(phone) || phone.trim().isEmpty()) {
            return phone;
        }
        // 去除空格串中的空格
        String trimmedPhone = phone.trim();
        // 简单校验手机号格式（11位数字）
        if (trimmedPhone.length() != 11 || !trimmedPhone.matches("\\d+")) {
            return phone;
        }
        // 进行脱敏处理
        return trimmedPhone.substring(0, 3) + "****" + trimmedPhone.substring(7);
    }
    /**
     * 地址脱敏处理
     * 保留前6位字符，后续字符用星号代替
     * 例如：北京市朝阳区某某街道 脱敏后为 北京市朝***
     *
     * @param address 原始地址
     * @return 脱敏后的地址，如果输入为空则返回原字符串
     */
    public static String desensitizerAddress(String address) {
        // 检查地址是否为空
        if (Objects.isNull(address) || address.trim().isEmpty()) {
            return address;
        }
        // 去除空格
        String trimmedAddress = address.trim();
        // 如果地址长度小于等于6位，直接返回原地址
        if (trimmedAddress.length() <= 6) {
            return trimmedAddress;
        }
        // 保留前6位，后面用星号代替（星号数量与被替换字符数相同）
        StringBuilder sb = new StringBuilder();
        sb.append(trimmedAddress.substring(0, 6));
        // 计算需要替换的字符数并添加对应数量的星号
        int replaceLength = trimmedAddress.length() - 6;
        for (int i = 0; i < replaceLength; i++) {
            sb.append("*");
        }
        return sb.toString();
    }

    public static void main(String[] args) {
        // 手机号脱敏测试


        // 正常情况
        System.out.println(Objects.equals("138****8000",desensitizerPhone("13800138000")));
        System.out.println(Objects.equals("139****1234", desensitizerPhone("13912341234")));

        // 带空格的情况
        System.out.println(Objects.equals("138****8000",desensitizerPhone(" 13800138000 ")));

        // 异常情况
        System.out.println(Objects.equals("",desensitizerPhone("")));  // 空字符串
        System.out.println(Objects.equals("12345",desensitizerPhone("12345")));  // 短于11位
        System.out.println(Objects.equals("123456789012",desensitizerPhone("123456789012")));  // 长于11位
        System.out.println(Objects.equals("138abc8000",desensitizerPhone("138abc8000")));  // 包含非数字

        // 正常情况
        System.out.println(Objects.equals("北京市朝***", desensitizerAddress("北京市朝阳区某某街道")));
        System.out.println(Objects.equals("上海市浦***", desensitizerAddress("上海市浦东新区")));

        // 带空格的情况
        System.out.println(Objects.equals("广州市天***", desensitizerAddress(" 广州市天河区中山大道 ")));

        // 边界情况
        System.out.println(Objects.equals("123456", desensitizerAddress("123456")));  // 刚好6位
        System.out.println(Objects.equals("12345", desensitizerAddress("12345")));    // 少于6位



        System.out.println(desensitizerAddress("abcdefghijkl")+"xxx"+desensitizerAddress("中国北京市海淀区"));  // 字母
        System.out.println(Objects.equals("abcdef***", desensitizerAddress("abcdefghijkl")));  // 字母
        System.out.println(Objects.equals("中国北京市***", desensitizerAddress("中国北京市海淀区")));


    }
}
