package com.tomas.mybaties3.utils.vo;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
public class JsonObjectBiz {

    @JsonProperty("0")
    private Map<String, Object> zeroMap;

    @JsonProperty("changed")
    private String changed;

    @JsonProperty("domainCode")
    private String domainCode;

    @JsonProperty("api_name")
    private String apiName;

    @JsonProperty("version")
    private String version;

    @JsonProperty("belong_bu")
    private String belongBu;

    @JsonProperty("app_name")
    private String appName;

    @JsonProperty("api_permission_type")
    private String apiPermissionType;

    @JsonProperty("api_type")
    private String apiType;

    @JsonProperty("is_white")
    private String isWhite;

    @JsonProperty("api_desc")
    private String apiDesc;

    @JsonProperty("requestType")
    private String requestType;

    @JsonProperty("api_url")
    private String apiUrl;

    @JsonProperty("code")
    private String code;

    @JsonProperty("description")
    private Object description;

    @JsonProperty("type")
    private int type;

    @JsonProperty("domainId")
    private String domainId;

    @JsonProperty("createBy")
    private Object createBy;

    @JsonProperty("updateBy")
    private Object updateBy;

    @JsonProperty("gmtUpdate")
    private Object gmtUpdate;

    @JsonProperty("domainName")
    private String domainName;

    @JsonProperty("id")
    private int id;


    @JsonProperty("requestPath")
    private Object requestPath;


    @JsonProperty("login_code")
    private String loginCode;

    @JsonProperty("project_manager")
    private String projectManager;

    @JsonProperty("status")
    private int status;

    @JsonProperty("publish_status")
    private int publishStatus;
}