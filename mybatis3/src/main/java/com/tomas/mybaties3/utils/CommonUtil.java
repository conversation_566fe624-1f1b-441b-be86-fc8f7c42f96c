package com.tomas.mybaties3.utils; /**
 * Create by hfzhang
 *
 * @date 2018/7/16
 */

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2018/7/16
 */
public class CommonUtil {
    private static final Logger log = LoggerFactory.getLogger(CommonUtil.class);


    /**
     * 保证每个线程的本地变量都是安全的，不同线程之间并不共享相同的SimpleDateFormat，从而避免了线程安全问题。
     * 虽然这种方式性能高，但是占用内存，不过也不是很夸张
     */
    private static ThreadLocal<DateFormat> threadLocal = new ThreadLocal<DateFormat>() {
        @Override
        protected DateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        }
    };

    /**
     * 字符串时间转换成Date
     * @param dateStr
     * @throws ParseException
     */
    public static Date strToDate(String dateStr){
        try {
            return threadLocal.get().parse(dateStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static String getReturnStr(String str){
        if(StringUtils.isBlank(str) || "null".equals(str)){
            return "";
        }

        return str.trim();
    }

    public static String getReturnNum(String num){
        if(StringUtils.isBlank(num) || "null".equals(num)){
            return "0";
        }

        return num.trim();
    }


    /**
     * 判断json是不是数组
     * @param requestBody
     * @param key
     * @return
     */
    public static JSONArray covertJsonMap(JSONObject requestBody, String key) {
        if(requestBody == null){
            return null;
        }
        Object data = requestBody.get(key);
        if (data instanceof Map) {
            return JSONArray.parseArray("[" + requestBody.getString(key) + "]");
        } else {
            return requestBody.getJSONArray(key);
        }
    }

    /**
     * 将字符串数量截取并返回数字类型
     * @param str
     * @return
     */
    public static Integer covertStrToInt(String str){
        if(StringUtils.isBlank(str)){
            return 0;
        }
        try {
            if(str.contains(".")){
                str = str.substring(0, str.indexOf("."));
            }

            return Integer.parseInt(str);
        }catch (Exception e){
            log.error("covertStrToInt|error",e);
        }
        return 0;
    }

    /**
     * @param targetClazz java.lang.reflect.Field 的数据类型 clazz。 getType 方法获取
     * @return
     */
    public  static boolean isNumberType(Class<?> targetClazz) {
        // 判断包装类
        if (Number.class.isAssignableFrom(targetClazz)) {
            return true;
        }
        // 判断原始类,过滤掉特殊的基本类型
        if (targetClazz == boolean.class || targetClazz == char.class || targetClazz == void.class) {
            return false;
        }
        return targetClazz.isPrimitive();
    }

    /**
     * 是否 Integer 类型
     * @param value
     * @return
     */
    public static boolean isValidInt(String value) {
        try {
            Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return false;
        }
        return true;
    }

    public static int length(String value) {
        int valueLength = 0;
        String chinese = "[\u0391-\uFFE5]";
        /* 获取字段值的长度，如果含中文字符，则每个中文字符长度为2，否则为1 */
        for (int i = 0; i < value.length(); i++) {
            /* 获取一个字符 */
            String temp = value.substring(i, i + 1);
            /* 判断是否为中文字符 */
            if (temp.matches(chinese)) {
                /* 中文字符长度为2 */
                valueLength += 1;
            } else {
                /* 其他字符长度为1 */
                valueLength += 1;
            }
        }
        return valueLength;
    }

    /**
     * 去除下划线
     * @param target
     * @return
     */
    public static String rejectUnderLine(String target){
        if(StringUtils.isBlank(target)){
            return "";
        }
        if(target.contains("_") && target.indexOf("_")!=0){
            return target.split("_")[0];
        }
        return target;
    }
    /**
     * 参数 md5
     * @param params
     * @return
     */
    public static String md5(Object... params){
        StringBuilder content=new StringBuilder();
        if (params.length==0){
            return null;
        }else {
            for (Object param : params) {
                if (null != param) {
                    content.append(JSON.toJSONString(param));
                }
            }
        }
        if (content.length()==0){
            return null;
        }
        String result = null;
        try {
            MessageDigest md = MessageDigest.getInstance("md5");
            md.update(content.toString().getBytes());
            byte[] bytes = md.digest();
            StringBuilder sb = new StringBuilder();
            for (byte b : bytes) {
                String str = Integer.toHexString(b & 0xFF);
                if (str.length() == 1) {
                    sb.append("0");
                }
                sb.append(str);
            }
            result = sb.toString();
        } catch (NoSuchAlgorithmException e) {
            log.error("md5|error!", e);
        }
        return result;
    }
 /*   public static void main(String[] args) throws Exception {
//        JSONArray jsonArray = new JSONArray();
//        jsonArray.add(createJson("1000","1","2000","1"));
//        jsonArray.add(createJson("1000","2","2000","2"));
//        jsonArray.add(createJson("1000","3","2000","3"));
//        List<SrmCheckOrderDTO> srmCheckOrderDTOS = JSON.parseArray(jsonArray.toJSONString(), SrmCheckOrderDTO.class);
//        System.out.println(srmCheckOrderDTOS);
//    }
//
//    public static JSONObject createJson(String iscmPlanNo, String iscmPlanId, String iscmOrderNo, String iscmOrderId){
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("PLANNO",iscmPlanNo);
//        jsonObject.put("ITEMID",iscmPlanId);
//        jsonObject.put("EBELN",iscmOrderNo);
//        jsonObject.put("EBELP",iscmOrderId);
//        return jsonObject;
    }*/


    public static void main(String[] args) {
        List<String> filterlist=new ArrayList<>();
        String filter = "id,in,";
        filterlist.add(filter);
        filterlist.add("filter222");
        String[] filterArray = filterlist.toArray(new String[0]);

        System.out.println(JSONObject.toJSONString(filterArray));

    }
}
