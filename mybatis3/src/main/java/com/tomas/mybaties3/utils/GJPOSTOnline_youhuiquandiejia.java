package com.tomas.mybaties3.utils;

import com.alibaba.fastjson.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class GJPOSTOnline_youhuiquandiejia {

    public static void main(String[] args) throws IOException, InterruptedException {
        List<JSONObject> list =new ArrayList<>();
        String[] ids = "14395833,14395831,14395830,14395829,14395828,14395827,14395826,14395825,14395824,14395823,14395822,14395821,14395820,14395819,14395818,14395817,14395816,14395815,14395813,14395812,14395811,14395810,14395746,14395745,14395744,14395740,14395734,14395732,14395731,14395727,14395726,14395725,14395724,14395723,14395721,14395719,14392270,14392264,14392255,14390150,14389903,14389863,14389861,14389858,14389855,14389854,14389841,14389835,14389815,14389806,14389804,14389793,14389768,14389763,14389753,14389745,14389743,14389737,14389732,14389729,14389728,14389717,14389408,14389368,14389339,14389320,14389319,14389318,14389311,14389303,14389302,14389299,14389242,14389241,14389238,14389235,14389221,14389220,14389217,14389215,14389193,14389191,14389183,14389163,14389155,14389150,14389146,14389143,14389134,14389132,14389125,14389122,14389118,14389114,14389111,14389105,14389104,14389101,14389046,14389018,14388942,14388941,14388940,14388939,14388938,14388937,14388936,14388934,14388933,14388932,14388931,14388930,14388928,14388927,14388926,14388925,14388924,14388922,14388921".split(","); // You can add more IDs here
        List<Long> cid=new ArrayList<>();
        for (String id : ids) {
            cid.add(Long.parseLong(id));
        }
        String collect = cid.stream().map(String::valueOf).collect(Collectors.joining(","));
        System.out.print(collect);
//        for (String id : ids) {
//            //[\n  {\n    "couponIdList": [\n      104004722735\n    ],\n    "couponJoin": 1,\n    "joinWay": 3,\n    "promotionId": 14396307\n  }\n]
//            JSONObject payload = new JSONObject();
//            List<Long> cid=new ArrayList<>();
//            cid.add(104004722735L);
//            payload.put("couponIdList", cid);
//            payload.put("couponJoin", 1);
//            payload.put("joinWay", 3);
//            payload.put("promotionId", Long.parseLong(id));
//            list.add(payload);
//
//
////            String response = sendPostRequest(
////                "https://api-internal.gaojihealth.cn/marketing/api/marketingPlanTool/updateCouponJoin",
////                payload.toString()
////            );
////            Thread.sleep(2000); // Wait for 2 seconds between requests
////            System.out.println("Response: " + response);
//
//        }


        System.out.println("Sending request with payload: " + JSONObject.toJSONString(list));
    }


//
//    [
//    {
//        "couponIdList": [
//        104004722735
//    ],
//        "couponJoin": 1,
//            "joinWay": 3,
//            "promotionId": 14396307
//    }
//]

    private static String sendPostRequest(String urlString, String jsonInputString) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setDoOutput(true);
        
        // Set all headers
        connection.setRequestProperty("authority", "api-internal.gaojihealth.cn");
        connection.setRequestProperty("pragma", "no-cache");
        connection.setRequestProperty("from_channel", "srm-web");
        connection.setRequestProperty("x-xsrf-token", "bb8ada00-0138-4540-80d8-55e9f8bb3cbf");
        connection.setRequestProperty("sec-ch-ua-mobile", "?0");
        connection.setRequestProperty("accept", "*/*");
        connection.setRequestProperty("content-type", "application/json");
        connection.setRequestProperty("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36");
        connection.setRequestProperty("client-id", "business-app");
        connection.setRequestProperty("origin", "https://api-internal.gaojihealth.cn");
        connection.setRequestProperty("sec-fetch-site", "same-origin");
        connection.setRequestProperty("sec-fetch-mode", "cors");
        connection.setRequestProperty("sec-fetch-dest", "empty");
        connection.setRequestProperty("referer", "https://api-internal.gaojihealth.cn/swagger-ui/index.html");
        connection.setRequestProperty("accept-language", "zh-CN,zh;q=0.9,en;q=0.8,fr;q=0.7");
        
        // Set cookie (Anonymized with 'x')
        String cookie = "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22191a385a6f72-0afee2f1764af9-********-2304000-191a385a6f8b9a%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%22191a385a6f72-0afee2f1764af9-********-2304000-191a385a6f8b9a%22%7D; login=; NW_U_T=%7B%22id%22%3A102%2C%22account%22%3A%*************%22%2C%22name%22%3A%22%E7%8E%8B%E4%BB%A3%E5%86%9B%22%2C%22avater%22%3Anull%2C%22phone%22%3A%*************%22%2C%22token%22%3A%226I5dFz5n45irmRGzeUpU8J0H1svTRmpqaU4UoRO2GW%2Bv72JsNvU7CIz3DC7w4dqwY558DtA0%2FgC%2FCAwumP5IFMO9mFCpdDcIdzPqKIcmpa2WhQVmdw562eV%2B2pOXJ0DeNyUo7e45Lv8jf3Oc2G8n4MzZgHuRKWe0wY%2BD7Wqdn3LuVMHz98O2PrdbJZTC%2Bb8%2Fkd6mBfKCBsZQFcetam5vdPVhNZ1BCnE58%2B44enw6%2Fjplk7arejVXaEwD9bm66%2Fs1RUBydhxfoJJS8GJXMntrwXLUiCe9kKAS1720INpOYPNezx7pf0ELwyVqqJGTT%2BISvTereczTNLUSuUptzCeSylIpKvO8jGLQRJOTQojtH7Y%3D%22%2C%22deptName%22%3Anull%2C%22deptId%22%3Anull%7D; gx_user_id=f98056780baf407e82343445e6570603; user_code=********; user_name=%E8%B7%AF%E6%98%8E%E8%85%BE; U_T=%7B%22token_type%22%3A%22bearer%22%2C%22expires_in%22%3A7198%2C%22scope%22%3A%22business-app%22%2C%22jti%22%3A%22ebb4408e-d373-444c-8595-78fa65bea97a%22%2C%22unionId%22%3Anull%2C%22platformBusinessId%22%3A193329%2C%22platformUserId%22%3A%*****************%22%2C%22loginName%22%3A%*************%22%2C%22loginCreatedDate%22%3A%222021-05-26%2018%3A32%3A33%22%2C%22roles%22%3A%22%5B%5C%22SZHSPGLY%5C%22%2C%5C%22GYLCSZY%5C%22%2C%5C%22DDMSHGLY%5C%22%2C%5C%22DDMJGS%5C%22%2C%5C%22JSWYH%5C%22%2C%5C%22DZ%5C%22%2C%5C%22ZHYXYYZY%5C%22%2C%5C%22JMGLY%5C%22%2C%5C%22MDDZ%5C%22%2C%5C%22GJTCSJS%5C%22%2C%5C%22APOLLOBUJGS%5C%22%2C%5C%22BIZWAREGL%5C%22%5D%22%2C%22businessId%22%3A%2278924%22%2C%22name%22%3A%22tomas%22%2C%22type%22%3A%221%22%2C%22userId%22%3A%*****************%22%2C%22status%22%3A%221%22%2C%22phone%22%3A%*************%22%2C%22sso%22%3A%22sso_test%22%7D; access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; session_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; XSRF-TOKEN=c361d7fc-3cdc-4c75-8551-feca4e53b383";
        connection.setRequestProperty("cookie", cookie);

        // Send request
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonInputString.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        // Read response
        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
        }

        return response.toString();
    }
}