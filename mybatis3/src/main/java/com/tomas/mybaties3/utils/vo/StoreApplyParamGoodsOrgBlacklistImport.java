package com.tomas.mybaties3.utils.vo;

import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.StringJoiner;

public class StoreApplyParamGoodsOrgBlacklistImport {
    private Integer lineNum;
    private String storeCode;
    private String goodsNo;
    private String startDate;
    private String endDate;

    public Integer getLineNum() {
        return lineNum;
    }

    public void setLineNum(Integer lineNum) {
        this.lineNum = lineNum;
    }

    public String getStoreCode() {
        return storeCode;
    }

    public void setStoreCode(String storeCode) {
        this.storeCode = storeCode;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    @Override
    public String toString() {
        return new StringJoiner(", ", StoreApplyParamGoodsOrgBlacklistImport.class.getSimpleName() + "[", "]")
                .add("lineNum=" + lineNum)
                .add("storeCode='" + storeCode + "'")
                .add("goodsNo='" + goodsNo + "'")
                .add("startDate=" + startDate)
                .add("endDate=" + endDate)
                .toString();
    }

    public static LinkedHashMap<String, String> getFieldMap() {
        LinkedHashMap<String, String> fieldMap = Maps.newLinkedHashMap();
        fieldMap.put("门店编码", "storeCode");
        fieldMap.put("商品编码", "goodsNo");
        fieldMap.put("开始日期", "startDate");
        fieldMap.put("结束日期", "endDate");
        return fieldMap;
    }

    public static void stringTrim(List<StoreApplyParamGoodsOrgBlacklistImport> imports) {
        imports.forEach(v -> {
            v.setStoreCode(StringUtils.trim(v.getStoreCode()));
            v.setGoodsNo(StringUtils.trim(v.getGoodsNo()));
        });
    }

    public static boolean isEmpty(List<StoreApplyParamGoodsOrgBlacklistImport> imports) {
        if (CollectionUtils.isEmpty(imports)) {
            return true;
        }
        int nullCount = (int) imports.stream().filter(v -> StringUtils.isBlank(v.getStoreCode()) && StringUtils.isBlank(v.getGoodsNo())
                && null == v.getEndDate() && null == v.getStartDate()).count();
        return nullCount == imports.size();
    }
}
