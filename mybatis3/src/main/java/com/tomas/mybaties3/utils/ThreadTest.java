package com.tomas.mybaties3.utils;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.*;

/**
 * ThreadTest 描述
 *
 * <AUTHOR>
 * @create 2021/10/21
 **/
@Slf4j
public class ThreadTest {

    private ExecutorService executorService = Executors.newFixedThreadPool(5);

    private List<Future<Integer>> listTask = new ArrayList<>();


    public static void main(String[] args) throws InterruptedException {

        ThreadTest thread=new ThreadTest();
        int test = thread.test();
        System.out.println(test);
    }


    public  int test() throws InterruptedException {
        List<String> any=new ArrayList<>();
        any.add("12312");
        any.add("123");
        any.add("1234");
        any.add("123125");
        any.add("1236");
        any.add("12347");
        any.add("123128");
        any.add("1239");
        any.add("12340");
        for (String store : any){
            List<String> storeIdList=new ArrayList<>();
            storeIdList.add(store);
            String traceId = "Tx_"+UUID.randomUUID().toString();
                    MDC.put("traceId",traceId);
            String b3_traceId ="T3x_"+UUID.randomUUID().toString();
                    MDC.put("X-B3-TraceId",b3_traceId);
            Future<Integer> future = executorService.submit(new Callable<Integer>() {
                @Override
                public Integer call() throws Exception {
                    MDC.put("traceId", traceId);
                    MDC.put("X-B3-TraceId", b3_traceId);
                    log.info("StockCostHandler | levelingCostStock | store信息={} 开始", JSON.toJSONString(store));
                    int i = new Random().nextInt(10);
                    log.info("睡一会"+i*1000L);

                    Thread.sleep(i*1000L);
                    log.info("StockCostHandler | levelingCostStock | store信息={} 更新行数={}",JSON.toJSONString(store),i);
                    return Integer.parseInt(store);
                }
            });
            listTask.add(future);
        }
        try {
            //等 600s 后开始 取结果  每个结果等 30s 超时取消
            if (executorService.awaitTermination(600, TimeUnit.SECONDS)) {
                log.info("StockCostHandler | levelingCostStock | task finished");
            } else {
                log.info("StockCostHandler | levelingCostStock | task time out,will terminate");
                for (Future<?> f : listTask) {
                    try {
                        // future将在1min之后取结果
                        Object o = f.get(4, TimeUnit.SECONDS);
                        if (null!=o) {
                            log.info("o={} one complete successfully",o);
                        }else {
                            log.info("o={} one complete error",o);
                        }
                    } catch (InterruptedException e) {
                        System.out.println("future在睡着时被打断");
                    } catch (ExecutionException e) {
                        System.out.println("future在尝试取得任务结果时出错");
                    } catch (TimeoutException e) {
                        log.info("future时间超时 t= {}");
                        f.cancel(true);
                    }
                }
            }
        } catch (InterruptedException e) {
            System.out.println("executor is interrupted");
        } finally {
            executorService.shutdown();
        }
        log.info("future完成");
        return 0;
    }
}
