package com.tomas.mybaties3.utils.vo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.ToString;

import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.Objects;

/**
 * 门店商品维度 销售分析查询VO
 */
@Data
@ToString
public class AnalysisStoreGoodsVo implements Serializable {

    BigDecimal lowerLimit = new BigDecimal("-100");

    BigDecimal upperLimit = new BigDecimal("100");

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "企业名称")
    private String businessName;

    @ApiModelProperty(value = "企业编码")
    private String businessId;

    @ApiModelProperty(value = "门店名称")
    private String storeName;

    @ApiModelProperty(value = "门店编码(新改的)")
    private String storeNo;

    @ApiModelProperty(value = "门店id")
    private Long storeId;

    @ApiModelProperty(value = "促销单号")
    private Long promotionId;

    @ApiModelProperty(value = "促销活动名称")
    private String promotionName;

    @ApiModelProperty(value = "促销单据类型描述")
    private String promotionType;

    @ApiModelProperty(value = "营销方案编码")
    private String promotionPlanName;

    @ApiModelProperty(value = "营销方案名称")
    private String promotionPlanCode;

    @ApiModelProperty(value = "方案类型描述")
    private String promotionPlanSubject;

    @ApiModelProperty(value = "商品编码")
    private String goodsNo;

    @ApiModelProperty(value = "销售数量")
    private BigDecimal skuCount;

    @ApiModelProperty(value = "客流数量")
    private int customerTrafficCount;
    public int getCustomerTrafficCount() {
        return customerTrafficCount-getReturnCustomerTrafficCount();
    }

    @ApiModelProperty(value = "促销客流数量")
    private int promotionCustomerTrafficCount;
    public int getPromotionCustomerTrafficCount() {
        return promotionCustomerTrafficCount-getReturnPromotionCustomerTrafficCount();
    }

    @ApiModelProperty(value = "促销客流占比")
    private BigDecimal promotionCustomerTrafficPercentage;
    public BigDecimal getPromotionCustomerTrafficPercentage() {
        if(Objects.nonNull(getPromotionId())){
            if(getAllCustomerTrafficCount().compareTo(BigDecimal.ZERO) != 0){
                return  BigDecimal.valueOf(getCustomerTrafficCount()).divide(getAllCustomerTrafficCount(), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            }else {
                return BigDecimal.valueOf(0);
            }
        }else {
            if(!BigDecimal.ZERO.equals(getCustomerTrafficCount())){
                return  BigDecimal.valueOf(getPromotionCustomerTrafficCount()).divide(BigDecimal.valueOf(getCustomerTrafficCount()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
            }else {
                return BigDecimal.valueOf(0);
            }
        }

    }
    @ApiModelProperty(value = "会员客流数量")
    private int memberTrafficCount;
    public int getMemberTrafficCount() {
        return memberTrafficCount-getReturnMemberTrafficCount();
    }

    @ApiModelProperty(value = "会员客流占比")
    private BigDecimal memberTrafficPercentage;
    public BigDecimal getMemberTrafficPercentage() {
        if(!BigDecimal.ZERO.equals(getCustomerTrafficCount())){
            return  BigDecimal.valueOf(getMemberTrafficCount()).divide(BigDecimal.valueOf(getCustomerTrafficCount()), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }else {
            return BigDecimal.valueOf(0);
        }
    }

    @ApiModelProperty(value = "预估成本金额")
    private BigDecimal estimatedCostAmount;


    @ApiModelProperty(value = "标价合计")
    private BigDecimal estimatedReceivedAmount;


    @ApiModelProperty(value = "销售额")
    private BigDecimal salesAmount;


    @ApiModelProperty(value = "参与促销销售额")
    private BigDecimal promotionSalesAmount;


    @ApiModelProperty(value = "参数促销的成本合计")
    private BigDecimal promotionEstimatedCostAmount;


    @ApiModelProperty(value = "促销销售额占比(%)") //销售额占比 分子：促销销售额 分母：时间段内所有商品销售额 保留两位小数
    private BigDecimal salesAmountPercentage;

    public BigDecimal getSalesAmountPercentage() {
        BigDecimal salesAmount = getSalesAmount();
        if(Objects.nonNull(getPromotionId())){
            BigDecimal allSalesAmount = getAllSalesRevenue();
            if(allSalesAmount.compareTo(BigDecimal.ZERO) != 0){
                BigDecimal p =  salesAmount.divide(allSalesAmount, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                return p;
            }else {
                return BigDecimal.valueOf(0);
            }
        }else {
            BigDecimal promotionSalesAmount = getPromotionSalesAmount();
            if (salesAmount.compareTo(BigDecimal.ZERO) != 0 && promotionSalesAmount.compareTo(BigDecimal.ZERO) != 0) {
                BigDecimal p = promotionSalesAmount.divide(salesAmount, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                return p;
            } else {
                return BigDecimal.ZERO;
            }
        }

    }

    @ApiModelProperty(value = "毛利额")    //毛利额公式=实收售价合计-预估成本金额合计
    private BigDecimal grossProfitAmount;

    public BigDecimal getGrossProfitAmount() {
        return getSalesAmount().subtract(getEstimatedCostAmount());
    }

    @ApiModelProperty(value = "毛利额占比") //参与促销的总销售-参数促销的成本合计 = 参与促销的毛利总额   毛利额占比= 参与促销的毛利总额/毛利额(销售-成本合计)
    private BigDecimal grossProfitPercentage;
    public BigDecimal getGrossProfitPercentage() {
        BigDecimal subtracted = getGrossProfitAmount();
        if(Objects.nonNull(getPromotionId())){
            BigDecimal allGrossProfit = getAllSalesRevenue().subtract(getAllEstimatedCostAmount());
            if(allGrossProfit.compareTo(BigDecimal.ZERO) != 0){
                BigDecimal p =  subtracted.divide(allGrossProfit, 4, BigDecimal.ROUND_HALF_UP).multiply(BigDecimal.valueOf(100));
                return p;
            }else {
                return BigDecimal.valueOf(0);
            }
        }else {
            if(subtracted.compareTo(BigDecimal.ZERO) != 0){
                BigDecimal p = (getPromotionSalesAmount().subtract(getPromotionEstimatedCostAmount())).divide(subtracted, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
                return p;
            }else {
                return BigDecimal.valueOf(0);
            }
        }
    }

    @ApiModelProperty(value = "毛利率") // 毛利率=毛利额/销售额
    private BigDecimal grossProfitMargin;
    public BigDecimal getGrossProfitMargin() {
        BigDecimal salesAmount = getSalesAmount();
        if(salesAmount.compareTo(BigDecimal.ZERO) != 0){
            return  getGrossProfitAmount().divide(getSalesAmount(), 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }else {
            return BigDecimal.valueOf(0);
        }
    }

    @ApiModelProperty(value = "折让比率/折扣")
    private BigDecimal discountRatio;
    public BigDecimal getDiscountRatio() {
        BigDecimal estimatedReceivedAmount = getEstimatedReceivedAmount();
        if(estimatedReceivedAmount.compareTo(BigDecimal.ZERO) != 0){
            return  getSalesAmount().divide(estimatedReceivedAmount, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }else {
            return BigDecimal.valueOf(0);
        }
    }

    @ApiModelProperty(value = "关联销售数量")
    private BigDecimal associatedSalesQuantity;

    @ApiModelProperty(value = "关联预估成本金额(改动过名称)")
    private BigDecimal associatedEstimatedCostAmount;

    @ApiModelProperty(value = "关联销售额)")
    private BigDecimal associatedSalesRevenue;

    @ApiModelProperty(value = "关联标价合计")
    private BigDecimal associatedReceivedAmount;

    @ApiModelProperty(value = "关联毛利额")
    private BigDecimal associatedGrossProfit;
    public BigDecimal getAssociatedGrossProfit() {
        return getAssociatedSalesRevenue().subtract(getAssociatedEstimatedCostAmount());
    }

    @ApiModelProperty(value = "关联销售额占比")
    private BigDecimal associatedSalesRevenuePercentage;
    public BigDecimal getAssociatedSalesRevenuePercentage() {
        BigDecimal allSalesRevenue = getAllSalesRevenue();
        if(allSalesRevenue.compareTo(BigDecimal.ZERO) != 0){
            return  getAssociatedSalesRevenue().divide(allSalesRevenue, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }else {
            return BigDecimal.valueOf(0);
        }
    }

    @ApiModelProperty(value = "关联毛利额占比")
    private BigDecimal associatedGrossProfitPercentage;
    public BigDecimal getAssociatedGrossProfitPercentage() {
        BigDecimal allGrossProfit = getAllSalesRevenue().subtract(getAllEstimatedCostAmount());
        if(allGrossProfit.compareTo(BigDecimal.ZERO) != 0){
            return  getAssociatedGrossProfit().divide(allGrossProfit, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }else {
            return BigDecimal.valueOf(0);
        }
    }

    @ApiModelProperty(value = "促销关联毛利率")
    private BigDecimal promotionalRelatedGrossMargin;
    public BigDecimal getPromotionalRelatedGrossMargin() {
        BigDecimal associatedSalesRevenue = getAssociatedSalesRevenue();
        if(associatedSalesRevenue.compareTo(BigDecimal.ZERO) != 0){
            return  getAssociatedGrossProfit().divide(associatedSalesRevenue, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }else {
            return BigDecimal.valueOf(0);
        }
    }

    @ApiModelProperty(value = "促销关联折扣")
    private BigDecimal promotionalRelatedDiscount;
    public BigDecimal getPromotionalRelatedDiscount() {
        BigDecimal associatedReceivedAmount = getAssociatedReceivedAmount();
        if(associatedReceivedAmount.compareTo(BigDecimal.ZERO) != 0){
            return  getAssociatedSalesRevenue().divide(associatedReceivedAmount, 4, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100));
        }else {
            return BigDecimal.valueOf(0);
        }
    }

    @ApiModelProperty(value = "活动优惠金额")
    private BigDecimal promotionalDiscountAmount;
    public BigDecimal getPromotionalDiscountAmount() {
        return getEstimatedReceivedAmount().subtract(getSalesAmount());
    }

    @ApiModelProperty(value = "商品通用名")
    private String curName;

    @ApiModelProperty(value = "标价")
    private BigDecimal tagPrice;
    public BigDecimal getTagPrice() {
        BigDecimal skuCount = getSkuCount();
        if(skuCount.compareTo(BigDecimal.ZERO) != 0){
            return  getEstimatedReceivedAmount().divide(skuCount, 2, RoundingMode.HALF_UP);
        }else {
            return BigDecimal.valueOf(0);
        }
    }
    @ApiModelProperty(value = "规格")
    private String specification;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "生产企业")
    private String manufacturer;

    //退货客流数量
    private int returnCustomerTrafficCount;
    //退货会员客流数量
    private int returnMemberTrafficCount;
    //退货客流数量
    private int returnPromotionCustomerTrafficCount;

    @ApiModelProperty(value = "全部客流数量")
    private BigDecimal allCustomerTrafficCount;

    @ApiModelProperty(value = "全部销售数量")
    private BigDecimal allSalesQuantity;

    @ApiModelProperty(value = "全部预估成本金额(改动过名称)")
    private BigDecimal allEstimatedCostAmount;

    @ApiModelProperty(value = "全部销售额)")
    private BigDecimal allSalesRevenue;

    @ApiModelProperty(value = "全部标价合计")
    private BigDecimal allReceivedAmount;

    /**
     * 查询条件 返回总数
     */
    private Long totalCount;


    public static void main(String[] args) {

        JSONObject commonResponseData = JSON.parseObject("{\"code\":\"success\",\"msg\":\"operate success\",\"data\":{\"result_0\":[{\"associatedSalesRevenue\":\"947.790\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"1457.540\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":11,\"associatedEstimatedCostAmount\":\"484.295\",\"estimatedCostAmount\":\"114.635\",\"promotionId\":13948625,\"returnCustomerTrafficCount\":0,\"estimatedReceivedAmount\":\"477.600\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"61.000\",\"salesAmount\":\"159.930\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":0,\"id\":13948625,\"customerTrafficCount\":11,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"24.000\"},{\"associatedSalesRevenue\":\"248.010\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"446.000\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":1,\"associatedEstimatedCostAmount\":\"267.600\",\"estimatedCostAmount\":\"148.800\",\"promotionId\":13948495,\"returnCustomerTrafficCount\":0,\"estimatedReceivedAmount\":\"248.000\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"2.000\",\"salesAmount\":\"248.000\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":0,\"id\":13948495,\"customerTrafficCount\":1,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"1.000\"},{\"associatedSalesRevenue\":\"49117.520\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"73045.770\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":299,\"associatedEstimatedCostAmount\":\"29274.049\",\"estimatedCostAmount\":\"13675.483\",\"promotionId\":13948481,\"returnCustomerTrafficCount\":14,\"estimatedReceivedAmount\":\"42683.300\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"2185.000\",\"salesAmount\":\"25764.050\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":14,\"id\":13948481,\"customerTrafficCount\":295,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"1055.000\"},{\"associatedSalesRevenue\":\"35.500\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"69.700\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":1,\"associatedEstimatedCostAmount\":\"11.803\",\"estimatedCostAmount\":\"10.001\",\"promotionId\":13948466,\"returnCustomerTrafficCount\":0,\"estimatedReceivedAmount\":\"59.800\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"3.000\",\"salesAmount\":\"25.600\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":0,\"id\":13948466,\"customerTrafficCount\":1,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"2.000\"},{\"associatedSalesRevenue\":\"20042.160\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"39730.580\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":278,\"associatedEstimatedCostAmount\":\"9681.164\",\"estimatedCostAmount\":\"3399.728\",\"promotionId\":13948449,\"returnCustomerTrafficCount\":7,\"estimatedReceivedAmount\":\"22767.000\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"1241.120\",\"salesAmount\":\"8552.550\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":7,\"id\":13948449,\"customerTrafficCount\":279,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"510.000\"},{\"associatedSalesRevenue\":\"255.000\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"275.000\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":2,\"associatedEstimatedCostAmount\":\"120.422\",\"estimatedCostAmount\":\"109.222\",\"promotionId\":13948421,\"returnCustomerTrafficCount\":0,\"estimatedReceivedAmount\":\"235.200\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"5.000\",\"salesAmount\":\"215.200\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":0,\"id\":13948421,\"customerTrafficCount\":2,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"4.000\"},{\"associatedSalesRevenue\":\"369.100\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"641.520\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":10,\"associatedEstimatedCostAmount\":\"162.045\",\"estimatedCostAmount\":\"23.095\",\"promotionId\":13948410,\"returnCustomerTrafficCount\":0,\"estimatedReceivedAmount\":\"115.200\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"50.050\",\"salesAmount\":\"51.470\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":0,\"id\":13948410,\"customerTrafficCount\":10,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"22.000\"},{\"associatedSalesRevenue\":\"301.800\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"745.800\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":4,\"associatedEstimatedCostAmount\":\"67.413\",\"estimatedCostAmount\":\"20.385\",\"promotionId\":13948408,\"returnCustomerTrafficCount\":0,\"estimatedReceivedAmount\":\"378.000\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"15.000\",\"salesAmount\":\"189.000\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":0,\"id\":13948408,\"customerTrafficCount\":4,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"8.000\"},{\"associatedSalesRevenue\":\"3557.380\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"10959.710\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":36,\"associatedEstimatedCostAmount\":\"1458.091\",\"estimatedCostAmount\":\"907.038\",\"promotionId\":13948273,\"returnCustomerTrafficCount\":1,\"estimatedReceivedAmount\":\"9204.000\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"243.000\",\"salesAmount\":\"2717.570\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":1,\"id\":13948273,\"customerTrafficCount\":36,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"156.000\"},{\"associatedSalesRevenue\":\"471.000\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"1167.000\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":2,\"associatedEstimatedCostAmount\":\"226.568\",\"estimatedCostAmount\":\"174.398\",\"promotionId\":13948271,\"returnCustomerTrafficCount\":0,\"estimatedReceivedAmount\":\"636.000\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"13.000\",\"salesAmount\":\"318.000\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":0,\"id\":13948271,\"customerTrafficCount\":2,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"4.000\"},{\"associatedSalesRevenue\":\"19.900\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"39.800\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":1,\"associatedEstimatedCostAmount\":\"8.662\",\"estimatedCostAmount\":\"8.662\",\"promotionId\":13948266,\"returnCustomerTrafficCount\":0,\"estimatedReceivedAmount\":\"39.800\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"2.000\",\"salesAmount\":\"19.900\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":0,\"id\":13948266,\"customerTrafficCount\":1,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"2.000\"},{\"associatedSalesRevenue\":\"154.610\",\"allCustomerTrafficCount\":16570,\"associatedReceivedAmount\":\"243.500\",\"allReceivedAmount\":\"1361856.150\",\"promotionCustomerTrafficCount\":1,\"associatedEstimatedCostAmount\":\"111.490\",\"estimatedCostAmount\":\"9.594\",\"promotionId\":13948265,\"returnCustomerTrafficCount\":0,\"estimatedReceivedAmount\":\"118.000\",\"allSalesQuantity\":\"63568.485\",\"associatedSalesQuantity\":\"8.000\",\"salesAmount\":\"55.420\",\"allSalesRevenue\":\"1011516.560\",\"allEstimatedCostAmount\":\"720323.039\",\"returnPromotionCustomerTrafficCount\":0,\"id\":13948265,\"customerTrafficCount\":1,\"promotionPlanCode\":\"1703748027494\",\"skuCount\":\"2.000\"}]}}");
        if(Objects.nonNull(commonResponseData.getJSONObject("data")) && Objects.nonNull(commonResponseData.getJSONObject("data").get("result_0"))) {
            JSONArray jsonArray = commonResponseData.getJSONObject("data").getJSONArray("result_0");
            if (Objects.nonNull(jsonArray) && !jsonArray.isEmpty()) {
                List<AnalysisStoreGoodsVo> listAll = jsonArray.toJavaList(AnalysisStoreGoodsVo.class);
                System.out.println("commonResponseData size=" + listAll.size());
                if (listAll.size() > 10) {
                    System.out.println("commonResponseData demo={}" + JSONObject.toJSONString(listAll.subList(0, 9)));
                } else {
                    System.out.println("commonResponseData demo={}" + JSONObject.toJSONString(listAll));
                }
            }
        }
    }
}
