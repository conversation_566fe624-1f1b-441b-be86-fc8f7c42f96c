package com.tomas.mybaties3.utils.vo;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class BDPCommonResponse<T> {

    private String code;
    private String msg;
    private Map<String, List<T>> data =Maps.newHashMap();
    private static final String successCode = "200";
    public static BDPCommonResponse success(){
        return new BDPCommonResponse("200", "成功");
    }
    public static BDPCommonResponse success(String message){
        return new BDPCommonResponse("200", message);
    }
    public static BDPCommonResponse fail(){
        return new BDPCommonResponse("1", "失败");
    }
    public static BDPCommonResponse fail(String message){
        return new BDPCommonResponse("1", message);
    }
    public BDPCommonResponse(String code, String message) {
        this.code = code;
        this.msg = message;
    }
    public static Boolean isSuccessCode(String code){
        return successCode.equals(code);
    }
    public Boolean isSuccess(){
        return BDPCommonResponse.successCode.equals(this.code);
    }
}
