
package com.tomas.mybaties3.utils.vo;

import org.apache.commons.lang.StringUtils;

/**
 * 养护单级别枚举类
 * <AUTHOR>
 */

public enum MaintenanceLevelEnum {

    NOMAINTENANCE(1,4182,"不养护"),
    EMPHASIS(2,4183,"重点养护"),
    COMMON(3,4184,"一般养护");

    private Integer code;
    private String name;
    private Integer source;

    MaintenanceLevelEnum(Integer code, Integer source, String name) {
        this.code = code;
        this.source = source;
        this.name = name;
    }

    public static String getName(Integer code){
        if(code == null) {
            return "";
        }
        for(MaintenanceLevelEnum maintenanceLevelEnum :values()){
            if(maintenanceLevelEnum.getCode().equals(code)){
                return maintenanceLevelEnum.getName();
            }
        }
        return "";
    }

    public static Integer getCode(String name){
        if(StringUtils.isBlank(name)) {
            return null;
        }
        for(MaintenanceLevelEnum maintenanceLevelEnum :values()){
            if(maintenanceLevelEnum.getName().equals(name)){
                return maintenanceLevelEnum.getCode();
            }
        }
        return null;
    }

    public static MaintenanceLevelEnum getSourceByCode(Integer code){
        if(code == null) {
            return null;
        }
        for(MaintenanceLevelEnum maintenanceLevelEnum :values()){
            if(maintenanceLevelEnum.getCode().equals(code)){
                return maintenanceLevelEnum;
            }
        }
        return null;
    }

    public static MaintenanceLevelEnum getSourceByName(String name){
        if(StringUtils.isBlank(name)) {
            return null;
        }
        for(MaintenanceLevelEnum maintenanceLevelEnum :values()){
            if(maintenanceLevelEnum.getName().equals(name)){
                return maintenanceLevelEnum;
            }
        }
        return null;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSource() {
        return source;
    }

    public void setSource(Integer source) {
        this.source = source;
    }

}
