package com.tomas.mybaties3.utils;

import com.alibaba.fastjson.JSONObject;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;

public class GJPOSTOnline {

    public static void main(String[] args) throws IOException, InterruptedException {
        String[] ids = "80258,80256,80257,80259,80263,82172,80264,80265,80266,80199,80200,80271,80273,80272,80274,80277,80278,80275,80276,80279,80281,80282,80284,80280,82217,80283,80285,80286,80287,81871,80312,20959,82256,82257,82258,82259,82260,82270,82271,82327,81805,81844,81862,81864,81865,81911,81912,81920,81913,74104,80005".split(","); // You can add more IDs here
        for (String id : ids) {
            JSONObject payload = new JSONObject();
            payload.put("bizCode", "ISCM");
            payload.put("bizType", "1002");
            payload.put("id", Long.parseLong(id));
            System.out.println("Sending request with payload: " + payload.toString());
            
            String response = sendPostRequest(
                "https://api-internal.gaojihealth.cn/tag-goods/api/optimize/storeComponent/delRule",
                payload.toString()
            );
            Thread.sleep(2000); // Wait for 2 seconds between requests
            System.out.println("Response: " + response);

        }
    }

    private static String sendPostRequest(String urlString, String jsonInputString) throws IOException {
        URL url = new URL(urlString);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setDoOutput(true);
        
        // Set all headers
        connection.setRequestProperty("authority", "api-internal.gaojihealth.cn");
        connection.setRequestProperty("pragma", "no-cache");
        connection.setRequestProperty("from_channel", "srm-web");
        connection.setRequestProperty("x-xsrf-token", "bb8ada00-0138-4540-80d8-55e9f8bb3cbf");
        connection.setRequestProperty("sec-ch-ua-mobile", "?0");
        connection.setRequestProperty("accept", "*/*");
        connection.setRequestProperty("content-type", "application/json");
        connection.setRequestProperty("user-agent", "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36");
        connection.setRequestProperty("client-id", "business-app");
        connection.setRequestProperty("origin", "https://api-internal.gaojihealth.cn");
        connection.setRequestProperty("sec-fetch-site", "same-origin");
        connection.setRequestProperty("sec-fetch-mode", "cors");
        connection.setRequestProperty("sec-fetch-dest", "empty");
        connection.setRequestProperty("referer", "https://api-internal.gaojihealth.cn/swagger-ui/index.html");
        connection.setRequestProperty("accept-language", "zh-CN,zh;q=0.9,en;q=0.8,fr;q=0.7");
        
        // Set cookie (Anonymized with 'x')
        String cookie = "sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%22191a385a6f72-0afee2f1764af9-********-2304000-191a385a6f8b9a%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%22191a385a6f72-0afee2f1764af9-********-2304000-191a385a6f8b9a%22%7D; login=; NW_U_T=%7B%22id%22%3A102%2C%22account%22%3A%*************%22%2C%22name%22%3A%22%E7%8E%8B%E4%BB%A3%E5%86%9B%22%2C%22avater%22%3Anull%2C%22phone%22%3A%*************%22%2C%22token%22%3A%226I5dFz5n45irmRGzeUpU8J0H1svTRmpqaU4UoRO2GW%2Bv72JsNvU7CIz3DC7w4dqwY558DtA0%2FgC%2FCAwumP5IFMO9mFCpdDcIdzPqKIcmpa2WhQVmdw562eV%2B2pOXJ0DeNyUo7e45Lv8jf3Oc2G8n4MzZgHuRKWe0wY%2BD7Wqdn3LuVMHz98O2PrdbJZTC%2Bb8%2Fkd6mBfKCBsZQFcetam5vdPVhNZ1BCnE58%2B44enw6%2Fjplk7arejVXaEwD9bm66%2Fs1RUBydhxfoJJS8GJXMntrwXLUiCe9kKAS1720INpOYPNezx7pf0ELwyVqqJGTT%2BISvTereczTNLUSuUptzCeSylIpKvO8jGLQRJOTQojtH7Y%3D%22%2C%22deptName%22%3Anull%2C%22deptId%22%3Anull%7D; gx_user_id=f98056780baf407e82343445e6570603; user_code=********; user_name=%E8%B7%AF%E6%98%8E%E8%85%BE; U_T=%7B%22token_type%22%3A%22bearer%22%2C%22expires_in%22%3A7198%2C%22scope%22%3A%22business-app%22%2C%22jti%22%3A%22ebb4408e-d373-444c-8595-78fa65bea97a%22%2C%22unionId%22%3Anull%2C%22platformBusinessId%22%3A193329%2C%22platformUserId%22%3A%*****************%22%2C%22loginName%22%3A%*************%22%2C%22loginCreatedDate%22%3A%222021-05-26%2018%3A32%3A33%22%2C%22roles%22%3A%22%5B%5C%22SZHSPGLY%5C%22%2C%5C%22GYLCSZY%5C%22%2C%5C%22DDMSHGLY%5C%22%2C%5C%22DDMJGS%5C%22%2C%5C%22JSWYH%5C%22%2C%5C%22DZ%5C%22%2C%5C%22ZHYXYYZY%5C%22%2C%5C%22JMGLY%5C%22%2C%5C%22MDDZ%5C%22%2C%5C%22GJTCSJS%5C%22%2C%5C%22APOLLOBUJGS%5C%22%2C%5C%22BIZWAREGL%5C%22%5D%22%2C%22businessId%22%3A%2278924%22%2C%22name%22%3A%22tomas%22%2C%22type%22%3A%221%22%2C%22userId%22%3A%*****************%22%2C%22status%22%3A%221%22%2C%22phone%22%3A%*************%22%2C%22sso%22%3A%22sso_test%22%7D; access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; session_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; XSRF-TOKEN=c361d7fc-3cdc-4c75-8551-feca4e53b383";
        connection.setRequestProperty("cookie", cookie);

        // Send request
        try (OutputStream os = connection.getOutputStream()) {
            byte[] input = jsonInputString.getBytes(StandardCharsets.UTF_8);
            os.write(input, 0, input.length);
        }

        // Read response
        StringBuilder response = new StringBuilder();
        try (BufferedReader br = new BufferedReader(new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
            String responseLine;
            while ((responseLine = br.readLine()) != null) {
                response.append(responseLine.trim());
            }
        }

        return response.toString();
    }
}