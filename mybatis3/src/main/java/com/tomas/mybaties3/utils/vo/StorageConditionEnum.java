package com.tomas.mybaties3.utils.vo;

/**
 * 养护单存储条件枚举类
 * <AUTHOR>
 */

public enum StorageConditionEnum {

    SHADY_COOL(1,"阴凉"),
    COLD(2,"冷藏"),
    NORMAL_TEMPERATURE(3,"常温"),
    FREEZE(4,"冷冻");

    private Integer code;
    private String name;

    StorageConditionEnum(Integer code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(Integer code){
        if(code == null) {
            return "";
        }
        for(StorageConditionEnum storageConditionEnum :values()){
            if(storageConditionEnum.getCode().equals(code)){
                return storageConditionEnum.getName();
            }
        }
        return "";
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
