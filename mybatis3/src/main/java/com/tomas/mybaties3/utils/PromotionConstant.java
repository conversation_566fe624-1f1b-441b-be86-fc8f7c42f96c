package com.tomas.mybaties3.utils;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONArray;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.util.List;

/**
 * @auther: jbliu
 * @date: 2021/1/9 16:52
 * @description:
 */
public class PromotionConstant {
    public static final Integer SIZE = 200;

    public static final Integer TOTAL=1000;
    /**
     * 回显
     */
    public static final Integer ORG_SHOW=1;

    /**
     * 不回显
     */
    public static final Integer ORG_SHOW_NON=2;
    /**
     * 状态：成功
     */
    public static final Integer STATUS_NORMAL=0;
    /**
     * 状态：失败
     */
    public static final Integer STATUS_ABNORMAL=-1;
    /**
     * 成功
     */
    public static final String SUCCESS = "成功";
    /**
     * 失败
     */
    public static final String FAIL = "失败";

    public static final Integer THREE_HUNDRED=300;

    public static final Integer FIVE_THOUSAND=5000;

    public static final Integer TEN_THOUSAND=10000;

    /**
     * 否
     */
    public static final Byte BYTE_ZERO = 0;

    /**
     * 是
     */
    public static final Byte BYTE_ONE = 1;
    /**
     * 初始版本号
     */
    public static final Integer VERSION = 1;
    //连锁品牌-500
    public static final Integer PERMISSION_ORG_TYPE_BUSINESS = 500;
    //连锁片区-700
    public static final Integer PERMISSION_ORG_TYPE_DISTRICT = 700;
    //平台
    public static final Integer PERMISSION_ORG_TYPE_PLATFORM = 600;
    //门店-800
    public static final Integer PERMISSION_ORG_TYPE_STORE = 800;
    //促销缓存信息key
    public static final String BUSINESS_PROMOTION_CACHE_KEY_ = "BUSINESS_PROMOTION_CACHE_KEY_";
    //促销缓存信息key
    public static final String BUSINESS_PROMOTION_LIST_CACHE_KEY_ = "BUSINESS_PROMOTION_LIST_CACHE_KEY_";

    // 初始页码 1
    public static final int INIT_PAGE = 1;
    // 默认每页条数 10
    public static final int DEFALT_SIZE = 10;

    /**
     * 平台类型 1-商家中心
     */
    public static final int PLATFORM_TYPE = 1;
    /**
     * 平台类型 2-智能管理平台
     */
    public static final int PLATFORM_TYPE_C_MALL = 2;
    /**
     * 状态 1-下载中
     */
    public static final int FILE_DOWNLOAD_STATUS = 1;


    public static final int FILE_PLACE = 1;

    /**
     * 文件下载状态： 2-成功
     */
    public static final Integer FILE_DOWN_STATUS_SUCCESS = 2;

    /**
     * 文件下载状态： 3-失败
     */
    public static final  Integer FILE_DOWN_STATUS_FAIL = 3;
    /**
     * 没有合并行时 标题在第一行  TITLE_ROW_NUMBER=0
     */
    public static final int GOODS_IMPORT_DEFAULT_TITLE_ROW_NUMBER_ZERO=0;

    /**
     * 表头有合并行  标题行在第二行  TITLE_ROW_NUMBER=1
     */
    public static final int GOODS_IMPORT_DEFAULT_TITLE_ROW_NUMBER_ONE=1;

    /**
     * easyExcel 表格第二行
     */
    public static final int GOODS_IMPORT_DEFAULT_TITLE_ROW_NUMBER_TWO=2;

    /**
     * 波浪线
     */
    public static final String WAVELINE ="~";

    /**
     * 核心线程数据量
     */
    public static final Integer COREPOOLSIZE = 5;

    /**
     * 最大线程数量
     */
    public static final Integer MAXPOOLSIZE = 50;

    /**
     * 100
     */
    public static final Integer ONE_HUNDRED=100;


    /**
     * 数字常量
     */
    public static final Integer ZERO = 0;

    public static final Integer ONE = 1;

    public static final Integer TWO = 2;

    public static final Integer THREE = 3;

    public static final Integer FOUR = 4;

    public static final Integer FIVE = 5;

    public static final Integer SIX = 6;

    public static final Integer SEVEN=7;

    public static final Integer EIGHT = 8;

    public static final Integer NINE=9;

    public static final Integer TEN=10;

    public static final Integer ELEVEN=11;

    public static final Integer TWELVE=12;

    public static final Integer THIRTEEN=13;

    public static final Integer FOURTEEN=14;

    public static final Integer FIFTEEN=15;

    public static final Integer SIXTEEN=16;

    public static final Integer SEVENTEEN=17;

    public static final Integer BURDEN_ONE=-1;

    public static final String MARKETING_PROMOTION_LOCK_KEY = "MARKETING_PROMOTION_LOCK_KEY_";

    public static final Long REDIS_LEASE_TIME = -1L;

    /**
     * 赠品池大小
     */
    public static final Integer ONCE_BATCH_GIFTPOOL_MAX =800;
    /**
     * 一次批量插入数量
     */
    public static final Integer ONCE_BATCH_INSERT=1000;


    /**
     * 一次批量删除数量
     */
    public static final Integer ONCE_BATCH_DELETE=5000;

    /**
     * 毛利率默认值 -9999
     */
    public static final BigDecimal DEFULT_GROSS_PROFIT_RATE=new BigDecimal(-9999.0000);

    /**
     * 赠品排序默认值 9999
     */
    public static final Integer DEFULT_BUSI_CODE =9999;

    /**
     * 农历日期 list
     */
    public static final List<String> CHINESE_DAY_LIST = JSONArray.parseArray("[\"初一\",\"初二\",\"初三\",\"初四\",\"初五\",\"初六\",\"初七\",\"初八\",\"初九\",\"初十\",\"十一\",\"十二\",\"十三\",\"十四\",\"十五\",\"十六\",\"十七\",\"十八\",\"十九\",\"二十\",\"廿一\",\"廿二\",\"廿三\",\"廿四\",\"廿五\",\"廿六\",\"廿七\",\"廿八\",\"廿九\",\"三十\"]", String.class);


    public static final BigDecimal BIG_DECIMAL_BURDEN_ONE = new BigDecimal(-1);
    /**
     * 优惠券消费提示
     */
    public static final String COMSUM_MESSAGE = "使用时请出示会员码";
    /**
     * 优惠券 优惠说明
     */
    public static final String DISCOUNT_EXPLAIN = "详见使用须知";

    /**
     * 英文逗号
     */
    public static final String ENG_COMMA=",";

    /**
     * 英文分号
     */
    public static final String ENG_SEMICOLON=";";

    public static final int[] businessAndStoreTypes = {PERMISSION_ORG_TYPE_BUSINESS, PERMISSION_ORG_TYPE_DISTRICT, PERMISSION_ORG_TYPE_STORE};


    public static void main(String[] args) throws FileNotFoundException {
//        BigExcelWriter writer = null;
//        File file = null;
//        try {
//            Path tempFile = Files.createTempFile("export_", ".xlsx");
//            file = tempFile.toFile();
//
//            // 确保文件已经被成功创建
//            if (!file.exists()) {
//                throw new IOException("Failed to create temp file");
//            }
//
//            writer = ExcelUtil.getBigWriter(file);
//
//            // 在此处添加写入逻辑，确保在此之后文件不为空
//            // 示例：写入标题
//            writer.writeRow(Arrays.asList("Column1", "Column2", "Column3"));
//
//            // 结束写入并刷新到文件
//            writer.flush();
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            // 关闭 BigExcelWriter 以确保资源释放
//            if (writer != null) {
//                writer.close();
//            }
//        }
        ExcelWriter writer = null;
        File file = null;
//
//        try {
//            // 创建临时文件
//            Path tempFile = Files.createTempFile("export_", ".xlsx");
//            file = tempFile.toFile();
//
//            System.out.println("Temp file created at: " + file.getAbsolutePath());
//
//            // 检查文件是否成功创建
//            if (file.length() == 0) {
//                System.out.println("Temp file is initially empty as expected.");
//            }
//
//            // 创建 BigExcelWriter 并使用 SXSSFWorkbook 以支持大数据量
//            writer = ExcelUtil.getBigWriter(file);
//            //writer.getWorkbook().setCompressTempFiles(true); // 启用压缩，减少内存占用
//
//            // 写入表头
//            writer.writeRow(Arrays.asList("Column1", "Column2", "Column3"));
//
//            // 写入数据
//            int totalRows = 500000;
//            int batchSize = 10000; // 每次写入1万行
//
//            for (int i = 0; i < totalRows; i++) {
//                writer.writeRow(Arrays.asList("Data" + i, "Data" + i, "Data" + i));
//
//                if (i % batchSize == 0) {
//                    System.out.println("Flushing data at row: " + i);
//                    writer.flush(); // 每1万行刷一次数据到磁盘
//                }
//            }
//
//            // 确保数据最终被写入
//            writer.flush();
//            System.out.println("Final flush complete.");
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        } finally {
//            // 确保资源被正确释放
//            if (writer != null) {
//                writer.close();
//                System.out.println("Writer closed.");
//            }
//        }
//
//        // 检查最终文件大小
//        if (file != null && file.length() > 0) {
//            System.out.println("Excel file created successfully, file size: " + file.length() + " bytes.");
//        } else {
//            System.out.println("Excel file creation failed, file is still empty.");
//        }



//        // 1. 创建数据源，这里假设我们有 50 万条数据
//        List<List<String>> rows = new ArrayList<>();
//        for (int i = 0; i < 500000; i++) {
//            List<String> row = new ArrayList<>();
//            row.add("Name_" + i);
//            row.add("Value_" + i);
//            rows.add(row);
//        }
//
//        // 2. 使用 ExcelUtil.getBigWriter 创建 BigExcelWriter 对象
//         writer = ExcelUtil.getBigWriter("/var/folders/4t/ghnb_8td1l3d6p07zf2lm3wr0000gp/T/export_856531217824424463.xlsx");
//
//        // 3. 将数据写入到 Excel
//        writer.write(rows);
//
//        // 4. 关闭 writer，释放资源
//        writer.close();

        List<?> row1 = CollUtil.newArrayList("aa", "bb", "cc", "dd", DateUtil.date(), 3.22676575765);
        List<?> row2 = CollUtil.newArrayList("aa1", "bb1", "cc1", "dd1", DateUtil.date(), 250.7676);
        List<?> row3 = CollUtil.newArrayList("aa2", "bb2", "cc2", "dd2", DateUtil.date(), 0.111);
        List<?> row4 = CollUtil.newArrayList("aa3", "bb3", "cc3", "dd3", DateUtil.date(), 35);
        List<?> row5 = CollUtil.newArrayList("aa4", "bb4", "cc4", "dd4", DateUtil.date(), 28.00);

        List<List<?>> rows = CollUtil.newArrayList(row1, row2, row3, row4, row5);
        FileOutputStream out = new FileOutputStream("/var/folders/4t/ghnb_8td1l3d6p07zf2lm3wr0000gp/T/export_856531217824424463.xlsx");
         writer = ExcelUtil.getBigWriter();// 一次性写出内容，使用默认样式
        writer.write(rows);
        writer.flush(out, true);
// 关闭writer，释放内存
        writer.close();


    }




}
