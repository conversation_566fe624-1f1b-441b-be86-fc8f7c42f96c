package com.tomas.mybaties3.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.protocol.HttpClientContext;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * Test 描述
 *
 * <AUTHOR>
 * @create 2021/7/28
 **/
public class GJGETOnline {
    public static void main(String[] args) throws IOException, InterruptedException {


       /*
*/

///2023-03-24,2023-03-23,2023-03-22,2023-03-21,2023-03-20,,2023-02-26,2023-02-25,2023-02-24,2023-02-23
 //2023-03-19,2023-03-18,2023-03-17,2023-03-16,2023-03-15,2023-03-14,2023-03-13,2023-03-12,2023-03-11,2023-03-10,2023-03-09,2023-03-08,2023-03-07,2023-03-06,2023-03-05,2023-03-04,2023-03-03,2023-03-02,2023-03-01,2023-02-28,2023-02-27
        List<String > list=new ArrayList<>();

    String text=
            "13940325,13951637,13952551,14071607,14072504,13977196,14016780,14016784,14072506,14129901,14158453,14158970,14162901,14167530,14170366,14170370,14179455,14179457,14179461,14179464,14194860,14194865,14200487,14200490,14200494,14200446,14200450,14200453,14200455,14207793,14207794,14207795,14207796,14219267,14219268,14219271,14219272,14203029,14203031,14204188,14203591,14203593,14203970,14203978,14203979,14204132,14204184,14199055,14202649,14202669,14202692,14207162,14207380,14207381,14203590,14203971,14203980,14203981,14205903,14205909,14205920,14205919,14205915,14205918,14205916,14205911,14205914,14205906,14205910,14205908,14205907,14205905,14205904,14205902,14205932,14205937,14205941,14205942,14205940,14205939,14205938,14205936,14205935,14205934,14205933,14205930,14205931,14205929,14205928,14140204,14199056,14202666,14202670,14202697,14208712,14210243,14217238,14225865,14225868,14225870,14211655,14211794,14211796,14211804,14216408,14217683,14219135,14219133,14222583,14222694,14225863,14227128,14222065,14222066,14222067,14220962,14221229,14221227,14221228,14223137,14222735,14222736,14222737,14222738,14223135,14227189,14227192,14227193,14227194,14223136,14222724,14226574,14223111,14222209,14222217,14222234,14222242,14225628,14225966,14225969,14225973,14225975,14225987,14225997,14225998,14225999,14226000,14226001,14226008,14226009,14226010,14226011,14226012,14224466,14225617,14225618,14225627,14225630,14225724,14225856,14225864,14225873,14226296,14226309,14226332,14226344,14226351,14226358,14225991,14225996,14226021,14226022,14226024,14226026,14226029,14226030,14227418,14227416,14192917,14192919,14192921,14219391,14221116,14221127,14221131,14222210,14222206,14224479,14218063,14221387,14221388,14221464,14222642,14222643,14224477,14224722,14224725,14227167,14227557,14227565,14218064,14221389,14221390,14222641,14222648,14226299,14226300,14226301,14226326,14226327,14226328,14226335,14226340,14226341,14221554,14225837,14225839,14225881,14225883,14225884,14220948,14220954,14215004,14215011,14215016,14215019,14215031,14226133,14226187,14226202,14227007,14174783,14174788,14174789,14174790,14174792,14174795,14179433,14179504,14179510,14179561,14180483,14180484,14182273,14182288,14208098,14218918,14226416,14224947,14224948,14224949,14224950,14227541,14227544,14227547,14124989,14124881";
        //",13952551,14071607,14072504,13977196,14016780,14016784,14072506,14129901,14158453,14158970,14162901,14167530,14170366,14170370,14179455,14179457,14179461,14179464,14194860,14194865,14200487,14200490,14200494,14200446,14200450,14200453,14200455,14207793,14207794,14207795,14207796,14219267,14219268,14219271,14219272,14203029,14203031,14204188,14203591,14203593,14203970,14203978,14203979,14204132,14204184,14199055,14202649,14202669,14202692,14207162,14207380,14207381,14203590,14203971,14203980,14203981,14205903,14205909,14205920,14205919,14205915,14205918,14205916,14205911,14205914,14205906,14205910,14205908,14205907,14205905,14205904,14205902,14205932,14205937,14205941,14205942,14205940,14205939,14205938,14205936,14205935,14205934,14205933,14205930,14205931,14205929,14205928,14140204,14199056,14202666,14202670,14202697,14208712,14210243,14217238,14225865,14225868,14225870,14211655,14211794,14211796,14211804,14216408,14217683,14219135,14219133,14222583,14222694,14225863,14227128,14222065,14222066,14222067,14220962,14221229,14221227,14221228,14223137,14222735,14222736,14222737,14222738,14223135,14227189,14227192,14227193,14227194,14223136,14222724,14226574,14223111,14222209,14222217,14222234,14222242,14225628,14225966,14225969,14225973,14225975,14225987,14225997,14225998,14225999,14226000,14226001,14226008,14226009,14226010,14226011,14226012,14224466,14225617,14225618,14225627,14225630,14225724,14225856,14225864,14225873,14226296,14226309,14226332,14226344,14226351,14226358,14225991,14225996,14226021,14226022,14226024,14226026,14226029,14226030,14227418,14227416,14192917,14192919,14192921,14219391,14221116,14221127,14221131,14222210,14222206,14224479,14218063,14221387,14221388,14221464,14222642,14222643,14224477,14224722,14224725,14227167,14227557,14227565,14218064,14221389,14221390,14222641,14222648,14226299,14226300,14226301,14226326,14226327,14226328,14226335,14226340,14226341,14221554,14225837,14225839,14225881,14225883,14225884,14220948,14220954";
   //     String text= "120220824142242917860027336,120220824142243440937027336";
    String[] split = text.split(",");
        //System.out.println(split.length);
        for (String s : split) {
        //  System.out.println(s);authority: api-store.gaojihealth.cn
        String[] cmds = {"curl", "-X","GET",  "-H", "authority: api-internal.gaojihealth.cn",
                "-H", "pragma: no-cache",
                "-H","from_channel: srm-web" ,
                "-H","x-xsrf-token: b75ac96d-402e-496a-82f1-4d159ecbcea8",
                "-H","sec-ch-ua-mobile: ?0" ,
               // "-H","authorization: bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" ,
                "-H","accept: */*" ,
                "-H","content-type: application/json" ,

                "-H","user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36" ,
                "-H","client-id: business-app" ,
                "-H","origin: https://api-internal.gaojihealth.cn" ,
                "-H","sec-fetch-site: same-origin" ,
                "-H","sec-fetch-mode: cors" ,
                "-H","sec-fetch-dest: empty" ,
                "-H","referer: https://api-internal.gaojihealth.cn/swagger-ui/index.html" ,
                "-H","accept-language: zh-CN,zh;q=0.9,en;q=0.8,fr;q=0.7" ,
                "-H","cookie: bug_user_account=; login=; NW_U_T=%7B%22id%22%3A102%2C%22account%22%3A%*************%22%2C%22name%22%3A%22%E7%8E%8B%E4%BB%A3%E5%86%9B%22%2C%22avater%22%3Anull%2C%22phone%22%3A%*************%22%2C%22token%22%3A%226I5dFz5n45irmRGzeUpU8J0H1svTRmpqaU4UoRO2GW%2Bv72JsNvU7CIz3DC7w4dqwY558DtA0%2FgC%2FCAwumP5IFFx%2FHnAxJTq2QP2jsIGDchGHHWsSB83TNWa81hG8xiG5NyUo7e45Lv8jf3Oc2G8n4MzZgHuRKWe0wY%2BD7Wqdn3J%2F1JiuH0tLSn8aMPZEpe%2BOkd6mBfKCBsZQFcetam5vdBLlMWEUz3FO6yI7MMlQjlYrrQUUe5js%2FXSZ40lIvXRQRUBydhxfoJJS8GJXMntrwbpsVo7QnwJMKgVNgPxb89cbMMzxb7cV6k6e6s%2BftabH53Ub4CFom3lhoWEs8mbcs%2FgpQjH3HCbHRmICkMTR%2Bg8%3D%22%2C%22deptName%22%3Anull%2C%22deptId%22%3Anull%7D; U_T=%7B%22token_type%22%3A%22bearer%22%2C%22expires_in%22%3A7198%2C%22scope%22%3A%22business-app%22%2C%22jti%22%3A%22b9374408-fd8c-404c-9a1e-8775aaeba0c9%22%2C%22unionId%22%3Anull%2C%22platformBusinessId%22%3A193329%2C%22platformUserId%22%3A%*****************%22%2C%22loginName%22%3A%*************%22%2C%22loginCreatedDate%22%3A%222021-05-26%2018%3A32%3A33%22%2C%22roles%22%3A%22%5B%5C%22SZHSPGLY%5C%22%2C%5C%22DDMSHGLY%5C%22%2C%5C%22DDMJGS%5C%22%2C%5C%22ZHGYLGLY%5C%22%2C%5C%22JSWYH%5C%22%2C%5C%22DZ%5C%22%2C%5C%22ZHYXYYZY%5C%22%2C%5C%22JMGLY%5C%22%2C%5C%22GJTCSJS%5C%22%2C%5C%22APOLLOBUJGS%5C%22%2C%5C%22BIZWAREGL%5C%22%5D%22%2C%22businessId%22%3A%2299999%22%2C%22name%22%3A%22tomas%22%2C%22type%22%3A%221%22%2C%22userId%22%3A%*****************%22%2C%22status%22%3A%221%22%2C%22phone%22%3A%*************%22%2C%22sso%22%3A%22sso_test%22%7D; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%*****************%22%2C%22first_id%22%3A%2219370a058962fdb-09b1a326d6c5eb-1e525636-2104200-19370a058973281%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkzNzBhMDU4OTYyZmRiLTA5YjFhMzI2ZDZjNWViLTFlNTI1NjM2LTIxMDQyMDAtMTkzNzBhMDU4OTczMjgxIiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiMTkzOTU4ODU5NDEyNTAxIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%*****************%22%7D%2C%22%24device_id%22%3A%221937785ab94a5d-04994676f9887e-1e525636-2104200-1937785ab95497d%22%7D; access_token=*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; session_token=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; XSRF-TOKEN=9bf4ccaf-1d37-447c-9a2d-ee609a44c553" ,
               // "--data-raw", s,
                "--compressed", "https://api-internal.gaojihealth.cn/marketing-sync/api/promotion/cache/util/getCacheByPromotionId?promotionId="+s
        };

//            System.out.println(JSON.toJSONString(cmds));
//            String s1 = execCurl(cmds);
//            System.out.println(s1);
//            JSONArray objects = JSON.parseArray(s1);
//            JSONObject object = JSON.parseObject(objects.get(0).toString());
//            JSONObject data = object.getJSONObject("promotion");
////            List<NewSpuVo> rows = JSONArray.parseArray(data.getString("rows"), NewSpuVo.class);
//
//            System.out.println(JSON.toJSONString(data));
//            System.out.println( data.getInteger("multiExcludeGoodsFlag"))
           // MaintenanceLevelEnum sourceByCode = MaintenanceLevelEnum.getSourceByCode(3);
            list.add(execCurl(s)) ;
         ;

//            rows.stream().forEach(v->{
//                System.out.println(StorageConditionEnum.getName(3).equals(v.getStoragecond()) &&  sourceByCode.getSource().equals(v.getMainten_type()));
//            });
//            List<NewSpuVo> needCheckList = rows.stream().filter(v ->  StorageConditionEnum.getName(3).equals(v.getStoragecond()) && null != sourceByCode && sourceByCode.getSource().equals(v.getMainten_type())).collect(Collectors.toList());
//            if(CollectionUtils.isEmpty(needCheckList)){
//                System.out.println("没有满足条件的商品");
//                continue;
//            }
 //           System.out.println(JSON.toJSONString(needCheckList));
        }

        list.stream().forEach(v->{
            System.out.println(v);
        });
    }



    public static String execCurl(String s) {

        String urlString = "https://api-internal.gaojihealth.cn/marketing-sync/api/promotion/cache/util/getCacheByPromotionId?promotionId="+s; // 替换为您的URL
        String cookie = "cookie=bug_user_account=; login=; NW_U_T=%7B%22id%22%3A102%2C%22account%22%3A%*************%22%2C%22name%22%3A%22%E7%8E%8B%E4%BB%A3%E5%86%9B%22%2C%22avater%22%3Anull%2C%22phone%22%3A%*************%22%2C%22token%22%3A%226I5dFz5n45irmRGzeUpU8J0H1svTRmpqaU4UoRO2GW%2Bv72JsNvU7CIz3DC7w4dqwY558DtA0%2FgC%2FCAwumP5IFFx%2FHnAxJTq2QP2jsIGDchGHHWsSB83TNWa81hG8xiG5NyUo7e45Lv8jf3Oc2G8n4MzZgHuRKWe0wY%2BD7Wqdn3J%2F1JiuH0tLSn8aMPZEpe%2BOkd6mBfKCBsZQFcetam5vdBLlMWEUz3FO6yI7MMlQjlYrrQUUe5js%2FXSZ40lIvXRQRUBydhxfoJJS8GJXMntrwbpsVo7QnwJMKgVNgPxb89cbMMzxb7cV6k6e6s%2BftabH53Ub4CFom3lhoWEs8mbcs%2FgpQjH3HCbHRmICkMTR%2Bg8%3D%22%2C%22deptName%22%3Anull%2C%22deptId%22%3Anull%7D; U_T=%7B%22token_type%22%3A%22bearer%22%2C%22expires_in%22%3A7198%2C%22scope%22%3A%22business-app%22%2C%22jti%22%3A%22b9374408-fd8c-404c-9a1e-8775aaeba0c9%22%2C%22unionId%22%3Anull%2C%22platformBusinessId%22%3A193329%2C%22platformUserId%22%3A%*****************%22%2C%22loginName%22%3A%*************%22%2C%22loginCreatedDate%22%3A%222021-05-26%2018%3A32%3A33%22%2C%22roles%22%3A%22%5B%5C%22SZHSPGLY%5C%22%2C%5C%22DDMSHGLY%5C%22%2C%5C%22DDMJGS%5C%22%2C%5C%22ZHGYLGLY%5C%22%2C%5C%22JSWYH%5C%22%2C%5C%22DZ%5C%22%2C%5C%22ZHYXYYZY%5C%22%2C%5C%22JMGLY%5C%22%2C%5C%22GJTCSJS%5C%22%2C%5C%22APOLLOBUJGS%5C%22%2C%5C%22BIZWAREGL%5C%22%5D%22%2C%22businessId%22%3A%2299999%22%2C%22name%22%3A%22tomas%22%2C%22type%22%3A%221%22%2C%22userId%22%3A%*****************%22%2C%22status%22%3A%221%22%2C%22phone%22%3A%*************%22%2C%22sso%22%3A%22sso_test%22%7D; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%*****************%22%2C%22first_id%22%3A%2219370a058962fdb-09b1a326d6c5eb-1e525636-2104200-19370a058973281%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTkzNzBhMDU4OTYyZmRiLTA5YjFhMzI2ZDZjNWViLTFlNTI1NjM2LTIxMDQyMDAtMTkzNzBhMDU4OTczMjgxIiwiJGlkZW50aXR5X2xvZ2luX2lkIjoiMTkzOTU4ODU5NDEyNTAxIn0%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%*****************%22%7D%2C%22%24device_id%22%3A%221937785ab94a5d-04994676f9887e-1e525636-2104200-1937785ab95497d%22%7D; access_token=*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; session_token=************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; XSRF-TOKEN=9bf4ccaf-1d37-447c-9a2d-ee609a44c553"; // 替换为您的Cookie字符串

        try (CloseableHttpClient httpClient = HttpClients.createDefault()) {
            HttpGet httpGet = new HttpGet(urlString);
            httpGet.setHeader("Cookie", cookie);
            HttpClientContext context = HttpClientContext.create();
            HttpResponse response = httpClient.execute(httpGet, context);
            int statusCode = response.getStatusLine().getStatusCode();
            //System.out.println("Response Status Code: " + statusCode);
            // 获取响应体
            String responseBody = EntityUtils.toString(response.getEntity());
           // System.out.println(responseBody);
            JSONArray objects = JSON.parseArray(responseBody);
            com.alibaba.fastjson.JSONObject object = JSON.parseObject(objects.get(0).toString());
            JSONObject data = object.getJSONObject("promotion");
           //System.out.println(JSON.toJSONString(data));
            System.out.println(s+":"+ data.getInteger("multiExcludeGoodsFlag"));
            return s+":"+ data.getInteger("multiExcludeGoodsFlag");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

}
