package com.tomas.mybaties3.utils;

import cn.hutool.core.codec.Base62;
import cn.hutool.crypto.digest.DigestUtil;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;

/**
 * 短密文加盐加密工具类
 * 针对密文长度优化的加密方案
 */
public class CompactSaltedEncryption {

    // 密钥
    private static final String RSA_KEYPAIR="RSA-PURCHASE-DATA";

    /**
     * 使用时间戳作为盐值种子的AES加密（推荐）
     * @param plaintext 明文
     * @return 极短的加密字符串（Base62编码）
     */
    public static String encryptCompact(String plaintext) {
        try {
            // 使用当前时间的小时作为盐值种子（可根据需要调整精度）
            long timeHour = System.currentTimeMillis() / (1000 * 60 * 60);
            String salt = String.valueOf(timeHour);

            // 生成密钥
            String keyString = DigestUtil.sha256Hex(RSA_KEYPAIR + salt);
            byte[] key = keyString.substring(0, 16).getBytes(StandardCharsets.UTF_8);

            // AES加密（ECB模式，无需IV，更短）
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
            cipher.init(Cipher.ENCRYPT_MODE, secretKey);
            byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

            // 使用Base62编码（比Base64更短）
            return Base62.encode(encrypted);

        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }

    /**
     * 对应的解密方法
     */
    public static String decryptCompact(String encryptedText) {
        try {
            // 尝试最近几个小时的时间戳作为盐值
            for (int i = 0; i <= 2; i++) {
                long timeHour = System.currentTimeMillis() / (1000 * 60 * 60) - i;
                String salt = String.valueOf(timeHour);

                try {
                    String keyString = DigestUtil.sha256Hex(RSA_KEYPAIR + salt);
                    byte[] key = keyString.substring(0, 16).getBytes(StandardCharsets.UTF_8);

                    byte[] encrypted = Base62.decode(encryptedText.getBytes());

                    Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
                    SecretKeySpec secretKey = new SecretKeySpec(key, "AES");
                    cipher.init(Cipher.DECRYPT_MODE, secretKey);
                    byte[] decrypted = cipher.doFinal(encrypted);

                    return new String(decrypted, StandardCharsets.UTF_8);

                } catch (Exception ignored) {
                    // 尝试下一个时间戳
                }
            }
            throw new RuntimeException("解密失败");

        } catch (Exception e) {
            throw new RuntimeException("解密失败", e);
        }
    }
    
    /**
     * 测试和对比各种方案
     */
    public static void main(String[] args) {
        String plaintext = "Hello World! 你好世界!";
        String password = "myPassword123";
        
        System.out.println("原文: " + plaintext);
        System.out.println("原文长度: " + plaintext.length());
        System.out.println("原文字节数: " + plaintext.getBytes(StandardCharsets.UTF_8).length);
        System.out.println();
        
        // 测试方案一：内嵌盐值AES
        System.out.println("=== 方案一：内嵌盐值AES（最推荐）===");
        String encrypted1 = encryptCompact(plaintext);
        System.out.println("加密后: " + encrypted1);
        System.out.println("密文长度: " + encrypted1.length());
        String decrypted1 = decryptCompact(encrypted1);
        System.out.println("解密后: " + decrypted1);
        System.out.println("解密成功: " + plaintext.equals(decrypted1));
        System.out.println();

        
        System.out.println("\n=== 长度对比总结 ===");
        System.out.println("原文长度: " + plaintext.length());
        System.out.println("方案一长度: " + encrypted1.length() + " (压缩比: " + String.format("%.1f", (double)encrypted1.length()/plaintext.length()) + ")");
    }
}