package com.tomas.mybaties3.utils.vo;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONObject;
import com.tomas.mybaties3.entity.PayChannelDetail;
import com.tomas.mybaties3.entity.PosSaleOrderDetailDTO;
import com.tomas.mybaties3.entity.VoteRecordOrder;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;

/**
 * Test 描述
 *
 * <AUTHOR>
 * @create 2021/7/28
 **/
public class GJPostOnlineOrder {
    public static void main(String[] args) throws IOException, InterruptedException {

        File file=new File("/Users/<USER>/Desktop/pos20230328.xlsx");
        List<VoteRecordOrder> rows =new ArrayList<>();
        ExcelReader excelReader = ExcelUtil.getReader(file);
        //2.第二种导入方式
        //忽略第一行头(第一行是中文的情况),直接读取表的内容
        List<List<Object>> list = excelReader.read(1);
        //List<VoteRecordCar> listUser = CollUtil.newArrayList();
        int i = 0;
        for (List<Object> row: list) {
            VoteRecordOrder user=new VoteRecordOrder();
            user.setDateString(row.get(0).toString());
            user.setOrderNo(row.get(1).toString());
            user.setJine(row.get(2).toString());
            user.setPosOrderNo(row.get(3).toString());
            System.out.println(row.get(3).toString()+"---"+i++);
            if (i <=10){
                if(StringUtils.isNotBlank(user.getOrderNo())){
                    try {
                        VoteRecordOrder order = fildVoteRecordCar(user);
                        if (order != null) {
                            rows.add(order);
                        }
                    }catch (Exception e){
                        e.printStackTrace();
                    }

                }
                ThreadUtil.sleep(1000L);
            }
        }
        ExcelWriter writer = ExcelUtil.getWriter("/Users/<USER>/Desktop/pos20230328Error"+System.currentTimeMillis()+".xlsx");
        //writer.merge(2, "一班成绩单");
        writer.write(rows, true);
        writer.close();

    }



    public static VoteRecordOrder fildVoteRecordCar( VoteRecordOrder user){
            //  System.out.println(s);
            JSONObject a=new JSONObject();
            //{\n  "businessId": 573208,\n  "orderType": 3,\n  "page": 0,\n  "pageSize": 20,\n  "": "230227-134165328762260"\n}


        String[] cmds = {"curl", "-X","GET",  "-H", "authority: api-internal.gaojihealth.cn",
                "-H", "pragma: no-cache",
                "-H","from_channel: srm-web" ,
                "-H","x-xsrf-token: b75ac96d-402e-496a-82f1-4d159ecbcea8",
                "-H","sec-ch-ua-mobile: ?0" ,
                // "-H","authorization: bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************" ,
                "-H","accept: */*" ,
                "-H","content-type: application/json" ,

                "-H","user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_12_6) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.77 Safari/537.36" ,
                "-H","client-id: business-app" ,
                "-H","origin: https://api-internal.gaojihealth.cn" ,
                "-H","sec-fetch-site: same-origin" ,
                "-H","sec-fetch-mode: cors" ,
                "-H","sec-fetch-dest: empty" ,
                "-H","referer: https://api-internal.gaojihealth.cn/swagger-ui/index.html" ,
                "-H","accept-language: zh-CN,zh;q=0.9,en;q=0.8,fr;q=0.7" ,
                "-H","cookie: _ati=3845830197176; _ga=GA1.2.527327123.**********; gr_user_id=a89cdcc1-79f2-4cba-95cb-ac00d671857c; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2217bee005330270-0eedbe557db7e9-1f3f6750-2304000-17bee005331865%22%2C%22first_id%22%3A%22%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%7D%2C%22%24device_id%22%3A%2217bee005330270-0eedbe557db7e9-1f3f6750-2304000-17bee005331865%22%7D; U_T=%7B%22token_type%22%3A%22bearer%22%2C%22expires_in%22%3A7198%2C%22scope%22%3A%22business-app%22%2C%22jti%22%3A%22c97ca346-0117-4b20-8386-d9bf7fd6a89b%22%2C%22unionId%22%3Anull%2C%22platformBusinessId%22%3A193329%2C%22phone%22%3A%2218511256591%22%2C%22loginName%22%3A%2218511256591%22%2C%22loginCreatedDate%22%3A%222021-05-26%2018%3A32%3A33%22%2C%22roles%22%3A%22%5B%5C%22DDJGK%5C%22%2C%5C%22DDMSHGLY%5C%22%2C%5C%22YPOS%5C%22%2C%5C%22YDPOSYPOS%5C%22%2C%5C%22ZHGYLGLY%5C%22%2C%5C%22JSWYH%5C%22%2C%5C%22DZ%5C%22%2C%5C%22YXZY%5C%22%2C%5C%22DDJZY%5C%22%2C%5C%22MDDZ%5C%22%2C%5C%22GJTCS%5C%22%5D%22%2C%22businessId%22%3A%2299999%22%2C%22name%22%3A%22tomas%22%2C%22type%22%3A%221%22%2C%22userId%22%3A%22193958859412501%22%2C%22status%22%3A%221%22%7D; access_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; session_token=**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************; XSRF-TOKEN=60f88d39-b22f-4af7-9948-ba022500074c" ,
                // "--data-raw", s,
                "--compressed", "https://api-store.gaojihealth.cn/order/api/orderPos/posOrderSaleDetail?businessId=466606&orderId="+user.getPosOrderNo() };

        //System.out.println(JSON.toJSONString(cmds));
        String s1 = execCurl(cmds);
            if (s1 != null && s1.length()>10) {
                System.out.println(s1);
                int length = s1.length();
                //String substring = s1.substring(1, length - 2);
                PosSaleOrderDetailDTO order = JSONObject.parseObject(s1, PosSaleOrderDetailDTO.class);
                System.out.println(JSONObject.toJSONString(order));
                if(null!=order && CollectionUtils.isNotEmpty(order.getPayType())){
                    for (int i = 0; i <order.getPayType().size() ; i++) {
                        PayChannelDetail payChannelDetail = order.getPayType().get(i);
                        if(i==0){
                            user.setPayChannelName(payChannelDetail.getPayChannelName());
                            user.setPayAmount(payChannelDetail.getPayAmount());
                        }else  if(i==1){
                            user.setPayChannelName2(payChannelDetail.getPayChannelName());
                            user.setPayAmount2(payChannelDetail.getPayAmount());
                        }else {
                            System.err.println("order 多支付方式"+JSONObject.toJSONString(order));
                        }
                    }
                }
            }
            return user;
    }

    public static String execCurl(String[] cmds) {
        ProcessBuilder process = new ProcessBuilder(cmds);
        Process p;
        try {
            p = process.start();
            BufferedReader reader = new BufferedReader(new InputStreamReader(p.getInputStream()));
            StringBuilder builder = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                builder.append(line);
                builder.append(System.getProperty("line.separator"));
            }
            return builder.toString();

        } catch (IOException e) {
            System.out.print("error");
            e.printStackTrace();
        }
        return null;
    }

}
