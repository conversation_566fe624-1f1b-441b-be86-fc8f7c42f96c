package com.tomas.mybaties3.utils.vo;

import com.google.common.collect.Maps;
import lombok.Data;

import java.time.LocalTime;
import java.util.Map;

@Data
public class CommonResponse {

    private String code;
    private String message;
    private Map data =Maps.newHashMap();
    private Object resultData;
    private static final String successCode = "200";

    public static CommonResponse success(){
        return new CommonResponse("200", "成功");
    }
    public static CommonResponse success(String message){
        return new CommonResponse("200", message);
    }
    public static CommonResponse fail(){
        return new CommonResponse("1", "失败");
    }
    public static CommonResponse fail(String message){
        return new CommonResponse("1", message);
    }
    public CommonResponse(String code, String message) {
        this.code = code;
        this.message = message;
    }
    public static Boolean isSuccessCode(String code){
        return successCode.equals(code);
    }
    public void putData(String key,Object value){
        data.put(key,value);
    }
    public Boolean isSuccess(){
        return CommonResponse.successCode.equals(this.code);
    }

    public static void main(String[] args) {
        // 获取当前时间
        LocalTime currentTime = LocalTime.now();
        // 设置比较的时间点为8点
        LocalTime eightAM = LocalTime.of(22, 0);
        // 判断当前时间是否晚于8点 晚8：00-23：59 发慢点休眠一会
        if (currentTime.isBefore(eightAM)) {
            System.out.println(100);
        }else {
            System.out.println(10000);
        }
    }
}
