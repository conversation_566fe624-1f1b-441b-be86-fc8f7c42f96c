package com.tomas.mybaties3.utils;

import com.alibaba.fastjson.JSONObject;
import com.tomas.mybaties3.entity.VoteRecord;

import java.util.*;
import java.util.stream.Collectors;

/**
 * JsonGetPV 描述
 *
 * <AUTHOR>
 * @create 2022/2/16
 **/
public class JsonGetPV {


    /**
     * @Title getJsonValueByKey
     * @Description 获取Json格式字符串中key对应的值
     * @param jsonStr json格式的字符串
     * @param key 要获取值的键
     * @return Object
     * @version V1.0
     */
    public static Object getJsonValueByKey(String jsonStr, String key) {
        // 此处引入的是 com.alibaba.fastjson.JSONObject; 对象
        JSONObject jsonObject = JSONObject.parseObject(jsonStr);
        // 获取到 key 对应的值
        return jsonObject.get(key);
    }

    public static void main(String[] args) {
        // json格式的字符串
        String str = "{'obj':[{'id':1,'name':'锅炉1'},{'id':2,'name':'锅炉2'}],'success':true,'message':null}";
        // 获取到key 为 success 的值，json字符串中 success 对应的值为 boolean 类型
        //getJsonValueByKey(str,"id");
        //System.out.println("success: " + success);
       // System.out.println(reverseChannelRlue(13));
       // System.out.println(BigDecimal.ONE.negate());
        VoteRecord u=new VoteRecord();
        VoteRecord u1=new VoteRecord();
        List<VoteRecord> dateList =new ArrayList<>();
        u.setId(1L);
        u.setCreateTime(new Date());
        dateList.add(u);
        try {
            Thread.sleep(1000L);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        u1.setId(2L);
        u1.setCreateTime(new Date());
        dateList.add(u1);
        System.out.println(dateList);

        VoteRecord voteRecord = dateList.stream().sorted(Comparator.comparing(VoteRecord::getCreateTime).reversed()).findFirst().orElse(null);
        System.out.println(voteRecord);

        List<VoteRecord> collect = dateList.stream().sorted(Comparator.comparing(VoteRecord::getCreateTime).reversed()).collect(Collectors.toList());
        System.out.println(collect);
       /* boolean present = Optional.ofNullable(str).isPresent();
        System.out.println(present);

        boolean present1 = Optional.ofNullable(null).isPresent();
        System.out.println(present1);*/
    }

    public static List<Integer> reverseChannelRlue(Integer applyChannel) {
        String binary = Integer.toBinaryString(applyChannel);
        List<Integer> list = new ArrayList<Integer>();
        byte b[] = binary.getBytes();
        for (int i = 0; i < b.length; i++) {
            if (b[i] == 49) {
                list.add(b.length - i - 1);
            }
        }
        return list;
    }


}
