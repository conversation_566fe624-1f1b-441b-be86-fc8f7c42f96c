package com.tomas.mybaties3.utils;

import cn.hutool.core.date.DateUtil;

import java.util.Calendar;
import java.util.Date;

/**
 * DateTimeUtil 描述
 *
 * <AUTHOR>
 * @create 2022/9/23
 **/
public class DateTimeUtil {



    /**
     * 获取date的前后days天 负数为前,正数为后
     * @param date
     * @param days
     * @return
     */
    public static Date getFetureDateBydays(Date date, int days){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, days);
        return calendar.getTime();
    }

    public static void main(String[] args) {

        for (int i = 0; i <30 ; i++) {

            Date fetureDateBydays = getFetureDateBydays(new Date(), -1 * i);
            System.out.print(DateUtil.format(fetureDateBydays,"yyyy-MM-dd")+",");

        }

    }
}
