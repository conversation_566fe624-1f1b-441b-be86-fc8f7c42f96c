package com.tomas.mybaties3.utils;

import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.symmetric.AES;
import cn.hutool.crypto.symmetric.SymmetricAlgorithm;
import cn.hutool.crypto.symmetric.SymmetricCrypto;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * 加盐对称加密工具类示例
 */
public class SaltedEncryptionExample {

    /**
     * 方法一：使用Hutool的AES加密（推荐）
     */
    public static class HutoolAESExample {
        private static final String ALGORITHM = "AES";

        /**
         * 加盐AES加密
         * @param plaintext 明文
         * @param password 用户密码
         * @param salt 盐值（可选，如果为null则自动生成）
         * @return 加密结果（包含盐值）
         */
        public static String encryptWithSalt(String plaintext, String password, String salt) {
            if (salt == null) {
                salt = RandomUtil.randomString(16); // 生成16位随机盐值
            }

            // 使用密码+盐值生成密钥
            String keyString = DigestUtil.sha256Hex(password + salt);
            byte[] key = keyString.substring(0, 32).getBytes(StandardCharsets.UTF_8);

            // 创建AES加密器
            AES aes = new AES(key);

            // 加密
            String encrypted = aes.encryptHex(plaintext);

            // 将盐值和密文组合返回（盐值:密文）
            return salt + ":" + encrypted;
        }

        /**
         * 加盐AES解密
         * @param encryptedWithSalt 包含盐值的加密数据
         * @param password 用户密码
         * @return 解密后的明文
         */
        public static String decryptWithSalt(String encryptedWithSalt, String password) {
            String[] parts = encryptedWithSalt.split(":", 2);
            if (parts.length != 2) {
                throw new IllegalArgumentException("Invalid encrypted data format");
            }

            String salt = parts[0];
            String encrypted = parts[1];

            // 使用密码+盐值生成密钥
            String keyString = DigestUtil.sha256Hex(password + salt);
            byte[] key = keyString.substring(0, 32).getBytes(StandardCharsets.UTF_8);

            // 创建AES解密器
            AES aes = new AES(key);

            // 解密
            return aes.decryptStr(encrypted, CharsetUtil.CHARSET_UTF_8);
        }
    }

    /**
     * 方法二：使用原生Java Crypto API
     */
    public static class NativeAESExample {
        private static final String ALGORITHM = "AES/CBC/PKCS5Padding";
        private static final String KEY_ALGORITHM = "AES";

        /**
         * 加盐AES-CBC加密
         * @param plaintext 明文
         * @param password 用户密码
         * @return 加密结果（Base64编码，包含盐值和IV）
         */
        public static String encryptWithSalt(String plaintext, String password) throws Exception {
            // 生成随机盐值
            byte[] salt = new byte[16];
            new SecureRandom().nextBytes(salt);

            // 生成随机IV
            byte[] iv = new byte[16];
            new SecureRandom().nextBytes(iv);

            // 基于密码和盐值生成密钥
            String keyString = DigestUtil.sha256Hex(password + new String(salt, StandardCharsets.UTF_8));
            byte[] key = keyString.substring(0, 32).getBytes(StandardCharsets.UTF_8);

            SecretKeySpec secretKey = new SecretKeySpec(key, KEY_ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            // 加密
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, secretKey, ivSpec);
            byte[] encryptedBytes = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

            // 组合盐值、IV和密文
            byte[] result = new byte[salt.length + iv.length + encryptedBytes.length];
            System.arraycopy(salt, 0, result, 0, salt.length);
            System.arraycopy(iv, 0, result, salt.length, iv.length);
            System.arraycopy(encryptedBytes, 0, result, salt.length + iv.length, encryptedBytes.length);

            return Base64.getEncoder().encodeToString(result);
        }

        /**
         * 加盐AES-CBC解密
         * @param encryptedData Base64编码的加密数据
         * @param password 用户密码
         * @return 解密后的明文
         */
        public static String decryptWithSalt(String encryptedData, String password) throws Exception {
            byte[] data = Base64.getDecoder().decode(encryptedData);

            // 提取盐值、IV和密文
            byte[] salt = new byte[16];
            byte[] iv = new byte[16];
            byte[] encryptedBytes = new byte[data.length - 32];

            System.arraycopy(data, 0, salt, 0, 16);
            System.arraycopy(data, 16, iv, 0, 16);
            System.arraycopy(data, 32, encryptedBytes, 0, encryptedBytes.length);

            // 基于密码和盐值生成密钥
            String keyString = DigestUtil.sha256Hex(password + new String(salt, StandardCharsets.UTF_8));
            byte[] key = keyString.substring(0, 32).getBytes(StandardCharsets.UTF_8);

            SecretKeySpec secretKey = new SecretKeySpec(key, KEY_ALGORITHM);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            // 解密
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, secretKey, ivSpec);
            byte[] decryptedBytes = cipher.doFinal(encryptedBytes);

            return new String(decryptedBytes, StandardCharsets.UTF_8);
        }
    }

    /**
     * 方法三：使用Hutool的其他对称加密算法
     */
    public static class HutoolOtherAlgorithms {

        /**
         * 使用DES加密（较老的算法，安全性较低）
         */
        public static String encryptWithDES(String plaintext, String password, String salt) {
            if (salt == null) {
                salt = RandomUtil.randomString(8);
            }

            String keyString = DigestUtil.md5Hex(password + salt);
            byte[] key = keyString.substring(0, 8).getBytes(StandardCharsets.UTF_8);

            SymmetricCrypto des = new SymmetricCrypto(SymmetricAlgorithm.DES, key);
            String encrypted = des.encryptHex(plaintext);

            return salt + ":" + encrypted;
        }

        public static String decryptWithDES(String encryptedWithSalt, String password) {
            String[] parts = encryptedWithSalt.split(":", 2);
            String salt = parts[0];
            String encrypted = parts[1];

            String keyString = DigestUtil.md5Hex(password + salt);
            byte[] key = keyString.substring(0, 8).getBytes(StandardCharsets.UTF_8);

            SymmetricCrypto des = new SymmetricCrypto(SymmetricAlgorithm.DES, key);
            return des.decryptStr(encrypted, CharsetUtil.CHARSET_UTF_8);
        }

        /**
         * 使用3DES加密
         */
        public static String encryptWith3DES(String plaintext, String password, String salt) {
            if (salt == null) {
                salt = RandomUtil.randomString(16);
            }

            String keyString = DigestUtil.sha256Hex(password + salt);
            byte[] key = keyString.substring(0, 24).getBytes(StandardCharsets.UTF_8);

            SymmetricCrypto des3 = new SymmetricCrypto(SymmetricAlgorithm.DESede, key);
            String encrypted = des3.encryptHex(plaintext);

            return salt + ":" + encrypted;
        }

        public static String decryptWith3DES(String encryptedWithSalt, String password) {
            String[] parts = encryptedWithSalt.split(":", 2);
            String salt = parts[0];
            String encrypted = parts[1];

            String keyString = DigestUtil.sha256Hex(password + salt);
            byte[] key = keyString.substring(0, 24).getBytes(StandardCharsets.UTF_8);

            SymmetricCrypto des3 = new SymmetricCrypto(SymmetricAlgorithm.DESede, key);
            return des3.decryptStr(encrypted, CharsetUtil.CHARSET_UTF_8);
        }
    }

    /**
     * 测试示例
     */
    public static void main(String[] args) {
        try {
            String plaintext = "这是需要加密的敏感数据Hello World!";
            String password = "mySecretPassword123";

            System.out.println("原文: " + plaintext);
            System.out.println("原文长度: " + plaintext.length());
            System.out.println();

            // 测试Hutool AES加密
            System.out.println("=== Hutool AES加密测试 ===");
            String encrypted1 = HutoolAESExample.encryptWithSalt(plaintext, password, null);
            System.out.println("加密后: " + encrypted1);
            System.out.println("密文长度: " + encrypted1.length());

            String decrypted1 = HutoolAESExample.decryptWithSalt(encrypted1, password);
            System.out.println("解密后: " + decrypted1);
            System.out.println("解密成功: " + plaintext.equals(decrypted1));
            System.out.println();

            // 测试原生AES加密
            System.out.println("=== 原生AES-CBC加密测试 ===");
            String encrypted2 = NativeAESExample.encryptWithSalt(plaintext, password);
            System.out.println("加密后: " + encrypted2);
            System.out.println("密文长度: " + encrypted2.length());

            String decrypted2 = NativeAESExample.decryptWithSalt(encrypted2, password);
            System.out.println("解密后: " + decrypted2);
            System.out.println("解密成功: " + plaintext.equals(decrypted2));
            System.out.println();

            // 测试3DES加密
            System.out.println("=== 3DES加密测试 ===");
            String encrypted3 = HutoolOtherAlgorithms.encryptWith3DES(plaintext, password, null);
            System.out.println("加密后: " + encrypted3);
            System.out.println("密文长度: " + encrypted3.length());

            String decrypted3 = HutoolOtherAlgorithms.decryptWith3DES(encrypted3, password);
            System.out.println("解密后: " + decrypted3);
            System.out.println("解密成功: " + plaintext.equals(decrypted3));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}