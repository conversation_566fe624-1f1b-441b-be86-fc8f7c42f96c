package com.tomas.mybaties3.utils.vo;

import java.util.List;

public class ResourceTreeVO {
    private Long id;

    private String name;

    private String icon;

    private Byte type;

    private String action;

    private String path;

    private Byte sort;

    private Long parentId;

    private Byte status;

    private String extend;

    private Integer version;

    //开始版本
    private Integer startVersion;
    //结束版本
    private Integer endVersion;

    //是否属于当前权限，1-属于 空-不属于
    private Integer isBelong;

    public Integer getIsBelong() {
        return isBelong;
    }

    public void setIsBelong(Integer isBelong) {
        this.isBelong = isBelong;
    }

    private List<ResourceTreeVO> child;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name == null ? null : name.trim();
    }

    public String getIcon() {
        return icon;
    }

    public void setIcon(String icon) {
        this.icon = icon == null ? null : icon.trim();
    }

    public Byte getType() {
        return type;
    }

    public void setType(Byte type) {
        this.type = type;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action == null ? null : action.trim();
    }

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path == null ? null : path.trim();
    }

    public Byte getSort() {
        return sort;
    }

    public void setSort(Byte sort) {
        this.sort = sort;
    }

    public Long getParentId() {
        return parentId;
    }

    public void setParentId(Long parentId) {
        this.parentId = parentId;
    }

    public Byte getStatus() {
        return status;
    }

    public void setStatus(Byte status) {
        this.status = status;
    }

    public String getExtend() {
        return extend;
    }

    public void setExtend(String extend) {
        this.extend = extend == null ? null : extend.trim();
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public List<ResourceTreeVO> getChild() {
        return child;
    }

    public void setChild(List<ResourceTreeVO> child) {
        this.child = child;
    }

    public Integer getStartVersion() {
        return startVersion;
    }

    public void setStartVersion(Integer startVersion) {
        this.startVersion = startVersion;
    }

    public Integer getEndVersion() {
        return endVersion;
    }

    public void setEndVersion(Integer endVersion) {
        this.endVersion = endVersion;
    }

    @Override
    public String toString() {
        return "ResourceTreeVO{" +
            "id=" + id +
            ", name='" + name + '\'' +
            ", icon='" + icon + '\'' +
            ", type=" + type +
            ", action='" + action + '\'' +
            ", path='" + path + '\'' +
            ", sort=" + sort +
            ", parentId=" + parentId +
            ", status=" + status +
            ", extend='" + extend + '\'' +
            ", version=" + version +
            ", isBelong=" + isBelong +
            ", child=" + child +
            '}';
    }
}
