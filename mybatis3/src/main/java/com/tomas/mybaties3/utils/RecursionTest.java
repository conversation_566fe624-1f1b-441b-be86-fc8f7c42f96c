package com.tomas.mybaties3.utils;

import com.tomas.mybaties3.utils.vo.ResourceTreeVO;
import org.springframework.util.CollectionUtils;

import java.io.BufferedReader;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 递归
 *
 * <AUTHOR>
 * @create 2021/10/2
 **/
public class RecursionTest {

    public static void main(String[] args) throws Exception {
       String a="INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (1, 3, 3, '高济健康', '0000', 100, 0, 1, 1, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (2, 3, 3, '高济健康', '0000', 100, 0, 1, 1, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (3, 3, 3, '高济健康', '0000', 100, 0, 1, 1, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (4, 3, 3, '高济健康', '0000', 100, 0, 1, 1, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (5, 3, 3, '高济健康', '0000', 100, 0, 1, 1, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (6, 3, 3, '高济健康', '0000', 100, 0, 1, 1, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (7, 3, 3, '高济健康', '0000', 100, 0, 2, 1, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (8, 3, 3, '高济健康', '0000', 100, 0, 2, 1, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (9, 3, 3, '高济健康', '0000', 100, 0, 2, 1, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (10, 3, 3, '高济健康', '0000', 100, 0, 2, 1, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (11, 3, 3, '高济健康', '0000', 100, 0, 2, 1, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (12, 3, 3, '高济健康', '0000', 100, 0, 2, 1, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (13, 3, 3, '高济健康', '0000', 100, 0, 3, 1, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (14, 3, 3, '高济健康', '0000', 100, 0, 3, 1, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (15, 3, 3, '高济健康', '0000', 100, 0, 3, 1, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (16, 3, 3, '高济健康', '0000', 100, 0, 3, 1, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (17, 3, 3, '高济健康', '0000', 100, 0, 3, 1, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (18, 3, 3, '高济健康', '0000', 100, 0, 3, 1, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (19, 3, 3, '高济健康', '0000', 100, 0, 4, 1, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (20, 3, 3, '高济健康', '0000', 100, 0, 4, 1, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (21, 3, 3, '高济健康', '0000', 100, 0, 4, 1, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (22, 3, 3, '高济健康', '0000', 100, 0, 4, 1, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (23, 3, 3, '高济健康', '0000', 100, 0, 4, 1, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (24, 3, 3, '高济健康', '0000', 100, 0, 4, 1, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (25, 3, 3, '高济健康', '0000', 100, 0, 5, 1, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (26, 3, 3, '高济健康', '0000', 100, 0, 5, 1, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (27, 3, 3, '高济健康', '0000', 100, 0, 5, 1, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (28, 3, 3, '高济健康', '0000', 100, 0, 5, 1, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (29, 3, 3, '高济健康', '0000', 100, 0, 5, 1, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (30, 3, 3, '高济健康', '0000', 100, 0, 5, 1, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (31, 3, 3, '高济健康', '0000', 100, 0, 6, 0, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (32, 3, 3, '高济健康', '0000', 100, 0, 6, 0, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (33, 3, 3, '高济健康', '0000', 100, 0, 6, 0, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (34, 3, 3, '高济健康', '0000', 100, 0, 6, 0, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (35, 3, 3, '高济健康', '0000', 100, 0, 6, 0, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (36, 3, 3, '高济健康', '0000', 100, 0, 6, 0, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (37, 3, 3, '高济健康', '0000', 100, 0, 6, 1, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (38, 3, 3, '高济健康', '0000', 100, 0, 6, 1, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (39, 3, 3, '高济健康', '0000', 100, 0, 6, 1, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (40, 3, 3, '高济健康', '0000', 100, 0, 6, 1, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (41, 3, 3, '高济健康', '0000', 100, 0, 6, 1, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (42, 3, 3, '高济健康', '0000', 100, 0, 6, 1, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (43, 3, 3, '高济健康', '0000', 100, 0, 7, 0, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (44, 3, 3, '高济健康', '0000', 100, 0, 7, 0, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (45, 3, 3, '高济健康', '0000', 100, 0, 7, 0, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (46, 3, 3, '高济健康', '0000', 100, 0, 7, 0, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (47, 3, 3, '高济健康', '0000', 100, 0, 7, 0, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (48, 3, 3, '高济健康', '0000', 100, 0, 7, 0, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (49, 3, 3, '高济健康', '0000', 100, 0, 7, 1, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (50, 3, 3, '高济健康', '0000', 100, 0, 7, 1, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (51, 3, 3, '高济健康', '0000', 100, 0, 7, 1, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (52, 3, 3, '高济健康', '0000', 100, 0, 7, 1, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (53, 3, 3, '高济健康', '0000', 100, 0, 7, 1, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (54, 3, 3, '高济健康', '0000', 100, 0, 7, 1, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (55, 3, 3, '高济健康', '0000', 100, 0, 8, 1, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (56, 3, 3, '高济健康', '0000', 100, 0, 8, 1, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (57, 3, 3, '高济健康', '0000', 100, 0, 8, 1, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (58, 3, 3, '高济健康', '0000', 100, 0, 8, 1, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (59, 3, 3, '高济健康', '0000', 100, 0, 8, 1, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (60, 3, 3, '高济健康', '0000', 100, 0, 8, 1, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (61, 3, 3, '高济健康', '0000', 100, 0, 9, 1, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (62, 3, 3, '高济健康', '0000', 100, 0, 9, 1, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (63, 3, 3, '高济健康', '0000', 100, 0, 9, 1, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (64, 3, 3, '高济健康', '0000', 100, 0, 9, 1, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (65, 3, 3, '高济健康', '0000', 100, 0, 9, 1, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (66, 3, 3, '高济健康', '0000', 100, 0, 9, 1, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (67, 3, 3, '高济健康', '0000', 100, 0, 10, 0, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (68, 3, 3, '高济健康', '0000', 100, 0, 10, 0, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (69, 3, 3, '高济健康', '0000', 100, 0, 10, 0, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (70, 3, 3, '高济健康', '0000', 100, 0, 10, 0, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (71, 3, 3, '高济健康', '0000', 100, 0, 10, 0, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (72, 3, 3, '高济健康', '0000', 100, 0, 10, 0, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (73, 3, 3, '高济健康', '0000', 100, 0, 10, 1, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (74, 3, 3, '高济健康', '0000', 100, 0, 10, 1, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (75, 3, 3, '高济健康', '0000', 100, 0, 10, 1, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (76, 3, 3, '高济健康', '0000', 100, 0, 10, 1, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (77, 3, 3, '高济健康', '0000', 100, 0, 10, 1, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (78, 3, 3, '高济健康', '0000', 100, 0, 10, 1, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (79, 3, 3, '高济健康', '0000', 100, 0, 11, 0, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (80, 3, 3, '高济健康', '0000', 100, 0, 11, 0, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (81, 3, 3, '高济健康', '0000', 100, 0, 11, 0, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (82, 3, 3, '高济健康', '0000', 100, 0, 11, 0, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (83, 3, 3, '高济健康', '0000', 100, 0, 11, 0, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (84, 3, 3, '高济健康', '0000', 100, 0, 11, 0, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (85, 3, 3, '高济健康', '0000', 100, 0, 11, 1, 0, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (86, 3, 3, '高济健康', '0000', 100, 0, 11, 1, 0, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (87, 3, 3, '高济健康', '0000', 100, 0, 11, 1, 1, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (88, 3, 3, '高济健康', '0000', 100, 0, 11, 1, 1, 1, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (89, 3, 3, '高济健康', '0000', 100, 0, 11, 1, 2, 0, 0, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);\n" +
                "INSERT INTO `cowell_cart`.`hd_web_pop_store` (`id`, `org_id`, `org_out_id`, `org_name`, `org_sap_code`, `org_type`, `parent_org_id`, `promotion_type`, `rule_condition`, `rule_threshold`, `pay_up`, `pop_up`, `inherit_parent`, `extra_field1`, `status`, `gmt_create`, `gmt_update`, `created_by`, `updated_by`, `extend`, `version`, `env`, `create_by_id`, `update_by_id`) VALUES (90, 3, 3, '高济健康', '0000', 100, 0, 11, 1, 2, 1, 1, 0, '4', 0, '2024-12-27 15:43:08', '2025-01-10 15:26:54', '系统管理员', '系统管理员', '', 0, 'prod', NULL, NULL);";


        String[] split = a.split(";\n");
        for (int i = 0; i <split.length ; i++) {
            String c=split[i]+";";
            c =   c.replace("`id`,","");
            c =   c.replace("VALUES ("+(i+1)+",","VALUES (");

            c =   c.replace("3, 3, '高济健康', '0000', 100, 0,","4707, 89688, '芜湖中山', '30X0', 500, 5285,");
            c =   c.replace("1, 0, 0, '4',","1, 1, 0, '4',");



            //`pay_up`, `pop_up`, `inherit_parent`, `extra_field1`


            System.out.println(c);
        }



//
//
//        String s=  FileUtil.readUtf8String("/Users/<USER>/Desktop/a.json");
//        System.out.println(s.substring(1, 200));
//        JSONObject jsonObject = JSONObject.parseObject(s);
//        JSONArray jsonArray = jsonObject.getJSONObject("data").getJSONArray("result_0");
//
////        jsonArray.stream().forEach(v -> {
////            JSONObject jsonObject1 = (JSONObject) v;
////            System.out.println(jsonObject1);
////        });
//
//        // 使用Map来存储每个ID及其出现的次数
//        Map<Long, Long> idFrequencyMap = new HashMap<>();
//        jsonArray.stream().forEach(v -> {
//            JSONObject jsonObject1 = (JSONObject) v;
//            idFrequencyMap.put(jsonObject1.getLong("id"), idFrequencyMap.getOrDefault("id", 0L) + 1);
//        });
//
//        // 过滤出出现次数大于1的ID，并收集到Set中
//        Set<Long> idsWithFrequencyGreaterThanOne = new HashSet<>();
//        for (Map.Entry<Long, Long> entry : idFrequencyMap.entrySet()) {
//            if (entry.getValue() > 1) {
//                idsWithFrequencyGreaterThanOne.add(entry.getKey());
//            }
//        }
//
//        // 输出结果
//        System.out.println(idsWithFrequencyGreaterThanOne);
//        result_0

        // List<ResourceTreeVO> resourceTreeVOS = JSONArray.parseArray(s, ResourceTreeVO.class);
        //System.out.println(JSON.toJSONString(resourceTreeVOS));
       // System.out.println(getResourceTreeByLevel(resourceTreeVOS,4));
//        ResourceTreeVO a=new ResourceTreeVO();
//        a.setStartVersion(1);
//        Integer av = Optional.ofNullable(a).flatMap(t -> Optional.ofNullable(t.getStartVersion())).orElse(Integer.valueOf(0));
//
//        ResourceTreeVO b=new ResourceTreeVO();
//        b.setStartVersion(null);
//        Integer bv = Optional.ofNullable(b).flatMap(t -> Optional.ofNullable(t.getStartVersion())).orElse(Integer.valueOf(0));
//
//
//        ResourceTreeVO c=null;
//        Integer cv = Optional.ofNullable(c).flatMap(t -> Optional.ofNullable(t.getStartVersion())).orElse(Integer.valueOf(0));
//
//        System.out.println("av="+av  +"bv="+bv+"  cv="+cv);

        //System.out.println(s);
    }




    public static List<ResourceTreeVO> getResourceTreeByLevel(List<ResourceTreeVO> resourceTree ,int level){
        List<ResourceTreeVO> departments = resourceTree.stream().filter(v-> (v.getType()!=null && level > v.getType().intValue())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(departments)){
            return resourceTree;
        }else {
            List<ResourceTreeVO> childList = new ArrayList<>();
            for (ResourceTreeVO entity : departments) {
                // 遍历所有节点，将父菜单id与传过来的id比较
                if (!CollectionUtils.isEmpty(entity.getChild())) {
                    childList.addAll(entity.getChild());
                }
            }
            if (CollectionUtils.isEmpty(childList)){
                return resourceTree;
            }
            return getResourceTreeByLevel(childList,level);
        }
    }


    /**
     * 读取文件
     *
     * @param Path
     * @return
     */
    public static String ReadFile(String Path) throws Exception {
        BufferedReader reader = null;
        String laststr = "";
        try {
            FileInputStream fileInputStream = new FileInputStream(Path);
            InputStreamReader inputStreamReader = new InputStreamReader(fileInputStream, "UTF-8");
            reader = new BufferedReader(inputStreamReader);
            String tempString = null;
            while ((tempString = reader.readLine()) != null) {
                laststr += tempString;
            }
            reader.close();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return laststr;
    }
}
