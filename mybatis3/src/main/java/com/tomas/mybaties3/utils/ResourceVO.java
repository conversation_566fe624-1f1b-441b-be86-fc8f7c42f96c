package com.tomas.mybaties3.utils;

import com.tomas.mybaties3.utils.vo.Resource;
import lombok.Data;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.List;

@Data
public class ResourceVO {

    private Long id;

    private String name;

    private String icon;

    private BigDecimal stock;

    private BigDecimal stockPice;

    private List<Resource> userList;


    public static void main(String[] args) throws Throwable {
        ResourceVO resourceVO = new ResourceVO();
        resourceVO.setId(123L);
        resourceVO.setName("sss");
        resourceVO.setStock(new BigDecimal(-1));

        ResourceVO resourceVO1 = new ResourceVO();
        resourceVO1.setId(125L);
        resourceVO1.setName("sss");
        resourceVO1.setStock(new BigDecimal(-1));

        List<Resource> userList=new ArrayList<>();
        ResourceVO a=new ResourceVO();
        a.setName("txte");
        a.setStock(resourceVO.getStock());

        resourceVO.setStock(BigDecimal.ZERO);

        //System.out.println(a.toString());
       // System.out.println(resourceVO.toString());
        resourceVO.setUserList(userList);


        Collection<ResourceVO> collection = new ArrayList();
        collection.add(resourceVO);
        collection.add(resourceVO1);
//简单逻辑
        collection.removeIf(p -> p.getId() >= 125L);//过滤30岁以上的求职者
        System.out.println(collection);


    }

    private static String buildParamKey(String prefixKey, Object[] pjp) throws Throwable {
       /* EvaluationContext context = new StandardEvaluationContext();
        // add self define arguments
        context.setVariable("args", pjp);

        ExpressionParser parser = new SpelExpressionParser();

        Expression expression = parser.parseExpression("args");


       // Object obj = scriptContext.getExpression().getValue(context);
        return expression.getValue(context, String.class);*/

/*
        SpelExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(opLog.OpItemIdExpression());
        EvaluationContext context = new StandardEvaluationContext();

        //获取参数值
        Object[] args =pjp;

        //获取运行时参数的名称
        LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
        String[] parameterNames = discoverer.getParameterNames(method);

        //将参数绑定到context中
        if(parameterNames != null){
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i],args[i]);
            }
        }
        // 解析表达式，获取结果
        String itemId = String.valueOf(expression.getValue(context));*/
       return  Arrays.toString(pjp);


    };


}
