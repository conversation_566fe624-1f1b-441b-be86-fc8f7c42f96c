package com.tomas.mybaties3.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.tomas.mybaties3.utils.vo.AnalysisStoreGoodsVo;
import com.tomas.mybaties3.utils.vo.BDPCommonResponse;
import com.tomas.mybaties3.utils.vo.CommonResponse;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


public class HttpUtils {

    private  static final Logger log = LoggerFactory.getLogger(HttpUtils.class);

    /**
     * POST---有参测试(对象参数)
     *
     * @date 2018年7月13日 下午4:18:50
     */
    public static CommonResponse doPost(String url, Object param) {

        // 获得Http客户端(可以理解为:你得先有一个浏览器;注意:实际上HttpClient与浏览器是不一样的)
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        // 创建Post请求
        HttpPost httpPost = new HttpPost(url);

        String jsonString = JSON.toJSONString(param);

        StringEntity entity = new StringEntity(jsonString, "UTF-8");

        // post请求是将参数放在请求体里面传过去的;这里将entity放入post请求体中
        httpPost.setEntity(entity);

        httpPost.setHeader("Content-Type", "application/json;charset=utf8");

        // 响应模型
        CloseableHttpResponse response = null;
        CommonResponse commonResponse = null;
        try {
            // 由客户端执行(发送)Post请求
            response = httpClient.execute(httpPost);
            // 从响应模型中获取响应实体
            HttpEntity responseEntity = response.getEntity();
            String responseStr = EntityUtils.toString(responseEntity);
            //System.out.println(responseStr);
            if(response.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                if (responseEntity != null) {
                    commonResponse = JSON.parseObject(responseStr,CommonResponse.class);
                    System.out.println(JSON.toJSONString(commonResponse));
                    return commonResponse;
                }
            }
            return CommonResponse.fail(responseStr);
        } catch (Exception e) {
           return CommonResponse.fail(e.getMessage());
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * POST---有参测试(对象参数)
     *
     * @date 2018年7月13日 下午4:18:50
     */
    public static String doPost(String url,String jsonString) {

        // 获得Http客户端(可以理解为:你得先有一个浏览器;注意:实际上HttpClient与浏览器是不一样的)
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();

        // 创建Post请求
        HttpPost httpPost = new HttpPost(url);

        StringEntity entity = new StringEntity(jsonString, "utf-8");

        // post请求是将参数放在请求体里面传过去的;这里将entity放入post请求体中
        httpPost.setEntity(entity);

        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        // 响应模型
        CloseableHttpResponse response = null;
        try {
            // 由客户端执行(发送)Post请求
            response = httpClient.execute(httpPost);
            // 从响应模型中获取响应实体
            HttpEntity responseEntity = response.getEntity();
            String responseStr = EntityUtils.toString(responseEntity,"utf-8");
            return responseStr;
        } catch (Exception e) {
            e.toString();
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return "";
    }


    public static String doPostForBDP(String url, Object param) {

        // 获得Http客户端(可以理解为:你得先有一个浏览器;注意:实际上HttpClient与浏览器是不一样的)
        CloseableHttpClient httpClient = HttpClientBuilder.create().build();
        // 创建Post请求
        HttpPost httpPost = new HttpPost(url);
        String jsonString = JSON.toJSONString(param);
        StringEntity entity = new StringEntity(jsonString, "UTF-8");
        // post请求是将参数放在请求体里面传过去的;这里将entity放入post请求体中
        httpPost.setEntity(entity);
        httpPost.setHeader("Content-Type", "application/json;charset=utf8");
        // 响应模型
        CloseableHttpResponse response = null;
        try {
            // 由客户端执行(发送)Post请求
            response = httpClient.execute(httpPost);
            // 从响应模型中获取响应实体
            HttpEntity responseEntity = response.getEntity();
            String responseStr = EntityUtils.toString(responseEntity,"UTF-8");
            if(response.getStatusLine().getStatusCode() == HttpStatus.SC_OK){
                if (responseEntity != null) {
                    return responseStr;
                }
            }
            return null ;
        } catch (Exception e) {
            return null;
        } finally {
            try {
                // 释放资源
                if (httpClient != null) {
                    httpClient.close();
                }
                if (response != null) {
                    response.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void main(String[] args) {


        List<String> names = Arrays.asList("tomas", "yada", "example", "more");

        // 使用Java Stream和Collectors将List转换为逗号分隔的字符串
        String result = names.stream().collect(Collectors.joining(","));

        System.out.println(result);

        JSONObject paramPresto=new JSONObject();
        try {
            JSONObject var=new JSONObject();
            //var.put("IP_STOREIDS","149032522735,139428622735");
            var.put("IP_STOREIDS","149032522735,139428622735");
            var.put("IP_BUSINESSIDS","22735");
            var.put("IP_DATE","20231130");
            var.put("IP_DATE_END","20231130");
            var.put("page",10);
            var.put("pageSize",5);
            paramPresto.put("var",var);
            paramPresto.put("name","PromotionStoreGoodData");
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }


        System.out.println(BigDecimal.ZERO.divide(BigDecimal.TEN, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)));



        /*CommonResponse commonResponse = HttpUtils.doPost("http://bdp-ddp.gaojihealth.cn/bdp/queryengine/query/presto", paramPresto);
        //log.info("paramPresto={} commonResponse={}",paramPresto,commonResponse);
        System.out.println(JSON.toJSONString(commonResponse));
        long totalCount=0L;
        if(Objects.nonNull(commonResponse.getData()) && Objects.nonNull(commonResponse.getData().get("result_0"))){
            Object result0 = JSONArray.parseArray(JSON.toJSONString(commonResponse.getData().get("result_0"))).get(0);
            if(Objects.nonNull(result0)){
                JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(result0));
                System.out.println(jsonObject.toJSONString());
                totalCount =  Long.parseLong(jsonObject.getInteger("totalCount").toString());
                System.out.println("totalCount"+totalCount);

            }
        }

        try {
            paramPresto.put("name","PromotionStoreGood");
            System.out.println(JSON.toJSONString(paramPresto));
            CommonResponse commonResponseData = HttpUtils.doPost("http://bdp-ddp.gaojihealth.cn/bdp/queryengine/query/presto", paramPresto);
           // log.info("paramPresto={} commonResponse={}",paramPresto,commonResponseData);
            //System.out.println(JSON.toJSONString(commonResponseData));
            if(Objects.nonNull(commonResponseData.getData()) && Objects.nonNull(commonResponseData.getData().get("result_0"))){

                for (Object result0 : JSONArray.parseArray(JSON.toJSONString(commonResponseData.getData().get("result_0")))) {
                    if(Objects.nonNull(result0)){
                        JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(result0));
                        System.out.println(jsonObject.getString("store_id"));
                    }
                }


            }

        } catch (JSONException e) {
            throw new RuntimeException(e);
        }*/

        String commonResponseCountStr = HttpUtils.doPostForBDP("http://bdp-ddp.gaojihealth.cn/bdp/queryengine/query/presto", paramPresto);
        log.info("commonResponseCountStr={}",commonResponseCountStr);
        BDPCommonResponse<AnalysisStoreGoodsVo> commonResponseCount = JSON.parseObject(commonResponseCountStr, new TypeReference<BDPCommonResponse<AnalysisStoreGoodsVo>>(){});
        log.info("paramPresto={} commonResponse={} commonResponseCountJSON={}",paramPresto,commonResponseCount,JSON.toJSONString(commonResponseCount));
        long totalCount=0L;
        if(Objects.nonNull(commonResponseCount.getData()) && Objects.nonNull(commonResponseCount.getData().get("result_0"))){
            List<AnalysisStoreGoodsVo> resultList = commonResponseCount.getData().get("result_0");
            if(CollectionUtils.isNotEmpty(resultList)){
                AnalysisStoreGoodsVo storeGoodsVo = resultList.get(0);
                totalCount = storeGoodsVo.getTotalCount();
            }
        }
        log.info("totalCount={}",  totalCount);
        List<AnalysisStoreGoodsVo> list=new ArrayList<>();
        try {
            paramPresto.put("name","PromotionStoreGood");
            String commonResponseDataStr = HttpUtils.doPostForBDP("http://bdp-ddp.gaojihealth.cn/bdp/queryengine/query/presto", paramPresto);
            log.info("commonResponseDataStr={}",commonResponseDataStr);
            BDPCommonResponse<AnalysisStoreGoodsVo> commonResponseData = JSON.parseObject(commonResponseDataStr, new TypeReference<BDPCommonResponse<AnalysisStoreGoodsVo>>(){});
            log.info("paramPresto={} commonResponse={} commonResponseDataJSOn={}",paramPresto,commonResponseData,JSON.toJSONString(commonResponseData));
            if(Objects.nonNull(commonResponseData.getData()) && Objects.nonNull(commonResponseData.getData().get("result_0"))){
                list = commonResponseCount.getData().get("result_0");
            }
        } catch (JSONException e) {
            throw new RuntimeException(e);
        }

    }
}
