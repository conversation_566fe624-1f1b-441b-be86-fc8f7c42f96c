package com.tomas.mybaties3.utils.vo;


import java.io.Serializable;

/**
 * <AUTHOR>
 */
public class NewSpuVo implements Serializable {
    private static final long serialVersionUID = 9047062012902909123L;

    private Long id;

    private Long businessId;

    private String goodsNo;

    private String name;

    private String barCode;

    private String opCode;

    private String curName;

    private String curOpCode;

    private String jhiSpecification;

    private String factoryid;

    private Long categoryId;

    private String categoryName;

    private String goodsunit;

    private String prodarea;

    /**
     * 拆零单位
     */
    private String pieceunit;

    /**
     * 拆零比例
     */
    private String piecerate;

    /**
     * 是否可拆零  1 是 2 否
     */
    private String pieceind;
    
    /**
     * 保质期
     */
    private String validperiod;
    
    /**
     * 养护方法 4181：外观质量检查法 4180 ： 晾晒法
     * 4179对抗养护法德国 4176清洁养护法 4177 密封养护法
     * 4178  低温养护法
     */
    private String mainten_method;

    /**
     * 养护周期
     */
    private String mainten_period;

    private String mainten_type;


    private String storagecond;

    public Long getBusinessId() {
        return businessId;
    }

    public void setBusinessId(Long businessId) {
        this.businessId = businessId;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getOpCode() {
        return opCode;
    }

    public void setOpCode(String opCode) {
        this.opCode = opCode;
    }

    public String getCurName() {
        return curName;
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setCurName(String curName) {
        this.curName = curName;
    }

    public String getCurOpCode() {
        return curOpCode;
    }

    public void setCurOpCode(String curOpCode) {
        this.curOpCode = curOpCode;
    }

    public String getJhiSpecification() {
        return jhiSpecification;
    }

    public String getBarCode() {
        return barCode;
    }

    public void setBarCode(String barCode) {
        this.barCode = barCode;
    }

    public void setJhiSpecification(String jhiSpecification) {
        this.jhiSpecification = jhiSpecification;
    }

    public String getFactoryid() {
        return factoryid;
    }

    public void setFactoryid(String factoryid) {
        this.factoryid = factoryid;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public String getProdarea() {
        return prodarea;
    }

    public void setProdarea(String prodarea) {
        this.prodarea = prodarea;
    }

    public String getPieceunit() {
        return pieceunit;
    }

    public void setPieceunit(String pieceunit) {
        this.pieceunit = pieceunit;
    }

    public String getPiecerate() {
        return piecerate;
    }

    public void setPiecerate(String piecerate) {
        this.piecerate = piecerate;
    }

    public String getMainten_method() {
        return mainten_method;
    }

    public void setMainten_method(String mainten_method) {
        this.mainten_method = mainten_method;
    }

    public String getMainten_period() {
        return mainten_period;
    }

    public void setMainten_period(String mainten_period) {
        this.mainten_period = mainten_period;
    }

    public String getMainten_type() {
        return mainten_type;
    }

    public void setMainten_type(String mainten_type) {
        this.mainten_type = mainten_type;
    }

    public String getStoragecond() {
        return storagecond;
    }

    public void setStoragecond(String storagecond) {
        this.storagecond = storagecond;
    }

    public String getPieceind() {
		return pieceind;
	}

	public void setPieceind(String pieceind) {
		this.pieceind = pieceind;
	}

	public String getValidperiod() {
		return validperiod;
	}

	public void setValidperiod(String validperiod) {
		this.validperiod = validperiod;
	}

	@Override
    public String toString() {
        return "NewSpuVo{" +
            "businessId=" + businessId +
            ", goodsNo='" + goodsNo + '\'' +
            ", name='" + name + '\'' +
            ", opCode='" + opCode + '\'' +
            ", curName='" + curName + '\'' +
            ", curOpCode='" + curOpCode + '\'' +
            ", jhiSpecification='" + jhiSpecification + '\'' +
            ", factoryid='" + factoryid + '\'' +
            ", categoryId=" + categoryId +
            ", categoryName='" + categoryName + '\'' +
            ", goodsunit='" + goodsunit + '\'' +
            ", prodarea='" + prodarea + '\'' +
            ", pieceunit='" + pieceunit + '\'' +
            ", piecerate='" + piecerate + '\'' +
            ", mainten_method='" + mainten_method + '\'' +
            ", mainten_period='" + mainten_period + '\'' +
            ", mainten_type='" + mainten_type + '\'' +
            ", storagecond='" + storagecond + '\'' +
            '}';
    }
}
