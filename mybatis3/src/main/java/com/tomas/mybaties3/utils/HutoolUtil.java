package com.tomas.mybaties3.utils;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.tomas.mybaties3.utils.vo.ListToExcelMultiSheetDTO;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.NumberToTextConverter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * <AUTHOR>
 */
public class HutoolUtil {


    private static final Logger log = LoggerFactory.getLogger(HutoolUtil.class);
    public final static String DATE_OUTPUT_PATTERNS = "yyyy-MM-dd HH:mm:ss";
    public final static SimpleDateFormat SIMPLE_DATE_FORMAT = new SimpleDateFormat(DATE_OUTPUT_PATTERNS);

    /**
     * @param inputStream : 承载着Excel的输入流
     * @param map         : Excel中的中文列头和类的英文属性的对应关系Map
     * @param entityClass : List中对象的类型（Excel中的每一行都要转化为该类型的对象）
     * @param x           : 从第几行开始读（表头）
     * @return : List<T>
     * @throws Exception
     * @MethodName : excelToList
     * @Description : 将Excel转化为List
     */
    public static <T> List<T> excelToList(InputStream inputStream, Map map, Class<T> entityClass, Integer x) throws Exception {
        Map<String, Map<String, Object>> excelMapModel = getExcelDataMap(inputStream, map, x);
        // 3 转化list
        List<T> list = Lists.newArrayList();
        try {
            JSONArray j = new JSONArray();
            for (Map.Entry<String, Map<String, Object>> entry : excelMapModel.entrySet()) {
                j.add(new JSONObject(entry.getValue()));
            }
            list = j.toJavaList(entityClass);
        } catch (Exception e) {
            log.error("解析文件失败", e);
        }
        return list;
    }

    public static <T> List<T> excelToListForObject(InputStream inputStream, Map map, Class<T> entityClass, Integer x) throws Exception {
        Map<String, Map<String, Object>> excelMapModel = getExcelDataMap(inputStream, map, x);
        // 3 转化list
        List<T> list = Lists.newArrayList();
        try {
            for (Map.Entry<String, Map<String, Object>> entry : excelMapModel.entrySet()) {
                T instance = ReflectUtil.newInstance(entityClass);
                if (MapUtils.isEmpty(entry.getValue())) {
                    continue;
                }
                for (Map.Entry<String, Object> e : entry.getValue().entrySet()) {
                    if (StringUtils.isBlank(e.getKey()) || !ReflectUtil.hasField(entityClass, e.getKey())) {
                        continue;
                    }
                    Field field = ReflectUtil.getField(entityClass, e.getKey());
                    Object object = e.getValue();
                    if (CommonUtil.isNumberType(field.getType()) && "".equals(object)) {
                        object = "0";
                    }
                    ReflectUtil.setFieldValue(instance, e.getKey(), object);
                }
                list.add(instance);
            }
        } catch (Exception e) {
            log.error("解析文件失败", e);
        }
        return list;
    }

    private static Map<String, Map<String, Object>> getExcelDataMap(InputStream inputStream, Map map, Integer x) {
        Map<String, Map<String, Object>> excelMapModel = Maps.newHashMap();
        ExcelReader reader = null;
        try {
            // 1 模板处理 sheetIndex=0代表第一个
            reader = ExcelUtil.getReader(inputStream, 0);
            excelMapModel = Maps.newHashMap();

            // 2 遍历行
            for (int start = x + 1; ; start++) {
                Map<String, Object> rowMap = Maps.newHashMap();

                // 如果这行的value到位null或者空字符则任务结束
                boolean isEmptyRow = true;
                for (int cell = 1; cell <= map.size(); cell++) {
                    //                log.info("start={},cell={}", start, cell);
                    Row keyRow = reader.getSheet().getRow(x - 1);
                    if (keyRow == null) {
                        break;
                    }
                    // 标题value当key
                    String key = getValue(keyRow.getCell(cell - 1));

                    Row row = reader.getSheet().getRow(start - 1);
                    if (row == null) {
                        break;
                    }
                    // 真实的value
                    String value = getValue(row.getCell(cell - 1));

                    if (StringUtils.isNotBlank(value)) {
                        isEmptyRow = false;
                    }
                    String newKey = (String) map.get(key);
                    rowMap.put(newKey, value);
                }
                if (isEmptyRow) {
                    break;
                }
                rowMap.put("lineNum", start);
                excelMapModel.put(String.valueOf(start), rowMap);
            }
        } catch (Throwable e) {
            log.error("解析文件异常", e);
            throw new RuntimeException("导入文件失败！");
        } finally {
            if (reader != null) {
                reader.close();
            }
        }
        return excelMapModel;
    }

    /**
     * @param list      数据源
     * @param fieldMap  类的英文属性和Excel中的中文列名的对应关系
     *                  如果需要的是引用对象的属性，则英文属性使用类似于EL表达式的格式
     *                  如：list中存放的都是student，student中又有college属性，而我们需要学院名称，则可以这样写
     *                  fieldMap.put("college.collegeName","学院名称")
     * @param sheetName 工作表的名称
     * @param out       导出流
     * @throws
     * @MethodName : listToExcel
     * @Description : 导出Excel（可以导出到本地文件系统，也可以导出到浏览器，可自定义工作表大小） 一个sheet不分页，不合并单元格
     */
    public static <T> void listToExcel(List<T> list, LinkedHashMap<String, String> fieldMap, String sheetName, OutputStream out) {

        //通过工具类创建writer
        ExcelWriter writer = ExcelUtil.getWriter();
        if (StringUtils.isNotBlank(sheetName)) {
            writer.renameSheet(sheetName);
        }
        // 1 写标题
        writer.writeHeadRow(fieldMap.values());
        // 2 写单元格
        int rowNum = 1;
        int cellNum = 0;
        for (T t : list) {
            for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
                Object v = ReflectUtil.getFieldValue(t, entry.getKey());
                if (v == null) {
                    v = "";
                }
                writer.writeCellValue(cellNum, rowNum, v);
                cellNum++;
            }
            rowNum++;
            cellNum = 0;
        }
        writer.flush(out);
        //关闭writer，释放内存
        writer.close();
    }

    /**
     * @param list      数据源
     * @param fieldMap  类的英文属性和Excel中的中文列名的对应关系
     *                  如果需要的是引用对象的属性，则英文属性使用类似于EL表达式的格式
     *                  如：list中存放的都是student，student中又有college属性，而我们需要学院名称，则可以这样写
     *                  fieldMap.put("college.collegeName","学院名称")
     * @param sheetName 工作表的名称
     * @return          返回输入流
     * @throws
     * @MethodName : listToExcel
     * @Description : 导出Excel（可以导出到本地文件系统，也可以导出到浏览器，可自定义工作表大小） 一个sheet不分页，不合并单元格
     */
    public static <T> InputStream listToExcel(List<T> list, LinkedHashMap<String, String> fieldMap, String sheetName) {
        try (ByteArrayOutputStream os = new ByteArrayOutputStream()) {
            listToExcel(list, fieldMap, sheetName, os);
            return new ByteArrayInputStream(os.toByteArray());
        } catch (Exception e) {
            log.info("文件导出异常", e);
        }
        return null;
    }

    /**
     * @param listDto  数据源
     * @param response 导出流
     * @throws
     * @MethodName : listToExcel
     * @Description : 导出Excel（可以导出到本地文件系统，也可以导出到浏览器，可自定义工作表大小） 一个sheet不分页，不合并单元格
     */
    public static void listToExcelMultiSheet(String fileName, List<ListToExcelMultiSheetDTO> listDto, HttpServletResponse response) {
        //设置response头信息
        response.reset();
        response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
        response.setCharacterEncoding("utf-8");
        response.setHeader("Content-disposition", "attachment; filename=" + fileName + ".xls");
        try {
            listToExcel(listDto, response.getOutputStream());
        } catch (IOException e) {
            log.info("文件导出异常", e);
        }
    }

    /**
     * @param listDto  数据源
     * @param desc 导出流
     * @param response 导出流
     * @throws
     * @MethodName : listToExcel 首行展示文案
     * @Description : 导出Excel（可以导出到本地文件系统，也可以导出到浏览器，可自定义工作表大小） 一个sheet不分页，不合并单元格
     */
    public static void listToExcelIncludeDesc(String fileName, List<ListToExcelMultiSheetDTO> listDto, String desc, HttpServletResponse response) {
        if(StringUtils.isBlank(desc)){
            throw new RuntimeException("文案为空");
        }
        try {
            //设置response头信息
            response.reset();
            response.setContentType("application/vnd.ms-excel");        //改成输出excel文件
            response.setCharacterEncoding("utf-8");
            response.setHeader("Content-disposition", "attachment; filename=" + new String(fileName.getBytes(),"iso-8859-1") + ".xls");
            listToExcelByDesc(listDto, desc, response.getOutputStream());
        } catch (IOException e) {
            log.info("文件导出异常", e);
        }
    }


    public static <T> void listToExcel(List<ListToExcelMultiSheetDTO> listDto, OutputStream out) {
        ExcelWriter writer = ExcelUtil.getWriter();
        try {
            Integer i = 0;
            for (ListToExcelMultiSheetDTO list : listDto) {

                writer.setSheet(i++).renameSheet(list.getSheetName());
                LinkedHashMap<String, String> fieldMap = list.getFieldMap();
                // 1 写标题
                writer.writeHeadRow(fieldMap.values());
                // 2 写单元格
                int rowNum = 1;
                int cellNum = 0;
                List<T> data = list.getListGroup();
                for (T t : data) {
                    for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
                        Object value = ReflectUtil.getFieldValue(t, entry.getKey());
                        if (value == null) {
                            value = "";
                        }
                        writer.writeCellValue(cellNum, rowNum, value);
                        cellNum++;
                    }
                    rowNum++;
                    cellNum = 0;
                }
            }
        } catch (Exception e) {
            log.info("文件导出异常", e);
        } finally {
            //关闭writer，释放内存
            writer.flush(out);
            writer.close();
        }
    }

    public static <T> void listToExcelByDesc(List<ListToExcelMultiSheetDTO> listDto, String desc, OutputStream out) {
        ExcelWriter writer = ExcelUtil.getWriter();
        try {
            Integer i = 0;
            for (ListToExcelMultiSheetDTO list : listDto) {

                writer.setSheet(i++).renameSheet(list.getSheetName());
                LinkedHashMap<String, String> fieldMap = list.getFieldMap();
                // 1 说明
                writer.writeCellValue(0, 0, desc);
                // 2 标题
                final List<Integer> num = new ArrayList<>(1);
                num.add(0);
                fieldMap.forEach((k,v) -> {
                    writer.writeCellValue(num.get(0), 1, v);
                    num.set(0, num.get(0) + 1);
                });
                // 3 写单元格
                int rowNum = 2;
                int cellNum = 0;
                List<T> data = list.getListGroup();
                for (T t : data) {
                    for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
                        Object value = ReflectUtil.getFieldValue(t, entry.getKey());
                        log.info("listToExcelByDesc -- valeu,{}.",value);
                        if (value == null) {
                            value = "";
                        }
                        writer.writeCellValue(cellNum, rowNum, value);
                        cellNum++;
                    }
                    rowNum++;
                    cellNum = 0;
                }
                writer.merge(0,0,0, list.getFieldMap().size(), desc,true);
            }
        } catch (Exception e) {
            log.info("文件导出异常", e);
        } finally {
            //关闭writer，释放内存
            writer.flush(out);
            writer.close();
        }
    }

  


    /**
     * 获取excel的值
     *
     * <AUTHOR>
     */
    public static String getValue(Cell cell) {
        String ret = "";
        if (cell == null) {
            return ret;
        }
        try {

            CellType type = cell.getCellTypeEnum();
            switch (type) {
                case BLANK:
                    ret = "";
                    break;
                case BOOLEAN:
                    ret = String.valueOf(cell.getBooleanCellValue());
                    break;
                case ERROR:
                    ret = null;
                    break;
                case FORMULA:
                    Workbook wb = cell.getSheet().getWorkbook();
                    CreationHelper crateHelper = wb.getCreationHelper();
                    FormulaEvaluator evaluator = crateHelper.createFormulaEvaluator();
                    ret = getValue(evaluator.evaluateInCell(cell));
                    break;
                case NUMERIC:
                    if (DateUtil.isCellDateFormatted(cell)) {
                        Date theDate = cell.getDateCellValue();
                        ret = SIMPLE_DATE_FORMAT.format(theDate);
                    } else {
                        ret = NumberToTextConverter.toText(cell.getNumericCellValue());
                    }
                    break;
                case STRING:
                    ret = cell.getRichStringCellValue().getString();
                    break;
                default:
                    ret = "";
                    break;
            }
            return ret;

        } catch (Exception e) {
            log.warn("读取excel文件异常", e);
        }
        return ret;
    }

    /**
     * @param inputStream : 承载着Excel的输入流
     * @param map         : Excel中的中文列头和类的英文属性的对应关系Map
     * @param entityClass : List中对象的类型（Excel中的每一行都要转化为该类型的对象）
     * @param x           : 从第几行开始读（表头）
     * @return : List<T>
     * @throws Exception
     * @MethodName : excelToList
     * @Description : 将Excel转化为List
     */
    public static <T> List<T> excelToBeList(InputStream inputStream, Map map, Class<T> entityClass, Integer x) throws Exception {
        Map<String, Map<String, Object>> excelMapModel = getExcelInfoMap(inputStream, map, x);
        // 3 转化list
        List<T> list = Lists.newArrayList();
        try {
            JSONArray j = new JSONArray();
            for (Map.Entry<String, Map<String, Object>> entry : excelMapModel.entrySet()) {
                j.add(new JSONObject(entry.getValue()));
            }
            list = j.toJavaList(entityClass);
        } catch (Exception e) {
            log.error("解析文件失败", e);
        }
        return list;
    }

    private static Map<String, Map<String, Object>> getExcelInfoMap(InputStream inputStream, Map map, Integer x) {
        Map<String, Map<String, Object>> excelMapModel = Maps.newHashMap();
        ExcelReader reader = null;
        try {
            // 1 模板处理 sheetIndex=0代表第一个
            reader = ExcelUtil.getReader(inputStream, 0);
            excelMapModel = Maps.newHashMap();

            // 2 遍历行
            for (int start = x + 1; ; start++) {
                Map<String, Object> rowMap = Maps.newHashMap();

                // 如果这行的value到位null或者空字符则任务结束
                boolean isEmptyRow = true;
                for (int cell = 1; cell <= map.size(); cell++) {
                    Row keyRow = reader.getSheet().getRow(x - 1);
                    if (keyRow == null) {
                        break;
                    }
                    // 标题value当key
                    String key = getValue(keyRow.getCell(cell - 1));
                    if (!map.containsKey(key)) {
                        throw new RuntimeException("模版数据异常，请重新下载模版导入！");
                    }
                    Row row = reader.getSheet().getRow(start - 1);
                    if (row == null) {
                        break;
                    }
                    // 真实的value
                    String value = getValue(row.getCell(cell - 1));

                    if (StringUtils.isNotBlank(value)) {
                        isEmptyRow = false;
                    }
                    String newKey = (String) map.get(key);
                    rowMap.put(newKey, value);
                }
                if (isEmptyRow) {
                    break;
                }
                rowMap.put("lineNum", start);
                excelMapModel.put(String.valueOf(start), rowMap);
            }
        } catch (RuntimeException e) {
            throw e;
        } catch (Throwable e) {
            log.error("解析文件异常", e);
            throw new RuntimeException("导入文件失败！");
        } finally {
            if (reader != null) {
                reader.close();
            }
        }
        return excelMapModel;
    }




    public static <T> void listToExcle(List<ListToExcelMultiSheetDTO> listDto, OutputStream out) {
        ExcelWriter writer = ExcelUtil.getWriter();
        try {
            Integer i = 0;
            for (ListToExcelMultiSheetDTO list : listDto) {

                writer.setSheet(i++).renameSheet(list.getSheetName());
                LinkedHashMap<String, String> fieldMap = list.getFieldMap();
                // 1 写标题
                writer.writeHeadRow(fieldMap.values());
                // 2 写单元格
                int rowNum = 1;
                int cellNum = 0;
                List<T> data = list.getListGroup();
                for (T t : data) {
                    for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
                        Object value = ReflectUtil.getFieldValue(t, entry.getKey());
                        if (value == null) {
                            value = "";
                        }
                        writer.writeCellValue(cellNum, rowNum, value);
                        cellNum++;
                    }
                    rowNum++;
                    cellNum = 0;
                }
            }
        } catch (Exception e) {
            log.info("文件导出异常", e);
        } finally {
            //关闭writer，释放内存
            writer.flush(out);
            writer.close();
        }
    }
}
