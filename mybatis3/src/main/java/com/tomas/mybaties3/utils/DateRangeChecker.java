package com.tomas.mybaties3.utils;

import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

public class DateRangeChecker {

    public static void main(String[] args) {
        String datesStr = "";
        List<LocalDate> firstOpenDate = Arrays.stream("2025-03-07".split(","))
                .filter(dateStr -> !dateStr.isEmpty())
                .map(LocalDate::parse)
                .collect(Collectors.toList());
        System.out.println(firstOpenDate);

    }





}