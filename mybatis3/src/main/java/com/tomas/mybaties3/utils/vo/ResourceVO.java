package com.tomas.mybaties3.utils.vo;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.springframework.util.DigestUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Data
public class ResourceVO {

    private Long id;

    private String name;

    private String icon;

    private BigDecimal stock;

    private BigDecimal stockPice;

    private List<Resource> userList;


    public static void main(String[] args) throws Throwable {
        ResourceVO resourceVO = new ResourceVO();
        resourceVO.setId(123L);
        resourceVO.setName("sss");

        List<Resource> userList=new ArrayList<>();
        Resource a=new Resource();
        a.setName("txte");
        userList.add(a);

        resourceVO.setUserList(userList);
        //System.out.println(ToStringBuilder.reflectionToString(resourceVO, ToStringStyle.DEFAULT_STYLE));


        //System.out.println(ToStringBuilder.reflectionToString(resourceVO, ToStringStyle.MULTI_LINE_STYLE));


        //System.out.println(ToStringBuilder.reflectionToString(resourceVO, ToStringStyle.NO_FIELD_NAMES_STYLE));


        //System.out.println(ToStringBuilder.reflectionToString(resourceVO, ToStringStyle.SIMPLE_STYLE));
        //ExecutorService executorService = Executors.newFixedThreadPool(2);


      //  String string = ToStringBuilder.reflectionToString(resourceVO, ToStringStyle.SIMPLE_STYLE);
        Object[] args1=new Object[1];

        args1[0]=resourceVO;

        ResourceVO resourceVO1 = new ResourceVO();
        resourceVO1.setId(123L);
        resourceVO1.setName("sss");

        List<Resource> userList1=new ArrayList<>();
        Resource a1=new Resource();
        a1.setName("txte");
        userList1.add(a1);

        Object[] args2=new Object[1];

        args2[0]=resourceVO1;

        resourceVO1.setUserList(userList1);
        System.out.println(ToStringBuilder.reflectionToString(args1, ToStringStyle.SIMPLE_STYLE));
        String redisKey = DigestUtils.md5DigestAsHex(ToStringBuilder.reflectionToString(args1, ToStringStyle.JSON_STYLE).getBytes());
        System.out.println(redisKey);

        //System.out.println(ToStringBuilder.reflectionToString(args2, ToStringStyle.SIMPLE_STYLE));
       // String redisKey1 = DigestUtils.md5Hex(ToStringBuilder.reflectionToString(args2, ToStringStyle.SIMPLE_STYLE));
        //System.out.println(redisKey1+"xxx");
        System.out.println(buildParamKey("a",args2));
        //String redisKey3 = DigestUtils.md5Hex(buildParamKey("a",args2));
        //System.out.println(redisKey3);



       /* Long start = System.currentTimeMillis();

        for (int i = 0; i < 10000000; i++) {

            if (i == 10000000-1){
                System.out.println("完事"+(System.currentTimeMillis()-start));
            }
        }*/


    }

    private static String buildParamKey(String prefixKey, Object[] pjp) throws Throwable {
       /* EvaluationContext context = new StandardEvaluationContext();
        // add self define arguments
        context.setVariable("args", pjp);

        ExpressionParser parser = new SpelExpressionParser();

        Expression expression = parser.parseExpression("args");


       // Object obj = scriptContext.getExpression().getValue(context);
        return expression.getValue(context, String.class);*/

/*
        SpelExpressionParser parser = new SpelExpressionParser();
        Expression expression = parser.parseExpression(opLog.OpItemIdExpression());
        EvaluationContext context = new StandardEvaluationContext();

        //获取参数值
        Object[] args =pjp;

        //获取运行时参数的名称
        LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
        String[] parameterNames = discoverer.getParameterNames(method);

        //将参数绑定到context中
        if(parameterNames != null){
            for (int i = 0; i < parameterNames.length; i++) {
                context.setVariable(parameterNames[i],args[i]);
            }
        }
        // 解析表达式，获取结果
        String itemId = String.valueOf(expression.getValue(context));*/
       return  Arrays.toString(pjp);


    };


}
