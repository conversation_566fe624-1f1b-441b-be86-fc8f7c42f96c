package com.tomas.mybaties3.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;

import java.util.List;
import java.util.Map;

public class PromotionStore {


    public static void main(String[] args) {


            // 原始JSON对象
            String jsonObject = "{\n" +
                    "    \"25730\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 872, 25730, \\\"广元芝心\\\", 500, 1, 0, 16, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-08 15:10:05\\\", \\\"2024-10-08 15:10:05\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"3010\\\");\",\n" +
                    "    \"603456\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 68107, 603456, \\\"眉州公司\\\", 500, 1, 0, 16, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-08 16:15:37\\\", \\\"2024-10-08 16:15:37\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"35M0\\\");\",\n" +
                    "    \"66289\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 3016, 66289, \\\"岳池天天康\\\", 500, 1, 0, 16, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-08 16:35:08\\\", \\\"2024-10-08 16:35:08\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"3270\\\");\",\n" +
                    "    \"510424\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 60085, 510424, \\\"川南大区\\\", 500, 1, 0, 16, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-08 16:52:36\\\", \\\"2024-10-08 16:52:36\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"33Y0\\\");\",\n" +
                    "    \"80347\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 3132, 80347, \\\"达州区域\\\", 500, 1, 0, 16, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-08 17:01:42\\\", \\\"2024-10-08 17:01:42\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"30L0\\\");\",\n" +
                    "    \"20532\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 2107, 20532, \\\"南昌开心人\\\", 500, 1, 0, 5285, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-08 17:05:28\\\", \\\"2024-10-08 17:05:28\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"31J0\\\");\",\n" +
                    "    \"66673\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 3072, 66673, \\\"攀枝花敬仁堂\\\", 500, 1, 0, 16, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-08 17:19:28\\\", \\\"2024-10-08 17:19:28\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"32B0\\\");\",\n" +
                    "    \"119771\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 8892, 119771, \\\"雅安和康\\\", 500, 1, 0, 16, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 09:23:19\\\", \\\"2024-10-09 09:23:19\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"30R0\\\");\",\n" +
                    "    \"95410\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 5247, 95410, \\\"新疆济康\\\", 500, 1, 0, 35145, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 10:34:48\\\", \\\"2024-10-09 10:34:48\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"31S0\\\");\",\n" +
                    "    \"572458\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 64266, 572458, \\\"昌吉惠生堂\\\", 500, 1, 0, 35145, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 10:34:48\\\", \\\"2024-10-09 10:34:48\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"34B0\\\");\",\n" +
                    "    \"582961\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 66165, 582961, \\\"喀什惠生堂\\\", 500, 1, 0, 35145, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 11:18:51\\\", \\\"2024-10-09 11:18:51\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"34A0\\\");\",\n" +
                    "    \"84258\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 4528, 84258, \\\"沧州城市公司\\\", 500, 1, 0, 18, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 11:23:33\\\", \\\"2024-10-09 11:23:33\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"31B0\\\");\",\n" +
                    "    \"19484\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 326, 19484, \\\"三门峡华为\\\", 500, 1, 0, 17, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 11:36:02\\\", \\\"2024-10-09 11:36:02\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"3060\\\");\",\n" +
                    "    \"12501\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 327, 12501, \\\"洛阳好一生\\\", 500, 1, 0, 17, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 11:43:13\\\", \\\"2024-10-09 11:43:13\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"3080\\\");\",\n" +
                    "    \"104319\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 5518, 104319, \\\"都江堰兴福\\\", 500, 1, 0, 16, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 14:39:17\\\", \\\"2024-10-09 14:39:17\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"30M0\\\");\",\n" +
                    "    \"73707\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 2938, 73707, \\\"南阳隆泰仁\\\", 500, 1, 0, 17, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 14:56:38\\\", \\\"2024-10-09 14:56:38\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"30F0\\\");\",\n" +
                    "    \"65366\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 2531, 65366, \\\"合肥广济\\\", 500, 1, 0, 5285, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 15:07:24\\\", \\\"2024-10-09 15:07:24\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"30U0\\\");\",\n" +
                    "    \"93544\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 4945, 93544, \\\"梧州百姓\\\", 500, 1, 0, 20, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 15:35:34\\\", \\\"2024-10-09 15:35:34\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"32E0\\\");\",\n" +
                    "    \"92773\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 4947, 92773, \\\"福建大区\\\", 500, 1, 0, 20, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 15:35:34\\\", \\\"2024-10-09 15:35:34\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"31Q0\\\");\",\n" +
                    "    \"118113\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 8891, 118113, \\\"清远百姓\\\", 500, 1, 0, 20, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 15:35:34\\\", \\\"2024-10-09 15:35:34\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"3150\\\");\",\n" +
                    "    \"442122\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 54847, 442122, \\\"江门高济\\\", 500, 1, 0, 20, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 15:35:34\\\", \\\"2024-10-09 15:35:34\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"31Y0\\\");\",\n" +
                    "    \"474952\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 57425, 474952, \\\"广州宝芝林\\\", 500, 1, 0, 20, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 15:35:34\\\", \\\"2024-10-09 15:35:34\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"3310\\\");\",\n" +
                    "    \"564475\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 63574, 564475, \\\"珠海嘉宝华\\\", 500, 1, 0, 20, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 15:35:34\\\", \\\"2024-10-09 15:35:34\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"3540\\\");\",\n" +
                    "    \"573208\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 64568, 573208, \\\"云浮邦健\\\", 500, 1, 0, 20, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 15:35:34\\\", \\\"2024-10-09 15:35:34\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"31Z0\\\");\",\n" +
                    "    \"43352\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 1309, 43352, \\\"乐山海棠\\\", 500, 1, 0, 16, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 15:50:56\\\", \\\"2024-10-09 15:50:56\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"30Q0\\\");\",\n" +
                    "    \"15262\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 59, 15262, \\\"成都一部\\\", 500, 1, 0, 16, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 16:04:12\\\", \\\"2024-10-09 16:04:12\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"3000\\\");\",\n" +
                    "    \"81363\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 3682, 81363, \\\"阜阳公司\\\", 500, 1, 0, 5285, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 16:07:24\\\", \\\"2024-10-09 16:07:24\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"30Y0\\\");\",\n" +
                    "    \"96106\": \"INSERT INTO promotion_store (promotion_id, apply_store, org_id, org_out_id, org_name, org_type, org_show, parent_org_out_id, parent_org_id, parent_code, parent_name, org_pid, dynamic_status, status, gmt_create, gmt_update, created_by, updated_by, extend, version, env, sap_code) VALUES (TT, 1, 5509, 96106, \\\"淮北敬贤堂\\\", 500, 1, 0, 5285, \\\"\\\", \\\"\\\", 0, 0, 1, \\\"2024-10-09 16:32:34\\\", \\\"2024-10-09 16:32:34\\\", \\\"1305105906820532\\\", \\\"1305105906820532\\\", \\\"\\\", 1, \\\"pro\\\", \\\"30V0\\\");\"\n" +
                    "}";

            // 输出JSON字符串
        String mapStr="" +
                "{\n" +
                "  \"14154135\": [66289, 43352],\n" +
                "  \"14154259\": [66673, 25730, 564475, 603456, 15262],\n" +
                "  \"14154258\": [66673, 564475, 603456, 15262],\n" +
                "  \"14154257\": [66673, 603456],\n" +
                "  \"14154335\": [73707, 15262],\n" +
                "  \"14154207\": [66289, 66673, 603456, 15262, 510424],\n" +
                "  \"14154206\": [66289, 66673, 603456, 15262],\n" +
                "  \"14154014\": [66289, 603456],\n" +
                "  \"14154205\": [66673, 603456, 15262],\n" +
                "  \"14154140\": [66289, 84258, 95410, 43352, 603456, 93544, 119771, 15262, 474952],\n" +
                "  \"14154139\": [66289, 66673, 572458, 43352, 603456, 93544, 474952],\n" +
                "  \"14154138\": [66289, 66673, 572458, 43352, 93544, 474952],\n" +
                "  \"14154137\": [66289, 43352, 93544, 119771, 474952],\n" +
                "  \"14154136\": [66289, 43352, 93544, 119771],\n" +
                "  \"14154050\": [66289, 95410, 564475, 582961, 119771, 15262, 510424],\n" +
                "  \"14153527\": [572458, 84258, 603456, 93544, 96106, 474952, 66289, 66673, 81363, 95410, 12501, 65366, 582961, 80347, 19484],\n" +
                "  \"14153526\": [66673, 572458, 81363, 84258, 95410, 12501, 65366, 582961, 603456, 93544, 119771, 474952],\n" +
                "  \"14153525\": [66673, 572458, 81363, 84258, 95410, 12501, 65366, 582961, 93544, 119771, 474952],\n" +
                "  \"14153524\": [572458, 81363, 84258, 95410, 92773, 12501, 65366, 582961, 119771, 80347, 474952],\n" +
                "  \"14153523\": [66289, 66673, 572458, 84258, 12501, 65366, 582961, 119771, 474952],\n" +
                "  \"14153522\": [66289, 572458, 564475, 12501, 65366, 119771],\n" +
                "  \"14153521\": [95410, 12501, 65366],\n" +
                "  \"14154352\": [20532, 65366],\n" +
                "  \"14154303\": [573208, 118113, 25730, 564475, 603456, 119771, 73707, 96106, 442122, 15262, 104319],\n" +
                "  \"14154302\": [573208, 118113, 25730, 564475, 603456, 119771, 73707, 442122, 15262, 104319],\n" +
                "  \"14154109\": [66289, 582961],\n" +
                "  \"14154301\": [118113, 25730, 564475, 603456, 119771, 73707, 442122, 15262, 104319],\n" +
                "  \"14154300\": [25730, 603456, 119771, 73707, 442122, 15262, 104319],\n" +
                "  \"14154299\": [25730, 603456, 119771, 73707, 442122, 15262, 104319],\n" +
                "  \"14154043\": [66289, 95410, 603456, 119771, 15262, 510424],\n" +
                "  \"14154298\": [603456, 119771, 73707, 442122, 15262, 104319],\n" +
                "  \"14154297\": [95410, 119771, 442122, 15262],\n" +
                "  \"14154296\": [442122, 15262],\n" +
                "  \"14153511\": [95410, 12501],\n" +
                "  \"14154277\": [25730, 95410, 564475, 603456, 119771, 73707, 442122, 15262],\n" +
                "  \"14154276\": [25730, 603456, 119771, 73707, 442122, 15262],\n" +
                "  \"14154275\": [25730, 603456, 119771, 73707, 442122, 15262],\n" +
                "  \"14154274\": [15262],\n" +
                "  \"14154146\": [603456, 119771],\n" +
                "  \"14154273\": [15262],\n" +
                "  \"14154017\": [66289, 95410, 603456, 119771, 15262],\n" +
                "  \"14154208\": [66289, 66673, 603456, 15262, 510424],\n" +
                "  \"14153390\": [66673, 25730, 95410, 92773, 603456, 474952],\n" +
                "  \"14153389\": [25730, 95410, 92773, 603456],\n" +
                "  \"14153388\": [66673, 25730, 92773, 603456],\n" +
                "  \"14153387\": [66673, 603456],\n" +
                "  \"14154024\": [66289, 95410, 603456, 119771, 15262, 510424]\n" +
                "}";

       // System.out.println(mapStr);


         Map<Integer, List<Integer>> map = JSON.parseObject(mapStr, new TypeReference<Map<Integer, List<Integer>>>() {});


        Map<Integer, String> map2 = JSON.parseObject(jsonObject, new TypeReference<Map<Integer, String>>() {});

        System.out.println(map2.size());
        // 输出解析结果
        for (Map.Entry<Integer, List<Integer>> entry : map.entrySet()) {
           System.out.print(entry.getKey() + ",");

        }

//        for (Map.Entry<Integer, String> entry : map2.entrySet()) {
//            System.out.println("Key: " + entry.getKey() + ", Value: " + entry.getValue());
//        }
    }
}
