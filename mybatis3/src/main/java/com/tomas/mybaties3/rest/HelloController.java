package com.tomas.mybaties3.rest;

import com.alibaba.fastjson.JSON;
import com.tomas.mybaties3.dao.TransitGoodsInfoMapper;
import com.tomas.mybaties3.dao.UserMapper;
import com.tomas.mybaties3.dao.jdbc.UserJDBCDao;
import com.tomas.mybaties3.entity.ManageBatchUpdateParam;
import com.tomas.mybaties3.entity.TransitGoodsInfo;
import com.tomas.mybaties3.entity.VoteRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@RestController
@RequestMapping("/mybatis3")
public class HelloController {

    @Autowired
    UserMapper userMapper;

    @Autowired
    TransitGoodsInfoMapper transitGoodsInfoMapper;

    @Autowired
    UserJDBCDao userJDBCDao;

    AtomicInteger atomicInteger=new AtomicInteger(0);
    @RequestMapping("user/info")
    public Object index(String uid) {
        return System.nanoTime();
    }

    @RequestMapping("user/jdbc")
    public Object jdbc(String uid) {
        //System.out.println(System.nanoTime());
        VoteRecord voteRecord = null;
        try {
            voteRecord = userJDBCDao.selectByUserId(uid);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
            return System.nanoTime();
        }
        if (null!=voteRecord){
            return voteRecord;
        }
        System.out.println(atomicInteger.incrementAndGet());
        return System.nanoTime();
    }


    @GetMapping("/get")
    public String get(List<String> list) {
        System.out.println(JSON.toJSONString(list));
        return JSON.toJSONString(list);
    }
    @GetMapping("/getList")
    public String getList(@RequestParam List<String> list) {
        System.out.println(JSON.toJSONString(list));
        return JSON.toJSONString(list);
    }



    @GetMapping("/update")
    public String update(Integer vnum) {
        List<VoteRecord> list= new ArrayList<>();
        VoteRecord u=new VoteRecord();
        u.setId(11L);
        list.add(u);
        VoteRecord u2=new VoteRecord();
        u2.setId(12L);
        list.add(u2);
        userMapper.updateTransitQuantityBatch(vnum,list);
        return JSON.toJSONString(list);
    }




    @PostMapping("/getBatchList")
    public String getBatchList(@RequestBody List<String> goodsList) {
        List<TransitGoodsInfo> transitGoodsInfos = transitGoodsInfoMapper.queryTransitInfoByGoodsNos(131763L, 2650760731763L, goodsList);
        transitGoodsInfos.stream().forEach(v->{
            v.setQuantity(v.getQuantity().multiply(BigDecimal.valueOf(-1)));
            v.setBusinessId(131763L);
            v.setStoreId(2650760731763L);
            v.setVersion(2);
            v.setStatus(0);
        });
        return JSON.toJSONString(transitGoodsInfos);
    }

    @PostMapping("/updateBatch")
    public String updateBatch(@RequestBody List<TransitGoodsInfo> list) {
        transitGoodsInfoMapper.insertOrUpdateList(list);
        return JSON.toJSONString(list);
    }

    @PostMapping("/updateOne")
    public String updateOne(@RequestBody List<TransitGoodsInfo> list) {
        list.stream().forEach(recode->{
            transitGoodsInfoMapper.updateTransitQuantity(recode);
        });
        return JSON.toJSONString(list);
    }

    /**
     * 导入exce
     */
    @PostMapping("/test/import")
    public void importExcel() {
    }

    @PostMapping("/manage/batchUpdate")
    public ResponseEntity<Object> batchUpdate(HttpServletRequest request, @RequestBody ManageBatchUpdateParam manageCommonDTOList){
        log.info("manageCommonDTOList:{}", JSON.toJSONString(manageCommonDTOList));
        return ResponseEntity.ok(manageCommonDTOList);
    }




}
