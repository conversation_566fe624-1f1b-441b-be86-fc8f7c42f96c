package com.tomas.mybaties3.rest;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.client.RestTemplate;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/hana")
public class HanaController {

    @Autowired
    RestTemplate restTemplate;

    @Value("#{'${stockcenter.jmdAttr.list:加盟事业部-分支结构,加盟事业部-个人独资, 加盟事业部-特许加盟,加盟-单体-药证含加盟,加盟-单体-药证不含济加盟,加盟-连锁-药证含加盟,加盟-连锁-药证不含济加盟}'.split(',')}")
    private List<String> jmdAttrList;

   // @RequestMapping("/getOne")

    @RequestMapping(value = "/getDictOptions", method = {RequestMethod.POST, RequestMethod.GET})
    public String updateOne(String attr, HttpServletRequest request) {

        // 根据请求方法获取参数
        if (request.getMethod().equalsIgnoreCase("POST")) {
          return  "POST";
        } else if (request.getMethod().equalsIgnoreCase("GET")) {
            return "GET";
        }
        return  jmdAttrList.contains(attr)+"";
    }





}
