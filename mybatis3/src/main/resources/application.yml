server:
  port: 8081
spring:
  datasource:
    name: test
    url: *****************************************************************************************
    username: root
    password: 123456
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver

  thymeleaf:
    prefix: classpath:/templates/
    suffix: .html
    mode: LEGACYHTML5
    cache: false
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB
mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.tomas.mybatis
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


logging:
  config: classpath:logback-spring.xml
  level:
    org:
      mybatis:
        spring: DEBUG
      springframework: DEBUG
    com:
      tomas:
        mybatis:
          dao: DEBUG
      ibatis:
       common.jdbc.SimpleDataSource: DEBUG
       common.jdbc.ScriptRunner: DEBUG
       sqlmap.engine.impl.SqlMapClientDelegate: DEBUG
    java.sql:
     Connection: DEBUG
     Statement: DEBUG
     PreparedStatement: DEBUG


