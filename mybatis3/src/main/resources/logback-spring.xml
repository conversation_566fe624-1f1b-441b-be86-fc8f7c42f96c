<?xml version="1.0" encoding="UTF-8" ?>
<configuration>
    <!-- 控制台输出日志 -->
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>[%X{trace_id}] %d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [%thread] [%c] %L - %msg%n</pattern>
        </encoder>
    </appender>
    <!--每天生成一个日志文件，保存30天的日志文件。-->
    <appender name="DayFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>/Users/<USER>/code/github/SpringBoot-Learning/01log/mybatis3.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>01log/mybatis3.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>30</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>[%X{trace_id}] %d{yyyy-MM-dd HH:mm:ss.SSS} [%level] [%thread] [%c] %L - %msg%n</pattern>
        </encoder>
    </appender>
    <!--指定logger name为包名或类全名 指定级别 additivity设置是否传递到root logger -->
    <logger name="slf4j" level="INFO" additivity="false">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DayFile"/>
    </logger>
    <!--slf4j2包下的类在ERROR级别时候传递到root logger中-->
    <logger name="slf4j2" level="ERROR"/>
    <!--根logger控制-->
    <root level="INFO">
        <appender-ref ref="STDOUT"/>
        <appender-ref ref="DayFile"/>
    </root>
</configuration>
