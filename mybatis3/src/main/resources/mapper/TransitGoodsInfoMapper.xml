<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tomas.mybaties3.dao.TransitGoodsInfoMapper">
  <resultMap id="BaseResultMap" type="com.tomas.mybaties3.entity.TransitGoodsInfo">
      <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="store_id" jdbcType="BIGINT" property="storeId" />
    <result column="sku_merchant_code" jdbcType="VARCHAR" property="skuMerchantCode" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="quantity" jdbcType="DECIMAL" property="quantity" />
    <result column="bar_code" jdbcType="VARCHAR" property="barCode" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="created_by" jdbcType="VARCHAR" property="createdBy" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="updated_by" jdbcType="VARCHAR" property="updatedBy" />
    <result column="gmt_update" jdbcType="TIMESTAMP" property="gmtUpdate" />
    <result column="status" jdbcType="INTEGER" property="status" />
  </resultMap>

  <insert id="insert" parameterType="com.tomas.mybaties3.entity.TransitGoodsInfo">
    insert into transit_goods_info (business_id, store_id,
      sku_merchant_code, goods_name, quantity,
      bar_code, version, created_by,
      gmt_create, updated_by, gmt_update,
      status)
    values (#{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT},
      #{skuMerchantCode,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{quantity,jdbcType=DECIMAL},
      #{barCode,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR},
      #{gmtCreate,jdbcType=TIMESTAMP}, #{updatedBy,jdbcType=VARCHAR}, #{gmtUpdate,jdbcType=TIMESTAMP},
      #{status,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.tomas.mybaties3.entity.TransitGoodsInfo">
    insert into transit_goods_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="storeId != null">
        store_id,
      </if>
      <if test="skuMerchantCode != null">
        sku_merchant_code,
      </if>
      <if test="goodsName != null">
        goods_name,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="barCode != null">
        bar_code,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="gmtCreate != null">
        gmt_create,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="gmtUpdate != null">
        gmt_update,
      </if>
      <if test="status != null">
        status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="storeId != null">
        #{storeId,jdbcType=BIGINT},
      </if>
      <if test="skuMerchantCode != null">
        #{skuMerchantCode,jdbcType=VARCHAR},
      </if>
      <if test="goodsName != null">
        #{goodsName,jdbcType=VARCHAR},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=DECIMAL},
      </if>
      <if test="barCode != null">
        #{barCode,jdbcType=VARCHAR},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtCreate != null">
        #{gmtCreate,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=VARCHAR},
      </if>
      <if test="gmtUpdate != null">
        #{gmtUpdate,jdbcType=TIMESTAMP},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>

   <insert id="insertOrUpdate" parameterType="com.tomas.mybaties3.entity.TransitGoodsInfo">
        insert into transit_goods_info (business_id, store_id,
        sku_merchant_code, goods_name, quantity,
        bar_code, version, created_by,
        gmt_create, updated_by, gmt_update,
        status)
        values (#{businessId,jdbcType=BIGINT}, #{storeId,jdbcType=BIGINT},
        #{skuMerchantCode,jdbcType=VARCHAR}, #{goodsName,jdbcType=VARCHAR}, #{quantity,jdbcType=DECIMAL},
        #{barCode,jdbcType=VARCHAR}, #{version,jdbcType=INTEGER}, #{createdBy,jdbcType=VARCHAR},
       now(), #{updatedBy,jdbcType=VARCHAR}, now(),
        #{status,jdbcType=INTEGER})
        ON DUPLICATE KEY UPDATE
        quantity=values(quantity)+quantity
    </insert>

    <insert id="insertOrUpdateList" parameterType="java.util.List">
        insert into transit_goods_info (business_id, store_id,
        sku_merchant_code, goods_name, quantity,
        bar_code, version, created_by,
        gmt_create, updated_by, gmt_update,
        status)
        values
        <foreach collection="list" item="item" index="index" separator=",">
        (#{item.businessId,jdbcType=BIGINT}, #{item.storeId,jdbcType=BIGINT},
        #{item.skuMerchantCode,jdbcType=VARCHAR}, #{item.goodsName,jdbcType=VARCHAR},
        #{item.quantity,jdbcType=DECIMAL},
        #{item.barCode,jdbcType=VARCHAR}, #{item.version,jdbcType=INTEGER}, #{item.createdBy,jdbcType=VARCHAR},
        now(), #{item.updatedBy,jdbcType=VARCHAR}, now(),
        #{item.status,jdbcType=INTEGER})
        </foreach>
        ON DUPLICATE KEY UPDATE
        quantity=IF((values(quantity)+quantity)&lt;0,0,values(quantity)+quantity)
    </insert>

    <select id="queryTransitInfoByGoodsNos" resultMap="BaseResultMap">
        select
        sku_merchant_code,quantity
        from transit_goods_info
        where business_id = #{businessId,jdbcType=BIGINT}
         and store_id = #{storeId,jdbcType=BIGINT}
        <if test="goodsList != null and goodsList.size()>0">
            and sku_merchant_code in
            <foreach collection="goodsList" index="index" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>
         and status = 0
    </select>

    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" index="index" open="" close="" separator=";">
        update transit_goods_info
        set quantity= if((#{item.quantity}+quantity)&lt;0,0,(#{item.quantity}+quantity))
            where business_id=#{item.businessId}
            and store_id=#{item.storeId}
            and sku_merchant_code=#{item.skuMerchantCode}
        </foreach>
    </update>
    <update id="updateTransitQuantity" parameterType="com.tomas.mybaties3.entity.TransitGoodsInfo">
            update transit_goods_info
            set quantity= if((#{quantity}+quantity)&lt;0,0,(#{quantity}+quantity))
            where business_id=#{businessId}
            and store_id=#{storeId}
            and sku_merchant_code=#{skuMerchantCode}
    </update>

</mapper>
