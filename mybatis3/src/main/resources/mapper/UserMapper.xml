<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tomas.mybaties3.dao.UserMapper">
    <resultMap id="BaseResultMap" type="com.tomas.mybaties3.entity.VoteRecord">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="vote_num" property="voteNum" jdbcType="INTEGER"/>
        <result column="version" property="version" jdbcType="INTEGER"/>
    </resultMap>

    <sql id="Base_Column_List2">
      id,user_id,vote_num,create_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List2"/>
        from vote
        where id = #{id}
    </select>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List2"/>
        from vote
        where 1=1
        <if test="ids!=null and  ids.size>0">
            and id in
            <foreach collection="ids" item="item" index="index" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>

    </select>
    <select id="selectByUserId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List2"/>
        from vote
        where user_id = #{userId}
    </select>

    <update id="updateVersionLock" keyProperty="id" parameterType="com.tomas.mybaties3.entity.VoteRecord">
        update vote
        set vote_num=#{vote_num},
            version = version + 1
        where id = #{id}
          and version = #{version}
    </update>

    <!--数据源开启 允许一次多语句查询  &allowMultiQueries=true  -->
    <update id="updateVersionLockBatch" parameterType="com.tomas.mybaties3.entity.VoteRecord">
         <foreach collection="list" item="item" index="index">
             update vote
             set vote_num = #{item.voteNum}, version = version + 1
             where id = #{item.id};
         </foreach>
    </update>

    <!--数据源开启  没有开启  &allowMultiQueries=true  -->
    <update id="updateVersionLockBatchCaseWhen" parameterType="com.tomas.mybaties3.entity.VoteRecord">
        update vote
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="vote_num =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.id!=null">
                        when id=#{i.id} then #{i.voteNum}
                    </if>
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list"  item="i" index="index" open="(" separator="," close=")">
            #{i.id}
        </foreach>
    </update>


    <update id="batchUpdateVersionByUserId">
        update
        vote
        set  version = version+1
        where user_id = #{userId}
        <if test="ids != null and ids.size()!=0">
            and id in
            <foreach collection="ids" index="index" item="tid" open="(" separator="," close=")">
                #{tid}
            </foreach>
        </if>
    </update>

    <select id="selectByIdForUpdate" resultMap="BaseResultMap">
        select *
        from vote
        where id = #{id} for
        update
    </select>

    <update id="updateTransitQuantityBatch" >
        update vote
        set version= 0
        where vote_num  =  #{voteNum}
        <![CDATA[  and id >  #{voteNum} ]]>
        <if test="transitlist != null and transitlist.size > 0">
            and id in
            <foreach collection="transitlist" index="index" item="transitItem" open="(" separator="," close=")">
                #{transitItem.id,jdbcType=BIGINT}
            </foreach>
        </if>
    </update>

<!--    <select id="selectByBatchNumList" resultMap="BaseResultMap">
        select
            * from vote
        where  (user_id,vote_num)  IN
        <foreach collection="batchNumList" item="batchNum" separator="," open="(" close=")">
            (#{batchNum.userId} , #{batchNum.voteNum})
        </foreach>
        order by id desc
    </select>-->
</mapper>
