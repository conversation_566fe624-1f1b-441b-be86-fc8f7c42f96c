server:
  port: 8081
spring:
  ds0:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************************************
    username: root
    password: Gaoji_001#
    initial-size: 5
    min-idle: 1
    max-active: 5
    max-wait: 60000
    validation-query: SELECT 1
    test-while-idle: true
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 30000
    max-evictable-idle-time-millis: 60000
  ds1:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************************************
    username: root
    password: Gaoji_001#
    initial-size: 5
    min-idle: 1
    max-active: 5
    max-wait: 60000
    validation-query: SELECT 1
    test-while-idle: true
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 30000
    max-evictable-idle-time-millis: 60000
  ds2:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************************************
    username: root
    password: Gaoji_001#
    initial-size: 5
    min-idle: 1
    max-active: 5
    max-wait: 60000
    validation-query: SELECT 1
    test-while-idle: true
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 30000
    max-evictable-idle-time-millis: 60000
  ds3:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************************************
    username: root
    password: Gaoji_001#
    initial-size: 5
    min-idle: 1
    max-active: 5
    max-wait: 60000
    validation-query: SELECT 1
    test-while-idle: true
    time-between-eviction-runs-millis: 60000
    min-evictable-idle-time-millis: 30000
    max-evictable-idle-time-millis: 60000

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.tomas.mybatis
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl


logging:
  config: classpath:logback-spring.xml
  level:
    org:
      mybatis:
        spring: DEBUG
      springframework: DEBUG
    com:
      tomas:
        mybatis:
          dao: DEBUG
      ibatis:
       common.jdbc.SimpleDataSource: DEBUG
       common.jdbc.ScriptRunner: DEBUG
       sqlmap.engine.impl.SqlMapClientDelegate: DEBUG
    java.sql:
     Connection: DEBUG
     Statement: DEBUG
     PreparedStatement: DEBUG


