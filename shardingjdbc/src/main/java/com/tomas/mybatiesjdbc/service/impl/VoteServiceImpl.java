package com.tomas.mybatiesjdbc.service.impl;

import com.alibaba.fastjson.JSON;
import com.tomas.mybatiesjdbc.entity.VoteRecord;
import com.tomas.mybatiesjdbc.dao.UserMapper;
import com.tomas.mybatiesjdbc.service.VoteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * VoteServiceImpl 描述
 *
 * <AUTHOR>
 * @create 2021/1/21
 **/
@Slf4j
@Service("voteService")
public class VoteServiceImpl implements VoteService {

    @Autowired
    UserMapper userMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean leguanLock(String uid) {

        VoteRecord voteRecord = userMapper.selectByUserId(uid);
        if (null!=voteRecord){
            return false;
        }
        voteRecord.setVoteNum(voteRecord.getVoteNum()+1);
        log.info("voteRecord={}", JSON.toJSONString(voteRecord));
        VoteRecord voteRecord2 = userMapper.selectByUserId(uid);
        log.info("voteRecord2={}",JSON.toJSONString(voteRecord2));
        return userMapper.updateVersionLock(voteRecord)>0;
    }

}
