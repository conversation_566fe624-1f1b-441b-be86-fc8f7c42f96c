package com.tomas.mybatiesjdbc.dao;

import com.tomas.mybatiesjdbc.entity.VoteRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface UserMapper {

    VoteRecord selectById(@Param("id") Integer id);

    List<VoteRecord>  selectByIds(@Param("ids") List<Long> ids);

    VoteRecord selectByUserId(@Param("userId") String uid);

    int  updateVersionLock(VoteRecord vote);

    int updateVersionLockBatch(@Param("list") List<VoteRecord> list);

    int updateVersionLockBatchCaseWhen(@Param("list") List<VoteRecord> list);

    int batchUpdateVersionByUserId(@Param("userId") String userId,@Param("ids") List<Long> ids);

    VoteRecord  selectByIdForUpdate(@Param("id") Integer id);
}
