package com.tomas.mybatiesjdbc.rest;

import com.alibaba.fastjson.JSON;
import com.tomas.mybatiesjdbc.dao.UserMapper;
import com.tomas.mybatiesjdbc.dao.jdbc.UserJDBCDao;
import com.tomas.mybatiesjdbc.entity.VoteRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.SQLException;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@RestController
@RequestMapping("/mybatis3")
public class HelloController {

    @Autowired
    UserMapper userMapper;

    @Autowired
    UserJDBCDao userJDBCDao;

    AtomicInteger atomicInteger=new AtomicInteger(0);
    @RequestMapping("user/info")
    public Object index(String uid) {
        return System.nanoTime();
    }

    @RequestMapping("user/jdbc")
    public Object jdbc(String uid) {
        //System.out.println(System.nanoTime());
        VoteRecord voteRecord = null;
        try {
            voteRecord = userJDBCDao.selectByUserId(uid);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        } catch (SQLException e) {
            e.printStackTrace();
            return System.nanoTime();
        }
        if (null!=voteRecord){
            return voteRecord;
        }
        System.out.println(atomicInteger.incrementAndGet());
        return System.nanoTime();
    }


    @GetMapping("/get")
    public String get(List<String> list) {
        System.out.println(JSON.toJSONString(list));
        return JSON.toJSONString(list);
    }
    @GetMapping("/getList")
    public String getList(@RequestParam List<String> list) {
        System.out.println(JSON.toJSONString(list));
        return JSON.toJSONString(list);
    }
}
