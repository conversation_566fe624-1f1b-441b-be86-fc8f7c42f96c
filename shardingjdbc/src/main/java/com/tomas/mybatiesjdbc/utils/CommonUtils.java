package com.tomas.mybatiesjdbc.utils;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Array;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 公共utils
 */
public class CommonUtils {

    private static final Logger logger = LoggerFactory.getLogger(CommonUtils.class);

    /**
     * 将字符串拆分到list中
     * @param strs
     * @return
     */
    public static List<String> split(String strs) {
        return split(strs, ",");
    }

    public static List<Long> splitToLong(String strs) {
        if(StringUtils.isEmpty(strs)) {
            return new ArrayList<>();
        }
        try {
            return split(strs).stream().map(str -> Long.parseLong(str)).collect(Collectors.toList());
        } catch (Exception e) {
            logger.warn("split2Long error", e);
            return new ArrayList<>();
        }
    }

    public static List<String> split(String strs, String seperator) {
        if(StringUtils.isEmpty(strs)) {
            return new ArrayList<>();
        }
        Splitter splitter = Splitter.on(seperator);
        List<String> list = Lists.newArrayList(splitter.split(strs));
        return list;
    }

    /**
     * 将list组合为逗号分隔的字符串
     * @param list
     * @return
     */
    public static String join(List<String> list) {
        return join(list, ",");
    }

    public static String join(List<String> list, String seperator) {
        if(CollectionUtils.isEmpty(list)) {
            return "";
        }
        return Joiner.on(seperator).join(list);
    }

    /**
     * 判断对象是否为空
     *
     * @param obj
     * @return
     */
    public static boolean isNull(Object obj) {
        boolean result = false;
        if (obj == null) {
            result = true;
            return result;
        }
        if (obj instanceof String && obj.toString().trim().length() == 0) {
            result = true;
            return result;
        }
        if (obj.getClass().isArray() && Array.getLength(obj) == 0) {
            result = true;
            return result;
        }
        if (obj instanceof Collection && ((Collection) obj).isEmpty()) {
            result = true;
            return result;
        }
        if (obj instanceof Map && ((Map) obj).isEmpty()) {
            result = true;
            return result;
        }
        return result;
    }

    /**
     * 判断对象非空
     *
     * @param obj
     * @return
     */
    public static boolean isNotNull(Object obj) {
        return !isNull(obj);
    }

    /**
     * 判断对象是否为0
     *
     * @param obj
     * @return
     */
    public static boolean isZero(Object obj) {
        boolean result = false;
        if (obj == null) {
            result = true;
            return result;
        }
        if (obj instanceof Integer && ((Integer) obj).intValue() == 0) {
            result = true;
            return result;
        }
        if (obj instanceof BigDecimal && ((BigDecimal) obj).compareTo(new BigDecimal(0)) == 0) {
            result = true;
            return result;
        }
        if (obj instanceof Long && ((Long) obj).longValue() == 0) {
            result = true;
            return result;
        }
        if (obj instanceof Short && ((Short) obj).intValue() == 0) {
            result = true;
            return result;
        }
        return result;
    }

    /**
     * 判断对象是否小于等于0
     *
     * @param obj
     * @return
     */
    public static boolean lessZero(Object obj) {
        boolean result = false;
        if (obj == null) {
            result = true;
            return result;
        }
        if (obj instanceof Integer && ((Integer) obj).intValue() < 0) {
            result = true;
            return result;
        }
        if (obj instanceof BigDecimal && (((BigDecimal) obj).compareTo(new BigDecimal(0)) < 0)) {
            result = true;
            return result;
        }
        if (obj instanceof Long && ((Long) obj).longValue() < 0) {
            result = true;
            return result;
        }
        if (obj instanceof Short && ((Short) obj).intValue() < 0) {
            result = true;
            return result;
        }
        return result;
    }


    /**
     * 按指定大小，分隔集合，将集合按规定个数分为n个部分
     *
     * @param list
     * @param pageSize
     * @param <T>
     * @return
     */
    public static <T> List<List<T>> splitList(List<T> list, int pageSize) {
        if (list == null || list.isEmpty() || pageSize < 1) {
            return Collections.emptyList();
        }
        List<List<T>> result = new ArrayList<>();

        int size = list.size();
        int count = (size + pageSize - 1) / pageSize;

        for (int i = 0; i < count; i++) {
            List<T> subList = list.subList(i * pageSize, ((i + 1) * pageSize > size ? size : pageSize * (i + 1)));
            result.add(subList);
        }

        return result;

    }

    /**
     * 判断是否有数据更新
     *
     * @param affectRows
     * @return
     */
    public static boolean noDBModified(int affectRows) {
        if (affectRows < 1) {
            return true;
        }
        return false;
    }

    /**
     * 组装String
     *
     * @param msg
     * @param params
     * @return
     */
    public static String getParams(String msg, Object... params) {
        return String.format(msg, params);
    }

    /**
     * 根据订单号等单号生成hashcode
     *
     * @param relateId
     * @return
     */
    public static String getHashCode(String relateId) {
        int hashCode = Math.abs(relateId.hashCode());
        if (hashCode >= 10000) {
            return String.valueOf(hashCode);
        }
        if (hashCode >= 1000) {
            return "0" + hashCode;
        }
        if (hashCode >= 100) {
            return "00" + hashCode;
        }
        if (hashCode >= 10) {
            return "000" + hashCode;
        }
        if (hashCode >= 0) {
            return "0000" + hashCode;
        }
        return "00000";
    }

    /**
     * @param targetClazz java.lang.reflect.Field 的数据类型 clazz。 getType 方法获取
     * @return
     */
    public static boolean isNumberType(Class<?> targetClazz) {
        // 判断包装类
        if (Number.class.isAssignableFrom(targetClazz)) {
            return true;
        }
        // 判断原始类,过滤掉特殊的基本类型
        if (targetClazz == boolean.class || targetClazz == char.class || targetClazz == void.class) {
            return false;
        }
        return targetClazz.isPrimitive();
    }

    public static boolean objWithNull(Object object){
        if(Objects.isNull(object)){
            return false;
        }
        String objStr = object.toString();
        if(StringUtils.isEmpty(objStr)){
            return false;
        }
        if("null".equals(objStr)){
            return false;
        }
        return true;
    }

}
