package com.tomas.mybatiesjdbc.config.algorithm;

import com.cowell.framework.utils.IdUtils;
import io.shardingsphere.api.algorithm.sharding.ListShardingValue;
import io.shardingsphere.api.algorithm.sharding.ShardingValue;
import io.shardingsphere.api.algorithm.sharding.complex.ComplexKeysShardingAlgorithm;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

/**
 * 支付明细分库算法
 *
 * <AUTHOR>
 * @version PaymentShardingDatabaseAlgorithm.java, 2018/6/04 17:02
 */
public class BusinessAndStoreIdShardingDatabaseAlgorithm implements ComplexKeysShardingAlgorithm {

    private static final int DB_COUNT = 4;


    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, Collection<ShardingValue> shardingValues) {
        Collection<Long> values = getShardingValue(shardingValues, "store_id");
//        Collection<Long> businessIdValues = getShardingValue(shardingValues, "business_id");
//        Collection<Long> values = (storeIdValues != null && storeIdValues.size() > 0) ? storeIdValues : businessIdValues;
        List<String> shardingSuffix = new ArrayList<>();
        if (values != null && values.size() > 0) {
            for (Long val : values) {



                Long lastFiveNum = Long.parseLong(IdUtils.getFirstNumber(IdUtils.getLastNumber(val.toString(),10),5));
                Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber(lastFiveNum.toString(),3));



//                Long lastFiveNum = Long.parseLong(IdUtils.getLastNumber(val, 5));
//                Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber(val, 3));
                Long dbNum = (lastFiveNum - lastThreeNum) / 1000;
                // 后五位前两位分库
                String suffix = "_" + dbNum % DB_COUNT + "";
                availableTargetNames.forEach(x -> {
                    if (x.endsWith(suffix)) {
                        shardingSuffix.add(x);
                    }
                });
            }
        }

        return shardingSuffix;
    }

    private Collection<Long> getShardingValue(Collection<ShardingValue> shardingValues, final String key) {
        Collection<Long>        valueSet = new ArrayList<>();
        Iterator<ShardingValue> iterator = shardingValues.iterator();
        while (iterator.hasNext()) {
            ShardingValue next = iterator.next();
            if (next instanceof ListShardingValue) {
                ListShardingValue value = (ListShardingValue) next;
                if (value.getColumnName().equals(key)) {
                    return value.getValues();
                }
            }
        }
        return valueSet;
    }

}
