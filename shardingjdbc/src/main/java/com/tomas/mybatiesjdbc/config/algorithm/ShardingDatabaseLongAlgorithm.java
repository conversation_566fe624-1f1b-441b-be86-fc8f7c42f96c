package com.tomas.mybatiesjdbc.config.algorithm;

import com.cowell.framework.utils.IdUtils;
import io.shardingsphere.api.algorithm.sharding.PreciseShardingValue;
import io.shardingsphere.api.algorithm.sharding.standard.PreciseShardingAlgorithm;

import java.util.Collection;

/**
 * 订单分库算法
 *
 * @author: liubw
 * @date 2018/10/29 5:16 PM
 */
public class ShardingDatabaseLongAlgorithm implements PreciseShardingAlgorithm<Long> {

    private static final int DB_COUNT = 2;

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<Long> shardingValue) {
        Long lastFiveNum = Long.parseLong(IdUtils.getLastNumber(shardingValue.getValue(), 5));
        Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber(shardingValue.getValue(), 3));
        Long dbNum = (lastFiveNum - lastThreeNum) / 1000;
        long ext = dbNum % DB_COUNT;
        for (String name : availableTargetNames) {
            if (name.endsWith("_" + ext)) {
                return name;
            }
        }
        return null;
    }
}
