package com.tomas.mybatiesjdbc.config.algorithm;

import com.cowell.framework.utils.IdUtils;
import io.shardingsphere.api.algorithm.sharding.ListShardingValue;
import io.shardingsphere.api.algorithm.sharding.ShardingValue;
import io.shardingsphere.api.algorithm.sharding.complex.ComplexKeysShardingAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;
import java.util.LinkedHashSet;
import java.util.Objects;

public class MultipleKeysTableAlgorithm implements ComplexKeysShardingAlgorithm {

    private final Logger log = LoggerFactory.getLogger(MultipleKeysTableAlgorithm.class);

    /**
     * 根据分片值计算分片结果名称集合.
     *
     * @param availableTargetNames 所有的可用目标名称集合, 一般是数据源或表名称
     * @param shardingValues       分片值集合
     * @return 分片后指向的目标名称集合, 一般是数据源或表名称
     */
    @Override
    public Collection<String> doSharding(Collection<String> availableTargetNames, Collection<ShardingValue> shardingValues) {
        Collection<String> result = new LinkedHashSet<>(availableTargetNames.size());
        for (ShardingValue shardingValue : shardingValues) {
            if (shardingValue instanceof ListShardingValue) {
                ListShardingValue value = (ListShardingValue) shardingValue;
                Collection<Object> values = value.getValues();
                for (Object val : values) {
                    for (String tableName : availableTargetNames) {
                        IdUtils.ShardingDO last5SharingValue = getSharding(Objects.toString(val),value.getColumnName());
                        if (tableName.endsWith(last5SharingValue.getTableShardValue() % 128 + "")) {
                            result.add(tableName);
                            return result;
                        }
                    }
                }

            }
        }
        return result;
    }
    public static IdUtils.ShardingDO getSharding(String str, String colName) {
        if ("apply_code".equals(colName)) {
            if (str.length() >= 15) {
                str = str.substring(str.length()-15, str.length()-10);
                return IdUtils.getLast5SharingValue(str);
            }
        }
        return IdUtils.getLast5SharingValue(str);
    }
}
