package com.tomas.mybatiesjdbc.config;

import com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceBuilder;
import com.tomas.mybatiesjdbc.config.algorithm.BusinessAndStoreIdShardingDatabaseAlgorithm;
import com.tomas.mybatiesjdbc.config.algorithm.BusinessAndStoreIdShardingTableAlgorithm;
import io.shardingsphere.api.config.ShardingRuleConfiguration;
import io.shardingsphere.api.config.TableRuleConfiguration;
import io.shardingsphere.api.config.strategy.ComplexShardingStrategyConfiguration;
import io.shardingsphere.api.config.strategy.NoneShardingStrategyConfiguration;
import io.shardingsphere.shardingjdbc.api.ShardingDataSourceFactory;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.core.env.Environment;
import org.springframework.core.io.DefaultResourceLoader;
import org.springframework.core.io.support.PathMatchingResourcePatternResolver;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;

import javax.sql.DataSource;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据源及分表配置
 */
@Configuration
@MapperScan(basePackages = "com.tomas.mybatiesjdbc.dao", sqlSessionTemplateRef = "shardingSqlSessionTemplate")
public class DataSourceConfig {


    @Autowired
    private Environment environment;

    @Bean
    public static PropertySourcesPlaceholderConfigurer propertyConfigure() {
        return new PropertySourcesPlaceholderConfigurer();
    }

    /**
     * 配置数据源0
     *
     * @return
     */
    @Bean(name = "ds_0")
    @ConfigurationProperties(prefix = "spring.ds0")
    @Qualifier("ds_0")
    @Primary
    public DataSource dataSource0() {
        return DruidDataSourceBuilder.create().build(environment, "spring.ds0");
    }

    /**
     * 配置数据源1
     *
     * @return
     */
    @Bean(name = "ds_1")
    @ConfigurationProperties(prefix = "spring.ds1")
    @Qualifier("ds_1")
    public DataSource dataSource1() {

        return DruidDataSourceBuilder.create().build(environment, "spring.ds1");
    }

    /**
     * 配置数据源2
     *
     * @return
     */
    @Bean(name = "ds_2")
    @ConfigurationProperties(prefix = "spring.ds2")
    @Qualifier("ds_2")
    public DataSource dataSource2() {

        return DruidDataSourceBuilder.create().build(environment, "spring.ds2");
    }

    /**
     * 配置数据源3
     *
     * @return
     */
    @Bean(name = "ds_3")
    @ConfigurationProperties(prefix = "spring.ds3")
    @Qualifier("ds_3")
    public DataSource dataSource3() {

        return DruidDataSourceBuilder.create().build(environment, "spring.ds3");
    }


    @Bean(name = "dataSource")
    public DataSource getShardingDataSource() throws SQLException {

        ShardingRuleConfiguration shardingRuleConfig = new ShardingRuleConfiguration();
        shardingRuleConfig.getTableRuleConfigs().add(getPriceStoreDetailRule());
        shardingRuleConfig.setDefaultDataSourceName("ds_0");
        shardingRuleConfig.setDefaultDatabaseShardingStrategyConfig(new NoneShardingStrategyConfiguration());
        shardingRuleConfig.setDefaultTableShardingStrategyConfig(new NoneShardingStrategyConfiguration());
        Properties p =  new Properties() ;
        p.put("sql.show", true) ;
        return ShardingDataSourceFactory.createDataSource(createDataSourceMap(),
            shardingRuleConfig, new ConcurrentHashMap<>(), p);
    }

    /**
     * 门店价格
     */
    private TableRuleConfiguration getPriceStoreDetailRule() {
        TableRuleConfiguration result = new TableRuleConfiguration();
        result.setLogicTable("price_store_detail");
        result.setActualDataNodes("ds_${0..3}.price_store_detail_${0..127}");
        // 分库策略 store_id 取模 2个
        result.setDatabaseShardingStrategyConfig(new ComplexShardingStrategyConfiguration("store_id", new BusinessAndStoreIdShardingDatabaseAlgorithm()));
        // 分表策略 store_id 取模 128个
        result.setTableShardingStrategyConfig(new ComplexShardingStrategyConfiguration("store_id", new BusinessAndStoreIdShardingTableAlgorithm()));
        return result;
    }

    private Map<String, DataSource> createDataSourceMap() {
        Map<String, DataSource> result = new HashMap<>();
        result.put("ds_0", dataSource0());
        result.put("ds_1", dataSource1());
        result.put("ds_2", dataSource2());
        result.put("ds_3", dataSource3());
        return result;
    }

    /**
     * 需要手动配置事务管理器
     *
     * @param dataSource
     * @return
     */
    @Bean
    public DataSourceTransactionManager transactitonManager(@Qualifier("dataSource") DataSource dataSource) {
        return new DataSourceTransactionManager(dataSource);
    }


    @Bean(name = "sqlSessionFactory")
    @Primary
    public SqlSessionFactory testSqlSessionFactory(@Qualifier("dataSource") DataSource dataSource) throws Exception {
        SqlSessionFactoryBean bean = new SqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setConfigLocation(new DefaultResourceLoader().getResource("classpath:mybatis/mybatis-config.xml"));
        bean.setMapperLocations(new PathMatchingResourcePatternResolver().getResources("classpath:mapper/**/*.xml"));
        return bean.getObject();
    }

    @Bean(name = "shardingSqlSessionTemplate")
    @Primary
    public SqlSessionTemplate testSqlSessionTemplate(@Qualifier("sqlSessionFactory") SqlSessionFactory sqlSessionFactory) throws Exception {
        return new SqlSessionTemplate(sqlSessionFactory);
    }



}
