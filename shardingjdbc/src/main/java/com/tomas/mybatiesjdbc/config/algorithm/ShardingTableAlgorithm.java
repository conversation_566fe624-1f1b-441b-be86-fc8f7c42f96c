package com.tomas.mybatiesjdbc.config.algorithm;


import com.cowell.framework.utils.IdUtils;
import io.shardingsphere.api.algorithm.sharding.PreciseShardingValue;
import io.shardingsphere.api.algorithm.sharding.standard.PreciseShardingAlgorithm;

import java.util.Collection;

/**
 * 订单分表算法
 *
 * @author: liubw
 * @date 2018/10/29 5:17 PM
 */
public class ShardingTableAlgorithm implements PreciseShardingAlgorithm<String> {

    public static final int TABLE_COUNT = 128;

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<String> shardingValue) {
        Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber(shardingValue.getValue(), 3));
        long ext = lastThreeNum % TABLE_COUNT;
        for (String name : availableTargetNames) {
            if (name.endsWith("_" + ext)) {
                return name;
            }
        }
        return null;
    }
}
