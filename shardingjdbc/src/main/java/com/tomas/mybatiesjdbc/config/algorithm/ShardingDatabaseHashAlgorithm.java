package com.tomas.mybatiesjdbc.config.algorithm;

import com.cowell.framework.utils.IdUtils;
import com.tomas.mybatiesjdbc.utils.CommonUtils;
import io.shardingsphere.api.algorithm.sharding.PreciseShardingValue;
import io.shardingsphere.api.algorithm.sharding.standard.PreciseShardingAlgorithm;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Collection;

/**
 * 订单分库算法
 *
 * @author: liubw
 * @date 2018/10/29 5:16 PM
 */
public class ShardingDatabaseHashAlgorithm implements PreciseShardingAlgorithm<String> {

    private static final int DB_COUNT = 512;

    private final Logger log = LoggerFactory.getLogger(ShardingDatabaseHashAlgorithm.class);

    @Override
    public String doSharding(Collection<String> availableTargetNames, PreciseShardingValue<String> shardingValue) {

        String hashResult = CommonUtils.getHashCode(shardingValue.getValue());
        log.info("哈希前:{},哈希后:{}",shardingValue.getValue(),hashResult);
        Long lastFiveNum = Long.parseLong(IdUtils.getLastNumber(hashResult, 5));
        Long lastThreeNum = Long.parseLong(IdUtils.getLastNumber(hashResult, 3));
        Long dbNum = (lastFiveNum - lastThreeNum) / 1000;
        long ext = dbNum % DB_COUNT;
        for (String name : availableTargetNames) {
            if (name.endsWith("_" + ext)) {
                return name;
            }
        }
        return null;
    }
}
