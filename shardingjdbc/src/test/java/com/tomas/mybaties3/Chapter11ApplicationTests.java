package com.tomas.mybaties3;

import com.tomas.mybatiesjdbc.dao.UserMapper;
import com.tomas.mybatiesjdbc.entity.VoteRecord;
import com.tomas.mybatiesjdbc.rest.HelloController;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;


@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class Chapter11ApplicationTests {

    private MockMvc mvc;

    @Autowired
    private UserMapper userMapper;

    @Before
    public void setUp() {
        mvc = MockMvcBuilders.standaloneSetup(new HelloController()).build();
    }

    @Test
    public void getHello() throws Exception {
       /* mvc.perform(MockMvcRequestBuilders.get("/hello").accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk())
                .andExpect(content().string(equalTo("Hello World")));*/
    }
    @Test
    @Transactional
    @Rollback(true)// 事务自动回滚，默认是true。可以不写
    public void selectByUserId() throws Exception {
        VoteRecord tomas = userMapper.selectByUserId("cfcd208495d565ef66e7dff9f98764da");
        log.info("selectByUserId tomas={}",tomas);
    }

    @Test
    @Transactional(rollbackFor = Exception.class)
    @Rollback(false)// 事务自动回滚，默认是true。可以不写
    public void updateVersionLockBatch() throws Exception {
        List<Long> ids=new ArrayList<>();
        ids.add(4L);
        ids.add(1L);
        ids.add(3L);

        List<VoteRecord>  voteRecords= userMapper.selectByIds(ids);
        if(!CollectionUtils.isEmpty(voteRecords)){
            voteRecords.forEach(v->{
                log.info("selectByUserId v={}",v);
                v.setVoteNum(v.getVoteNum()+1);
            });
            //voteRecords.get(1).setId(-1L);
            int i = userMapper.updateVersionLockBatch(voteRecords);
            log.info("selectByUserId i={}",i);
        }
        List<VoteRecord>  voteRecordsSecond= userMapper.selectByIds(ids);
        if(!CollectionUtils.isEmpty(voteRecordsSecond)){
            voteRecordsSecond.forEach(v->{
                log.info("voteRecordsSecond v={}",v);
                v.setVoteNum(v.getVoteNum()+2);
            });
            int q = userMapper.updateVersionLockBatchCaseWhen(voteRecordsSecond);
            log.info("voteRecordsSecond q={}",q);

        }
    }

    @Test
    @Transactional(rollbackFor = Exception.class)
    @Rollback(false)// 事务自动回滚，默认是true。可以不写
    public void batchUpdateVersionByUserId() throws Exception {
        List<Long> ids=new ArrayList<>();
        ids.add(4L);
        ids.add(1L);
        ids.add(3L);
        int i = userMapper.batchUpdateVersionByUserId("tomas", ids);
        System.out.println(i);
    }


}
