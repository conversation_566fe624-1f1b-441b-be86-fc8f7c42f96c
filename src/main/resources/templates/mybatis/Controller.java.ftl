package ${packageName}.controller;

import ${packageName}.entity${path}.${modelName};
import ${packageName}.service${path}.${serviceName};
<#if swaggerFlag>
import io.swagger.annotations.*;
</#if>
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import java.util.List;

<#if swaggerFlag>
@Api(value = "${modelName}管理", tags = {"${modelName}管理"})
</#if>
@RestController
@RequestMapping("/${modelName?uncap_first}")
public class ${controllerName}{

    @Autowired
    ${serviceName} ${serviceName?uncap_first};

    <#if swaggerFlag>
    @ApiOperation(value = "查询全部记录")
    </#if>
    @GetMapping("/getAll${modelName}s")
    public List<${modelName}> getAll${modelName}s(){
        return ${serviceName?uncap_first}.getAll${modelName}s();
    }

    <#if swaggerFlag>
    @ApiOperation(value = "根据条件查询记录")
    </#if>
    @GetMapping("/getAll${modelName}sBySearch")
    public List<${modelName}> getAll${modelName}sBySearch(${modelName} ${modelName?uncap_first}){
        return ${serviceName?uncap_first}.getAll${modelName}sBySearch(${modelName?uncap_first});
    }

    <#if swaggerFlag>
    @ApiOperation(value = "根据主键查询单条记录")
    </#if>
    @GetMapping("/get${modelName}")
    public ${modelName} get${modelName}(${primaryColumn.propertyType} ${primaryColumn.propertyName?uncap_first}){
        return ${serviceName?uncap_first}.get${modelName}By${primaryColumn.propertyName?cap_first}(${primaryColumn.propertyName?uncap_first});
    }

    <#if swaggerFlag>
    @ApiOperation(value = "保存")
    </#if>
    @PostMapping("/save${modelName}")
    public int save${modelName}(@RequestBody ${modelName} ${modelName?uncap_first}){
        return ${serviceName?uncap_first}.save${modelName}(${modelName?uncap_first});
    }

    <#if swaggerFlag>
    @ApiOperation(value = "删除")
    </#if>
    @GetMapping("/deleteBy${primaryColumn.propertyName}?cap_first")
    public int delete${modelName}(${primaryColumn.propertyType} ${primaryColumn.propertyName?uncap_first}){
        return ${serviceName?uncap_first}.delete${modelName}By${primaryColumn.propertyName?cap_first}(${primaryColumn.propertyName?uncap_first});
    }
}