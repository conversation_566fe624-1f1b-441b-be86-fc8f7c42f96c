package ${packageName}.service${path};

import ${packageName}.entity${path}.${modelName};
import org.springframework.stereotype.Service;
import java.util.List;

public interface ${serviceName}{

    /**
     * 查询全部数据
     *
     * @return
     */
    List<${modelName}> getAll${modelName}s();

    /**
     * 根据条件查询全部记录
     *
     * @param ${modelName?uncap_first}
     * @return
     */
    List<${modelName}> getAll${modelName}sBySearch(${modelName} ${modelName?uncap_first});

    /**
     * 根据主键获取对应记录
     *
     * @param ${primaryColumn.propertyName?uncap_first} 主键
     * @return
     */
    ${modelName} get${modelName}By${primaryColumn.propertyName?cap_first}(${primaryColumn.propertyType} ${primaryColumn.propertyName?uncap_first});

    /**
     * 新增记录(全部字段)
     *
     * @param ${modelName?uncap_first}
     * @return
     */
    int save${modelName}(${modelName} ${modelName?uncap_first});

    /**
     * 根据主键删除对应记录
     *
     * @param ${primaryColumn.propertyName?uncap_first} 主键
     * @return
     */
    int delete${modelName}By${primaryColumn.propertyName?cap_first}(${primaryColumn.propertyType} ${primaryColumn.propertyName?uncap_first});

<#if batchFlag>
    /**
    * 批量新增
    *
    * @param list 集合
    * @return
    */
    int insertBatch${modelName}s(List<${modelName}> list);

    /**
    * 批量修改
    *
    * @param list 集合
    * @return
    */
    int updateBatch${modelName}s(List<${modelName}> list);

    /**
    * 通过主键批量删除
    *
    * @param list 主键集合
    * @return
    */
    int deleteBatch${modelName}s(List<${primaryColumn.propertyType}> list);

</#if>
}