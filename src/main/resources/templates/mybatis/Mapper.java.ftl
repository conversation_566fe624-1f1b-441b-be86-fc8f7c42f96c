package ${packageName}.mapper${path};

import ${packageName}.entity${path}.${modelName};
import java.util.List;

public interface ${mapperName}{

    /**
     * 获取全部记录
     *
     * @return ${modelName}
     */
    List<${modelName}> getAll${modelName}s();

    /**
     * 获取全部记录
     *
     * @return ${modelName}
     */
    List<${modelName}> getAllBySearch(${modelName} ${modelName?uncap_first});

    /**
     * 根据主键获取对应记录
     *
     * @return ${modelName}
     */
    ${modelName} getBy${primaryColumn.propertyName?cap_first}(${primaryColumn.propertyType} ${primaryColumn.propertyName?uncap_first});

    /**
     * 新增记录(全部字段)
     *
     * @return int
     */
    int insert(${modelName} ${modelName?uncap_first});

    /**
     * 新增记录(不为空的字段)
     *
     * @return int
     */
    int insertSelective(${modelName} ${modelName?uncap_first});

    /**
     * 修改记录(全部字段)
     *
     * @return int
     */
    int update(${modelName} ${modelName?uncap_first});

    /**
     * 修改记录(不为空的字段)
     *
     * @return
     */
    int updateSelective(${modelName} ${modelName?uncap_first});

    /**
     * 根据主键删除对应记录
     *
     * @return int
     */
    int deleteBy${primaryColumn.propertyName?cap_first}(${primaryColumn.propertyType} ${primaryColumn.propertyName?uncap_first});

<#if batchFlag>
    /**
     * 批量新增
     *
     * @return int
     */
    int insertBatch(List<${modelName}> list);

    /**
     * 批量修改
     *
     * @return int
     */
    int updateBatchSelective(List<${modelName}> list);

    /**
     * 批量修改
     *
     * @return int
     */
    int updateBatch(List<${modelName}> list);

    /**
     * 通过主键批量删除 int
     *
     * @return
     */
    int deleteBatchBy${primaryColumn.propertyName?cap_first}s(List<${primaryColumn.propertyType}> list);

</#if>
}