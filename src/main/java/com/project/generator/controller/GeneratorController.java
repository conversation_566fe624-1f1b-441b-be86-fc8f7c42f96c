package com.project.generator.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.google.common.base.CaseFormat;
import com.project.generator.constant.ResponseResult;
import com.project.generator.model.*;
import com.project.generator.model.iscm.GoodsGiftCommonDTO;
import com.project.generator.model.iscm.GoodsGiftExportDTO;
import com.project.generator.model.iscm.GoodsGiftExportPOIDTO;
import com.project.generator.service.GeneratorService;
import com.project.generator.utils.DBUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/03/20
 * @Description
 */
@Slf4j
@RestController
public class GeneratorController {

    @Autowired
    private GeneratorService generatorService;

    @PostMapping("/connect")
    public ResponseResult connect(@RequestBody DataSource dataSource){
        if ("mysql".equals(dataSource.getDbType())){
           dataSource.setDriverClassName(DBUtils.DRIVER_CLASS_NAME_MYSQL8);
        }else if("postgresql".equals(dataSource.getDbType())){
            dataSource.setDriverClassName(DBUtils.DRIVER_CLASS_NAME_POSTGRESQL);
        }
        Connection connection = DBUtils.initDataSource(dataSource);
        if (connection != null) {
            return ResponseResult.success(dataSource.getDbType() + "连接成功");
        }

        return ResponseResult.fail(dataSource.getDbType() +"连接失败");
    }

    // 获取表单
    @PostMapping("/config")
    public ResponseResult configTable(@RequestBody JdbcParam param){
        if (!"mybatis".equals(param.getJdbcType())){
            ResponseResult.error("目前仅支持mybatis模板");
        }
        String packageName = param.getPackageName();
        try {
            Connection connection = DBUtils.getConnection();
            DatabaseMetaData metaData = connection.getMetaData();
            ResultSet tables = metaData.getTables(connection.getCatalog(), null, null, new String[]{"TABLE","VIEW"});
            List<TableClass> tableClassList = new ArrayList<>();
            while (tables.next()) {
                TableClass tableClass = new TableClass();
                tableClass.setPackageName(packageName);
                String table_name = tables.getString("TABLE_NAME");
                String modelName = CaseFormat.LOWER_UNDERSCORE.to(CaseFormat.UPPER_CAMEL, table_name);
                tableClass.setTableName(table_name);
                tableClass.setModelName(modelName);
                String remarks = tables.getString("remarks");
                if (StringUtils.isNotEmpty(remarks)) {
                    tableClass.setTableComment(modelName);
                } else {
                    tableClass.setTableComment(remarks);
                }
                tableClass.setControllerName(modelName + "Controller");
                tableClass.setMapperName(modelName + "Mapper");
                tableClass.setServiceName(modelName+"Service");
                tableClass.setServiceImplName(modelName+"ServiceImpl");
                tableClass.setVoName(modelName+"VO");
                tableClassList.add(tableClass);
            }
            return ResponseResult.success("数据库信息读取并配置成功", tableClassList);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ResponseResult.error("数据库信息读取失败!");
    }


    @PostMapping("/generateCode")
    public ResponseResult generateCode(@RequestBody TableVO tableVO, HttpServletRequest req, HttpServletResponse response) {
        if (tableVO.getTableClassList()==null || tableVO.getTableClassList().size()==0){
            log.error("代码生成失败：表数据不能为空");
            return ResponseResult.error("代码生成失败：表数据不能为空");
        }
        return generatorService.generateCode(tableVO.getTableClassList(),tableVO,
                req.getSession().getServletContext().getRealPath("/"),response);
    }

    @PostMapping("/doImport2")
    public ResponseResult doImport2(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws IOException {
        List<GoodsGiftExportDTO> importDTOs =new ArrayList<>();
        List<GoodsGiftExportPOIDTO> importPOIDTOs = EasyExcel.read(file.getInputStream()).head(GoodsGiftExportPOIDTO.class).sheet().doReadSync();
        Set<String> hasMergeNoSet=new HashSet<>();
        for (GoodsGiftExportPOIDTO data : importPOIDTOs) {
            log.info("读取到数据:{}", JSONUtil.toJsonStr(data));
            // 判断日志名称不为空就是主数据
            GoodsGiftExportDTO dto = new GoodsGiftExportDTO();
            if(!org.springframework.util.StringUtils.isEmpty(data.getNo())&& !hasMergeNoSet.contains(data.getNo())) {
                dto.setNo(data.getNo());
                dto.setCompanyCode(data.getCompanyCode());
                dto.setGoodsNo(data.getGoodsNo());
                dto.setGoodsQuality(data.getGoodsQuality());
                dto.setStoreCodes(data.getStoreCodes());
                dto.setStartTime(data.getStartTime());
                dto.setEndTime(data.getEndTime());
                // 获取子类
                List<GoodsGiftCommonDTO> giftCommonDataList = new ArrayList<>();
                List<GoodsGiftExportPOIDTO> goodsGiftList = importPOIDTOs.stream().filter(v -> data.getNo().equals(v.getNo()) && data.getCompanyCode().equals(v.getCompanyCode()) && data.getGoodsNo().equals(v.getGoodsNo())).collect(Collectors.toList());
                goodsGiftList.stream().forEach(v->{
                    giftCommonDataList.add(new GoodsGiftCommonDTO(v.getGiftNo(),v.getGiftQuality()));
                });
                dto.setListGift(giftCommonDataList);
                importDTOs.add(dto);
                hasMergeNoSet.add(data.getNo());
            }
        }
        List<String> errors=new ArrayList<>();
        for (int i = 0; i < importDTOs.size(); i++) {
            GoodsGiftExportDTO importDTO = importDTOs.get(i);
            int line =  i + 1;
            //logger.info("验证数量 line={}  importDTO={}",line , JSONObject.toJSONString(importDTO));
            if (StringUtils.isBlank(importDTO.getNo())) {
                errors.add("第"+line+"行 序号为空");
                continue;
            }
            if (StringUtils.isBlank(importDTO.getCompanyCode())) {
                errors.add("序号:+"+importDTO.getNo()+" 连锁编码为空");
            }else {
                importDTO.setCompanyCode(StringUtils.trim(importDTO.getCompanyCode()));
            }
            if(StringUtils.isBlank(importDTO.getStoreCodes())) {
                errors.add("序号:+"+importDTO.getNo()+"  门店编码为空");
            }
            if (StringUtils.isBlank(importDTO.getGoodsNo())) {
                errors.add("序号:+"+importDTO.getNo()+" 主品编码为空");
            }
            int i1 = NumberUtils.toInt("6.000");
            if (StringUtils.isBlank(importDTO.getGoodsQuality()) ||  !NumberUtils.isNumber(importDTO.getGoodsQuality().trim()) ) {
                errors.add("序号:+"+importDTO.getNo()+"  主品数量异常");
            }
            Integer goodsQuality = Double.valueOf(importDTO.getGoodsQuality().trim()).intValue();
            if (goodsQuality <= 0 ) {
                errors.add("序号:+"+importDTO.getNo()+" 主品数量异常");
            }
            if (StringUtils.isBlank(importDTO.getStartTime()) ) {
                errors.add("序号:+"+importDTO.getNo()+" 开始时间异常");
            }
            if (StringUtils.isBlank(importDTO.getEndTime()) ) {
                errors.add("序号:+"+importDTO.getNo()+" 结束时间异常");
            }
            if (CollectionUtils.isNotEmpty(importDTO.getListGift())){
                importDTO.getListGift().stream().forEach(v -> {
                    if (StringUtils.isBlank(v.getGiftGoodsNo())) {
                        errors.add("序号:+"+importDTO.getNo()+" 赠品编码为空");
                    }
                    if (StringUtils.isBlank(v.getGiftGoodsQuality()) || !NumberUtils.isNumber(v.getGiftGoodsQuality().trim())  ) {
                        errors.add("序号:+"+importDTO.getNo()+" 赠品数量异常");
                    }
                    Integer goodsGiftQuality = Double.valueOf(v.getGiftGoodsQuality()).intValue();
                    if (goodsGiftQuality <= 0 ) {
                        errors.add("序号:+"+importDTO.getNo()+" 赠品数量异常");
                    }
                });
            }else {
                errors.add("序号:+"+importDTO.getNo()+" 赠品数据异常");
            }
        }

        return ResponseResult.success("数据库信息读取并配置成功");
    }


    @PostMapping("/doImport4")
    public ResponseResult doImport4(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws IOException {
        try{
            ImportParams importParams = new ImportParams();
            importParams.setTitleRows(2);
            //实际读取行数10001 >1w行 如果返回list大于1w 报错提示 行数不能大于1w
            importParams.setReadRows(10000);
            importParams.setKeyIndex(0);
            List<PromotionExcel> objects = ExcelImportUtil.importExcel(file.getInputStream(), PromotionExcel.class, importParams);
            log.info("objects={}", JSONUtil.parseArray(objects));
        }catch (Exception e){
            log.warn("excel导入解析异常,异常信息为：",e);
            //throw new BusinessErrorException("excel导入解析异常");
           return ResponseResult.fail("读取行数超过限制");
        }

        return ResponseResult.success("数据库信息读取并配置成功");
    }

    @PostMapping("/doImport")
    public ResponseResult doImport(HttpServletRequest request, @RequestParam("file") MultipartFile file) throws IOException {
        try{
            LimitExcelReadListener limitExcelReadListener=new LimitExcelReadListener(20);
            List<PromotionExcel> importPOIDTOs = EasyExcel.read(file.getInputStream()).headRowNumber(2).autoTrim(true).head(PromotionExcel.class).registerReadListener(limitExcelReadListener).sheet().doReadSync();
            log.info("objects={}", JSONUtil.parseArray(importPOIDTOs));
        }catch (Exception e){
            log.warn("excel导入解析异常,异常信息为：",e);
            //throw new BusinessErrorException("excel导入解析异常");
            return ResponseResult.fail("读取行数超过限制");
        }
        return ResponseResult.success("数据库信息读取并配置成功");
    }



    /**
     * 是否 Integer 类型
     * @param value
     * @return
     */
    public static boolean isValidInt(String value) {
        try {
            Integer.parseInt(value);
        } catch (NumberFormatException e) {
            return false;
        }
        return true;
    }



}
