package com.project.generator.utils;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SqlConverter {

    public static void main(String[] args) {
        String filePath = "/Users/<USER>/Desktop/EKKO1.sql"; // 请替换为实际路径

        try {
            BufferedReader reader = new BufferedReader(new FileReader(filePath));
            StringBuilder sqlBuilder = new StringBuilder();
            String line;

            while ((line = reader.readLine()) != null) {
                sqlBuilder.append(line).append("\n");
            }

            String hanaSql = sqlBuilder.toString();
            String mySql = convertToMySql(hanaSql);

            System.out.println("MySQL SQL Statements:\n" + mySql);

        } catch (IOException e) {
            e.printStackTrace();
        }
    }


        public static String convertToMySql(String hanaSql) {
            // 添加AUTO_INCREMENT的主键id字段
            String mysqlSql = hanaSql.replaceAll("CREATE TABLE `([A-Za-z_0-9]+)`", "CREATE TABLE `$1` (\n    `id` BIGINT(20) NOT NULL AUTO_INCREMENT,");

            // 转换NVARCHAR、VARCHAR为MySQL的VARCHAR
            mysqlSql = convertVarchar(mysqlSql);

            // 转换DECIMAL为MySQL的DECIMAL
            mysqlSql = convertDecimal(mysqlSql);

            // 转换SMALLINT、INTEGER为MySQL的INT类型
            mysqlSql = convertInt(mysqlSql);

            // 修改NOT NULL为可空
            mysqlSql = convertNotNull(mysqlSql);

            // 删除HANA特有的表选项
            mysqlSql = removeHanaOptions(mysqlSql);

            // 调整默认值语法为MySQL语法
            mysqlSql = adjustDefaultValues(mysqlSql);

            // 添加InnoDB引擎，utf8字符集
            mysqlSql = addEngineCharset(mysqlSql);

            // 添加主键
            mysqlSql = addPrimaryKey(mysqlSql);

            return mysqlSql;
        }

        private static String convertVarchar(String sql) {
            return sql.replaceAll("(NVARCHAR\\([0-9]+\\)|VARCHAR\\([0-9]+\\))", "VARCHAR$1");
        }

        private static String convertDecimal(String sql) {
            Pattern pattern = Pattern.compile("DECIMAL\\(([0-9]+),([0-9]+)\\)");
            Matcher matcher = pattern.matcher(sql);
            StringBuffer result = new StringBuffer();
            while (matcher.find()) {
                int precision = Integer.parseInt(matcher.group(1));
                int scale = Integer.parseInt(matcher.group(2));
                // 超过MySQL支持精度的类型，转换为DOUBLE
                if (precision > 65 || scale > 30) {
                    matcher.appendReplacement(result, "DOUBLE");
                } else {
                    matcher.appendReplacement(result, "DECIMAL($1,$2)");
                }
            }
            matcher.appendTail(result);
            return result.toString();
        }

        private static String convertInt(String sql) {
            return sql.replaceAll("(SMALLINT|INTEGER)", "INT");
        }

        private static String convertNotNull(String sql) {
            return sql.replaceAll("NOT NULL", "");
        }

        private static String removeHanaOptions(String sql) {
            return sql.replaceAll("(UNLOAD PRIORITY [0-9]+|\"COLUMN\" .*)", "");
        }

        private static String adjustDefaultValues(String sql) {
            return sql.replaceAll("'' NOT NULL", "DEFAULT ''");
        }

        private static String addEngineCharset(String sql) {
            return sql.replaceAll("ENGINE [A-Za-z_]+ DEFAULT CHARSET=\\w+", "ENGINE=InnoDB DEFAULT CHARSET=utf8mb4");
        }

        private static String addPrimaryKey(String sql) {
            return sql.replaceAll("PRIMARY KEY \\(`([A-Za-z_0-9]+)`\\)", "PRIMARY KEY (`id`,`$1`)");
        }
}
