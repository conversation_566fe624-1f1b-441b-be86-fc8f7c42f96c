package com.project.generator.utils;

/**
 * HutoolExcel 描述
 *
 * <AUTHOR>
 * @create 2023/3/31
 **/
public class HutoolExcel {


    public static void main(String[] args) {

       /* ByteArrayOutputStream os = new ByteArrayOutputStream();
        ExcelWriter writer = ExcelUtil.getBigWriter();;
        try {
            //docker镜像增加fontconfig ttf-dejavu字体支持
            //File destFile = new File(fileName);
            //转化实体类

            if(StringUtils.isNotBlank(mergeTitle)){
                writer.merge(alias.size() - 1, mergeTitle);
                Row row = writer.getOrCreateRow(0);
                row.setHeight((short) 2000);
                CellStyle cellStyle = writer.getOrCreateCellStyle(0,0);
                StyleUtil.setColor(cellStyle, (short) 13, FillPatternType.SOLID_FOREGROUND);
                StyleUtil.setAlign(cellStyle, HorizontalAlignment.LEFT, VerticalAlignment.CENTER);
            }
            if (CollectionUtils.isEmpty(resultList)) {
                resultList = new ArrayList();
                List list = new LinkedList();
                Set<String> set = alias.keySet();
                for (String key : set) {
                    list.add(alias.get(key));
                }
                resultList.add(list);
            }
            writer.setHeaderAlias(alias);
            writer.setOnlyAlias(true);
            writer.write(resultList, true);
            writer.flush(os);
            byte[] content = os.toByteArray();*/
    }
}
