package com.project.generator.model.iscm;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.format.NumberFormat;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.metadata.BaseRowModel;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;

@ColumnWidth(25)
public class GoodsGiftExportPOIDTO extends BaseRowModel {

    @ExcelProperty(value = "序号")
    private String no;

    @ExcelProperty(value = "企业编码")
    private String companyCode;

    @ExcelProperty(value = "门店编码列表")
    private String storeCodes;

    @ExcelProperty(value = "主品编码")
    private String goodsNo;

    @ExcelProperty(value = "主品数量")
    @NumberFormat("#.##")
    private String goodsQuality;

    @ExcelProperty(value = "开始时间")
    private String startTime;

    @ExcelProperty(value = "结束时间")
    private String endTime;

    @ExcelProperty({"赠品信息", "赠品编号"})
    private String giftNo;

    @ExcelProperty({"赠品信息", "赠品数量"})
    @NumberFormat("#.##")
    private String giftQuality;

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getStoreCodes() {
        return storeCodes;
    }

    public void setStoreCodes(String storeCodes) {
        this.storeCodes = storeCodes;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsQuality() {
        return goodsQuality;
    }

    public void setGoodsQuality(String goodsQuality) {
        this.goodsQuality = goodsQuality;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public String getGiftNo() {
        return giftNo;
    }

    public void setGiftNo(String giftNo) {
        this.giftNo = giftNo;
    }

    public String getGiftQuality() {
        return giftQuality;
    }

    public void setGiftQuality(String giftQuality) {
        this.giftQuality = giftQuality;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;

        if (!(o instanceof GoodsGiftExportPOIDTO)) return false;

        GoodsGiftExportPOIDTO that = (GoodsGiftExportPOIDTO) o;

        return new EqualsBuilder()
                .append(getNo(), that.getNo())
                .append(getCompanyCode(), that.getCompanyCode())
                .append(getStoreCodes(), that.getStoreCodes())
                .append(getGoodsNo(), that.getGoodsNo())
                .append(getGoodsQuality(), that.getGoodsQuality())
                .append(getStartTime(), that.getStartTime())
                .append(getEndTime(), that.getEndTime())
                .append(getGiftNo(), that.getGiftNo())
                .append(getGiftQuality(), that.getGiftQuality())
                .isEquals();
    }

    @Override
    public int hashCode() {
        return new HashCodeBuilder(17, 37)
                .append(getNo())
                .append(getCompanyCode())
                .append(getStoreCodes())
                .append(getGoodsNo())
                .append(getGoodsQuality())
                .append(getStartTime())
                .append(getEndTime())
                .append(getGiftNo())
                .append(getGiftQuality())
                .toHashCode();
    }
}
