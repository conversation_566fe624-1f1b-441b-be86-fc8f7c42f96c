package com.project.generator.model.iscm;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.util.Objects;

@Data
public class GoodsGiftCommonDTO {

    @ExcelProperty(value = "赠品编号")
    private String giftGoodsNo;

    @ExcelProperty(value = "赠品数量")
    private String giftGoodsQuality;

    public String getGiftGoodsNo() {
        return giftGoodsNo;
    }

    public void setGiftGoodsNo(String giftGoodsNo) {
        this.giftGoodsNo = giftGoodsNo;
    }

    public String getGiftGoodsQuality() {
        return giftGoodsQuality;
    }

    public void setGiftGoodsQuality(String giftGoodsQuality) {
        this.giftGoodsQuality = giftGoodsQuality;
    }

    public GoodsGiftCommonDTO(String giftGoodsNo, String giftGoodsQuality) {
        this.giftGoodsNo = giftGoodsNo;
        this.giftGoodsQuality = giftGoodsQuality;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof GoodsGiftCommonDTO)) return false;
        GoodsGiftCommonDTO that = (GoodsGiftCommonDTO) o;
        return Objects.equals(getGiftGoodsNo(), that.getGiftGoodsNo()) &&
                Objects.equals(getGiftGoodsQuality(), that.getGiftGoodsQuality());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getGiftGoodsNo(), getGiftGoodsQuality());
    }
}
