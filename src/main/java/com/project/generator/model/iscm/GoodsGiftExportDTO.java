package com.project.generator.model.iscm;

import com.alibaba.excel.annotation.ExcelProperty;

import java.util.List;
import java.util.Objects;

public class GoodsGiftExportDTO {

    @ExcelProperty(value = "序号")
    private String no;

    @ExcelProperty(value = "企业编码")
    private String companyCode;

    @ExcelProperty(value = "门店编码列表")
    private String storeCodes;

    @ExcelProperty(value = "主品编码")
    private String goodsNo;

    @ExcelProperty(value = "主品数量")
    private String goodsQuality;

    @ExcelProperty(value = "开始时间")
    private String startTime;

    @ExcelProperty(value = "结束时间")
    private String endTime;

    @ExcelProperty(value = "赠品信息")
    private List<GoodsGiftCommonDTO> listGift;

    public String getNo() {
        return no;
    }

    public void setNo(String no) {
        this.no = no;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode;
    }

    public String getStoreCodes() {
        return storeCodes;
    }

    public void setStoreCodes(String storeCodes) {
        this.storeCodes = storeCodes;
    }

    public String getGoodsNo() {
        return goodsNo;
    }

    public void setGoodsNo(String goodsNo) {
        this.goodsNo = goodsNo;
    }

    public String getGoodsQuality() {
        return goodsQuality;
    }

    public void setGoodsQuality(String goodsQuality) {
        this.goodsQuality = goodsQuality;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

    public List<GoodsGiftCommonDTO> getListGift() {
        return listGift;
    }

    public void setListGift(List<GoodsGiftCommonDTO> listGift) {
        this.listGift = listGift;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (!(o instanceof GoodsGiftExportDTO)) return false;
        GoodsGiftExportDTO that = (GoodsGiftExportDTO) o;
        return Objects.equals(getNo(), that.getNo()) &&
                Objects.equals(getCompanyCode(), that.getCompanyCode()) &&
                Objects.equals(getStoreCodes(), that.getStoreCodes()) &&
                Objects.equals(getGoodsNo(), that.getGoodsNo()) &&
                Objects.equals(getGoodsQuality(), that.getGoodsQuality()) &&
                Objects.equals(getStartTime(), that.getStartTime()) &&
                Objects.equals(getEndTime(), that.getEndTime());
    }

    @Override
    public int hashCode() {
        return Objects.hash(getNo(), getCompanyCode(), getStoreCodes(), getGoodsNo(), getGoodsQuality(), getStartTime(), getEndTime());
    }

}
