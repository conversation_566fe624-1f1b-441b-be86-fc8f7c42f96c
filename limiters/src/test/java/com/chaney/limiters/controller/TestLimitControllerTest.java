package com.chaney.limiters.controller;

import com.chaney.limiters.result.Result;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest
public class TestLimitControllerTest {

    @Autowired
    AccessLimitController accessLimitController;

    @Test
    public void rateLimiter() throws InterruptedException {
        for (int i= 0; i<50; i++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    Result result = accessLimitController.rateLimiter();
                    System.out.println(new Date()+"-rateLimiter-"+result.toString());
                }
            }).start();
        }
        Thread.sleep(2000L);
    }

    @Test
    public void counter() throws InterruptedException {
        for (int i= 0; i<50; i++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    System.out.println(new Date()+"-counter-"+accessLimitController.counter().toString());
                }
            }).start();
        }
        Thread.sleep(2000L);
    }

    @Test
    public void bucket() throws InterruptedException {
        for (int i= 0; i<50; i++) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    System.out.println(new Date()+"-bucket-"+accessLimitController.bucket().toString());
                }
            }).start();

        }
        Thread.sleep(2000L);
    }
}
