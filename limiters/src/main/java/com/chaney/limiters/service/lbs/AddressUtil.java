package com.chaney.limiters.service.lbs;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSONObject;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;


public class AddressUtil {

    private static Logger log = LoggerFactory.getLogger(AddressUtil.class);

    private static final String TENCENT_MAP_KEY = "BWRBZ-MRREL-B7WPJ-MQLHB-HEGQ7-ZFBJ4";

    public static Map<String,Long> getDistanceByLatLng(LbsQQRequestDTO requestDTO) {
        if (StringUtils.isBlank(requestDTO.getFrom()) || StringUtils.isBlank(requestDTO.getTo())) {
            return new HashMap<>();
        }
        Map<String, Long> distanceMap = new HashMap<>();
        String url = "https://apis.map.qq.com/ws/distance/v1/matrix";
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("mode", "bicycling");
        paramMap.put("from", requestDTO.getFrom());
        paramMap.put("to", requestDTO.getTo());
        paramMap.put("key", TENCENT_MAP_KEY);
        String[] toLocations = requestDTO.getTo().split(";");
        int retryCount = 0;
        final int maxRetries = 10;
        while (retryCount <= maxRetries) {
            try {
                String result = HttpUtil.get(url, paramMap);
                log.info("调用腾讯地图查询步行距离结束：{}", result);
                LbsQQResponseDTO response = JSONObject.parseObject(result, LbsQQResponseDTO.class);
                if (response == null || response.getResult() == null ||
                        response.getResult().getRows() == null || response.getResult().getRows().isEmpty() ||
                        toLocations.length == 0) {
                    return distanceMap;
                }
                if (Objects.nonNull(response.getStatus())
                        && CollectionUtils.isNotEmpty(response.getResult().getRows()) &&
                        CollectionUtils.isNotEmpty(response.getResult().getRows().get(0).getElements())) {
                    // 获取第一个row中的elements列表
                    List<LbsQQResponseDTO.Element> elements = response.getResult().getRows().get(0).getElements();
                    // 确保elements数量与目标坐标数量一致
                    if (elements.size() != requestDTO.getTo().split(";").length) {
                        throw new IllegalArgumentException("Elements count does not match toLocations count");
                    }
                    // 构建坐标到距离的映射
                    for (int i = 0; i < elements.size(); i++) {
                        distanceMap.put(requestDTO.getTo().split(";")[i], elements.get(i).getDistance());
                    }
                    return distanceMap;
                }
                // 如果没有获取到有效结果，进行重试
                retryCount++;
                if (retryCount <= maxRetries) {
                    log.info("腾讯地图查询步行距离未获取有效结果，第{}次重试", retryCount);
                    ThreadUtil.sleep(200);
                }
            } catch (Exception e) {
                retryCount++;
                if (retryCount <= maxRetries) {
                    log.error("调用腾讯地图查询步行距离异常，第{}次重试：", retryCount, e);
                    ThreadUtil.sleep(200);
                } else {
                    log.error("调用腾讯地图查询步行距离异常，重试{}次后失败：", maxRetries, e);
                }
            }
        }
        return distanceMap;
    }

    public static void main(String[] args) {
        LbsQQRequestDTO  aa=  new LbsQQRequestDTO() ;
        aa.setFrom("23.020685,113.489096");
        aa.setTo("23.019998,113.489190;23.014998,113.485190;23.219998,113.389190");
        System.out.println(getDistanceByLatLng(aa));
    }
}
