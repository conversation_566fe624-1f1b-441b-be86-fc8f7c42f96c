package com.chaney.limiters.service.lbs;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class LbsQQResponseDTO {
    private String message;
    private String request_id;
    private Result result;
    private Integer status;

    @Data
    @NoArgsConstructor
    public static class Result {
        private List<Row> rows;
    }

    @Data
    @NoArgsConstructor
    public static class Row {
        private List<Element> elements;
    }

    @Data
    @NoArgsConstructor
    public static class Element {
        private Long distance;
        private Long duration;
    }
}
