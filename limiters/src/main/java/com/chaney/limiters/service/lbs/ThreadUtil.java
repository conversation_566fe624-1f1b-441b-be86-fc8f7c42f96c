//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package com.chaney.limiters.service.lbs;

import org.apache.commons.lang3.RandomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class ThreadUtil {
    private static Logger logger = LoggerFactory.getLogger(ThreadUtil.class);
    private static String nightStartTime = "23:00:00";
    private static String nightEndTime = "07:00:00";
    private static String delayTimeForNight = "30";
    private static String delayTimeForDay = "100";

    public static int getRandomNum(int milliSecond) {
        try {
            return RandomUtils.nextInt(10, milliSecond);
        } catch (Exception e) {
            logger.warn("获取10~x内随机数异常：", e);
            return 0;
        }
    }

    public static int randomSleep() {
        int num = 0;

        try {
            num = getRandomNum(100);
            Thread.sleep((long)num);
        } catch (Exception e) {
            logger.warn("startSleepRandom睡眠异常：", e);
        }

        return num;
    }
}
