package com.chaney.limiters.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 导入实体类
 * <AUTHOR>
 */
@Data
public class PromotionExcel implements Serializable {

    /**
     * 商品编码
     */
    @Excel(name="商品编码")
    private String goodsCode;

    /**
     * 单笔订单条件商品要求数量
     */
    @Excel(name="单笔订单条件商品要求数量")
    private Integer orderRequireCount;

    /**
     * 单笔订单条件商品要求金额
     */
    @Excel(name="单笔订单条件商品要求金额")
    private BigDecimal orderRequireAmount;

    /**
     * 单店限购数量
     */
    @Excel(name="单店限购数量")
    private Integer storeOverloadCount;

    /**
     * 单日单门店限购数量
     */
    @Excel(name = "单日单门店限购数量")
    private Integer dayStoreOverloadCount;

    /**
     * 单日单会员限购数量
     */
    @Excel(name ="单日单会员限购数量")
    private Integer dayUserOverloadCount;

    /**
     * 单会员限购数量
     */
    @Excel(name ="单会员限购数量")
    private Integer userOverloadCount;

    /**
     * 单笔订单限购数量
     */
    @Excel(name ="单笔订单限购数量")
    private Integer orderOverloadCount;

    /**
     * 限购总量
     */
    @Excel(name ="限购总量")
    private Integer overloadCount;

    /**
     * 限购开始时间
     */
    @Excel(name ="限购开始时间")
    private Date startOverloadDate;

    /**
     * 限购结束时间
     */
    @Excel(name ="限购结束时间")
    private Date endOverloadDate;

    /**
     * 非限购价格选择 1录入,对应值为non_overload_price 2原价(非会员取零售价,会员取会员价) 3零售价
     */
    @Excel(name ="*非限购价格选择")
    private Integer nonOverloadPriceType;
    /**
     * 非限购价格
     */
    @Excel(name ="非限购价格")
    private BigDecimal nonOverloadPrice;

    /**
     *超量购买计价方式
     */
    @Excel(name ="超量购买计价方式")
    private Integer overPurchase;

    /**
     * 扣减类型  0整单立减  1按件立减
     */
    @Excel(name="扣减类型")
    private Integer reduceType;

    /**
     *返利模式
     */
    @Excel(name ="返利模式")
    private Integer rebateModel;

    /**
     * 单件返利金额(元)
     */
    @Excel(name ="单件返利金额")
    private BigDecimal rebateAmount;

    /**
     * 单份返利金额(元)
     */
    @Excel(name ="单份返利金额")
    private BigDecimal singleRebateAmount;

    /**
     * 返利协议号
     */
    @Excel(name ="返利协议号")
    private String rebateNum;

    /**
     * 返利厂家/供应商
     */
    @Excel(name ="返利厂家/供应商")
    private String rebateFactory;

    /**
     * 入组数量
     */
    @Excel(name ="入组数量")
    private Integer groupNum;

    /**
     * 必选目录
     */
    @Excel(name ="必选目录")
    private Integer conditionGoods;

    /**
     *单组限购数量
     */
    @Excel(name ="单组限购数量")
    private Integer singleGroupCount;
    /**
     * 1档满额(元)/第X件/满量(件)
     */
    @Excel(name ="1档满额(元)/第X件/满量(件)")
    private BigDecimal fristGear;

    /**
     *1档折扣
     */
    @Excel(name ="1档折扣")
    private BigDecimal fristDiscount;
    /**
     * 1档会员折扣
     */
    @Excel(name ="1档会员折扣")
    private BigDecimal fristMemberDiscount;

    /**
     * 1档立减现金(元)
     */
    @Excel(name = "1档立减现金(元)")
    private BigDecimal fristAmount;

    /**
     *1档会员立减现金(元)
     */
    @Excel(name = "1档会员立减现金(元)")
    private BigDecimal fristMemberAmount;

    /**
     * 1档价格选择  1输入 2零售价
     */
    @Excel(name = "*1档价格选择")
    private Integer fristSpecialType;

    /**
     * 1档会员价格选择  1输入 2会员价
     */
    @Excel(name = "*1档会员价格选择")
    private Integer fristSpecialMemberType;
    /**
     * 1档特价(元)
     */
    @Excel(name = "1档特价(元)")
    private BigDecimal fristPrice;

    /**
     *1档会员特价(元)
     */
    @Excel(name = "1档会员特价(元)")
    private BigDecimal fristMemberPrice;

    /**
     *1档赠品选择方式
     */
    @Excel(name ="1档赠品选择方式")
    private Integer fristGiftOption;

    /**
     * 1档加价品选择方式
     */
    @Excel(name ="1档加价品选择方式")
    private Integer fristIncreaseOption;

    /**
     * 1档赠品件/份数
     */
    @Excel(name="1档赠品/加价品件/份数")
    private Integer fristGiftNum;

    @Excel(name="1档加价购加价价格")
    private BigDecimal fristAddPrice;


    @Excel(name="1档单件兑换积分")
    private BigDecimal fristIncreasePoint;

    /**
     * 2档满额(元)/第X件/满量(件)
     */
    @Excel(name ="2档满额(元)/第X件/满量(件)")
    private BigDecimal secondGear;

    /**
     *2档折扣
     */
    @Excel(name ="2档折扣")
    private BigDecimal secondDiscount;
    /**
     * 2档会员折扣
     */
    @Excel(name ="2档会员折扣")
    private BigDecimal secondMemberDiscount;

    /**
     * 2档价格选择  1输入 2零售价
     */
    @Excel(name = "2档价格选择")
    private Integer secondSpecialType;

    /**
     * 2档会员价格选择  1输入 2会员价
     */
    @Excel(name = "2档会员价格选择")
    private Integer secondSpecialMemberType;

    /**
     * 2档特价(元)
     */
    @Excel(name = "2档特价(元)")
    private BigDecimal secondPrice;

    /**
     *2档会员特价(元)
     */
    @Excel(name = "2档会员特价(元)")
    private BigDecimal secondMemberPrice;

    /**
     *2档赠品选择方式
     */
    @Excel(name ="2档赠品选择方式")
    private Integer secondGiftOption;

    /**
     *2档加价品选择方式
     */
    @Excel(name ="2档加价品选择方式")
    private Integer secondIncreaseOption;

    /**
     *2档立减现金(元)
     */
    @Excel(name = "2档立减现金(元)")
    private BigDecimal secondAmount;

    /**
     *2档会员立减现金(元)
     */
    @Excel(name = "2档会员立减现金(元)")
    private BigDecimal secondMemberAmount;

    /**
     * 2档赠品件/份数
     */
    @Excel(name="2档赠品/加价品件/份数")
    private Integer secondGiftNum;

    @Excel(name="2档单件兑换积分")
    private BigDecimal secondIncreasePoint;

    @Excel(name="2档加价购加价价格")
    private BigDecimal secondAddPrice;

    /**
     * 3档满额(元)/第X件/满量(件)
     */
    @Excel(name ="3档满额(元)/第X件/满量(件)")
    private BigDecimal thirdGear;

    /**
     *3档折扣
     */
    @Excel(name ="3档折扣")
    private BigDecimal thirdDiscount;
    /**
     * 3档会员折扣
     */
    @Excel(name ="3档会员折扣")
    private BigDecimal thirdMemberDiscount;
    /**
     * 3档价格选择  1输入 2零售价
     */
    @Excel(name = "3档价格选择")
    private Integer thirdSpecialType;

    /**
     * 3档会员价格选择  1输入 2会员价
     */
    @Excel(name = "3档会员价格选择")
    private Integer thirdSpecialMemberType;
    /**
     * 3档特价(元)
     */
    @Excel(name = "3档特价(元)")
    private BigDecimal thirdPrice;

    /**
     *3档会员特价(元)
     */
    @Excel(name = "3档会员特价(元)")
    private BigDecimal thirdMemberPrice;

    /**
     *3档赠品选择方式
     */
    @Excel(name ="3档赠品选择方式")
    private Integer thirdGiftOption;

    /**
     *3档加价品选择方式
     */
    @Excel(name = "3档加价品选择方式")
    private Integer thirdIncreaseOption;

    /**
     *3档立减现金(元)
     */
    @Excel(name = "3档立减现金(元)")
    private BigDecimal thirdAmount;

    /**
     *3档会员立减现金(元)
     */
    @Excel(name = "3档会员立减现金(元)")
    private BigDecimal thirdMemberAmount;


    /**
     * 3档赠品件/份数
     */
    @Excel(name="3档赠品/加价品件/份数")
    private Integer thirdGiftNum;

    @Excel(name="3档单件兑换积分")
    private BigDecimal thirdIncreasePoint;

    @Excel(name="3档加价购加价价格")
    private BigDecimal thirdAddPrice;

    /**
     * 4档满额(元)/第X件/满量(件)
     */
    @Excel(name ="4档满额(元)/第X件/满量(件)")
    private BigDecimal fourthGear;

    /**
     *4档折扣
     */
    @Excel(name ="4档折扣")
    private BigDecimal fourthDiscount;
    /**
     * 4档会员折扣
     */
    @Excel(name ="4档会员折扣")
    private BigDecimal fourthMemberDiscount;
    /**
     * 4档价格选择  1输入 2零售价
     */
    @Excel(name = "4档价格选择")
    private Integer fourthSpecialType;

    /**
     * 4档会员价格选择  1输入 2会员价
     */
    @Excel(name = "4档会员价格选择")
    private Integer fourthSpecialMemberType;
    /**
     * 4档特价(元)
     */
    @Excel(name = "4档特价(元)")
    private BigDecimal fourthPrice;

    /**
     *4档会员特价(元)
     */
    @Excel(name = "4档会员特价(元)")
    private BigDecimal fourthMemberPrice;

    /**
     *4档赠品选择方式
     */
    @Excel(name ="4档赠品选择方式")
    private Integer fourthGiftOption;

    /**
     *4档立减现金(元)
     */
    @Excel(name = "4档立减现金(元)")
    private BigDecimal fourthAmount;

    /**
     *4档会员立减现金(元)
     */
    @Excel(name = "4档会员立减现金(元)")
    private BigDecimal fourthMemberAmount;

    /**
     *4档加价品选择方式
     */
    @Excel(name = "4档加价品选择方式")
    private Integer fourthIncreaseOption;

    /**
     * 4档赠品件/份数
     */
    @Excel(name="4档赠品/加价品件/份数")
    private Integer fourthGiftNum;

    @Excel(name="4档加价购加价价格")
    private BigDecimal fourthAddPrice;


    @Excel(name="4档单件兑换积分")
    private BigDecimal fourthIncreasePoint;

    /**
     * 5档满额(元)/第X件/满量(件)
     */
    @Excel(name ="5档满额(元)/第X件/满量(件)")
    private BigDecimal fifthGear;

    /**
     *5档折扣
     */
    @Excel(name ="5档折扣")
    private BigDecimal fifthDiscount;
    /**
     * 5档会员折扣
     */
    @Excel(name ="5档会员折扣")
    private BigDecimal fifthMemberDiscount;
    /**
     * 5档价格选择  1输入 2零售价
     */
    @Excel(name = "5档价格选择")
    private Integer fifthSpecialType;

    /**
     * 5档会员价格选择  1输入 2会员价
     */
    @Excel(name = "5档会员价格选择")
    private Integer fifthSpecialMemberType;
    /**
     * 5档特价(元)
     */
    @Excel(name = "5档特价(元)")
    private BigDecimal fifthPrice;

    /**
     *5档会员特价(元)
     */
    @Excel(name = "5档会员特价(元)")
    private BigDecimal fifthMemberPrice;

    /**
     *5档加价品选择方式
     */
    @Excel(name = "5档加价品选择方式")
    private Integer fifthIncreaseOption;

    /**
     *5档立减现金(元)
     */
    @Excel(name = "5档立减现金(元)")
    private BigDecimal fifthAmount;

    /**
     *5档会员立减现金(元)
     */
    @Excel(name = "5档会员立减现金(元)")
    private BigDecimal fifthMemberAmount;

    /**
     *5档赠品选择方式
     */
    @Excel(name ="5档赠品选择方式")
    private Integer fifthGiftOption;

    /**
     * 5档赠品件/份数
     */
    @Excel(name="5档赠品/加价品件/份数")
    private Integer fifthGiftNum;

    @Excel(name="5档加价购加价价格")
    private BigDecimal fifthAddPrice;

    @Excel(name="5档单件兑换积分")
    private BigDecimal fifthIncreasePoint;

    /**
     * 6档满额(元)/第X件/满量(件)
     */
    @Excel(name ="6档满额(元)/第X件/满量(件)")
    private BigDecimal sixthGear;
    /**
     * 6档价格选择  1输入 2零售价
     */
    @Excel(name = "6档价格选择")
    private Integer sixthSpecialType;

    /**
     * 6档会员价格选择  1输入 2会员价
     */
    @Excel(name = "6档会员价格选择")
    private Integer sixthSpecialMemberType;
    /**
     * 6档特价(元)
     */
    @Excel(name = "6档特价(元)")
    private BigDecimal sixthPrice;

    /**
     *6档会员特价(元)
     */
    @Excel(name = "6档会员特价(元)")
    private BigDecimal sixthMemberPrice;

    /**
     *6档立减现金(元)
     */
    @Excel(name = "6档立减现金(元)")
    private BigDecimal sixthAmount;

    /**
     *6档会员立减现金(元)
     */
    @Excel(name = "6档会员立减现金(元)")
    private BigDecimal sixthMemberAmount;

    @Excel(name="6档单件兑换积分")
    private BigDecimal sixthIncreasePoint;

    /**
     * 7档满额(元)/第X件/满量(件)
     */
    @Excel(name ="7档满额(元)/第X件/满量(件)")
    private BigDecimal seventhGear;

    /**
     * 7档价格选择  1输入 2零售价
     */
    @Excel(name = "7档价格选择")
    private Integer seventhSpecialType;

    /**
     * 7档会员价格选择  1输入 2会员价
     */
    @Excel(name = "7档会员价格选择")
    private Integer seventhSpecialMemberType;
    /**
     * 7档特价(元)
     */
    @Excel(name = "7档特价(元)")
    private BigDecimal seventhPrice;

    /**
     *7档会员特价(元)
     */
    @Excel(name = "7档会员特价(元)")
    private BigDecimal seventhMemberPrice;

    /**
     *7档立减现金(元)
     */
    @Excel(name = "7档立减现金(元)")
    private BigDecimal seventhAmount;

    /**
     *7档会员立减现金(元)
     */
    @Excel(name = "7档会员立减现金(元)")
    private BigDecimal seventhMemberAmount;

    @Excel(name="7档单件兑换积分")
    private BigDecimal seventhIncreasePoint;

    /**
     * 8档满额(元)/第X件/满量(件)
     */
    @Excel(name ="8档满额(元)/第X件/满量(件)")
    private BigDecimal eighthGear;
    /**
     * 8档价格选择  1输入 2零售价
     */
    @Excel(name = "8档价格选择")
    private Integer eighthSpecialType;

    /**
     * 8档会员价格选择  1输入 2会员价
     */
    @Excel(name = "8档会员价格选择")
    private Integer eighthSpecialMemberType;
    /**
     * 8档特价(元)
     */
    @Excel(name = "8档特价(元)")
    private BigDecimal eighthPrice;

    /**
     *8档会员特价(元)
     */
    @Excel(name = "8档会员特价(元)")
    private BigDecimal eighthMemberPrice;

    /**
     *8档立减现金(元)
     */
    @Excel(name = "8档立减现金(元)")
    private BigDecimal eighthAmount;

    /**
     *8档会员立减现金(元)
     */
    @Excel(name = "8档会员立减现金(元)")
    private BigDecimal eighthMemberAmount;

    @Excel(name="8档单件兑换积分")
    private BigDecimal eighthIncreasePoint;

    /**
     *9档满额(元)/第X件/满量(件)
     */
    @Excel(name ="9档满额(元)/第X件/满量(件)")
    private BigDecimal ninthGear;
    /**
     * 9档价格选择  1输入 2零售价
     */
    @Excel(name = "9档价格选择")
    private Integer ninthSpecialType;

    /**
     * 9档会员价格选择  1输入 2会员价
     */
    @Excel(name = "9档会员价格选择")
    private Integer ninthSpecialMemberType;
    /**
     * 9档特价(元)
     */
    @Excel(name = "9档特价(元)")
    private BigDecimal ninthPrice;

    /**
     *9档会员特价(元)
     */
    @Excel(name = "9档会员特价(元)")
    private BigDecimal ninthMemberPrice;

    /**
     *9档立减现金(元)
     */
    @Excel(name = "9档立减现金(元)")
    private BigDecimal ninthAmount;

    /**
     *9档会员立减现金(元)
     */
    @Excel(name = "9档会员立减现金(元)")
    private BigDecimal ninthMemberAmount;

    @Excel(name="9档单件兑换积分")
    private BigDecimal ninthIncreasePoint;

    /**
     *10档满额(元)/第X件/满量(件)
     */
    @Excel(name ="10档满额(元)/第X件/满量(件)")
    private BigDecimal tenthGear;
    /**
     * 10档价格选择  1输入 2零售价
     */
    @Excel(name = "10档价格选择")
    private Integer tenthSpecialType;

    /**
     * 10档会员价格选择  1输入 2会员价
     */
    @Excel(name = "10档会员价格选择")
    private Integer tenthSpecialMemberType;
    /**
     * 10档特价(元)
     */
    @Excel(name = "10档特价(元)")
    private BigDecimal tenthPrice;

    /**
     *10档会员特价(元)
     */
    @Excel(name = "10档会员特价(元)")
    private BigDecimal tenthMemberPrice;

    /**
     *10档立减现金(元)
     */
    @Excel(name = "10档立减现金(元)")
    private BigDecimal tenthAmount;

    /**
     *10档会员立减现金(元)
     */
    @Excel(name = "10档会员立减现金(元)")
    private BigDecimal tenthMemberAmount;

    @Excel(name="10档单件兑换积分")
    private BigDecimal tenthIncreasePoint;

    /**
     * 促销折扣方式
     */
    @Excel(name="促销折扣方式")
    private Integer discountType;

    /**
     * 促销费用来源
     */
    @Excel(name="促销费用来源")
    private Integer resource;

    /**
     * 有效期至
     */
    @Excel(name="有效期至")
    private Date validPeriod;

    /**
     * 近效期
     */
    @Excel(name="近效期")
    private Integer expireDate;

    @Excel(name="单月限购总量")
    private BigDecimal monthLimitedCount;
    @Excel(name = "单周限购总量")
    private BigDecimal weekLimitedCount;
    @Excel(name = "单笔订单最小金额")
    private BigDecimal orderLowestAmount;
    @Excel(name ="指定天数限购量值")
    private BigDecimal dayLimitedCount;
    @Excel(name ="指定天数限购天数")
    private BigDecimal dayLimit;

    @Excel(name="厂家投入")
    private BigDecimal factoryInvested;
    @Excel(name ="加提金额")
    private BigDecimal rebateAmountDvc;
    @Excel(name="流向要求")
    private String requireFlowStr;


    private String message;

    private static final long serialVersionUID = 1L;

    @Override
    public String toString(){
        return ToStringBuilder.reflectionToString(this);
    }
}

