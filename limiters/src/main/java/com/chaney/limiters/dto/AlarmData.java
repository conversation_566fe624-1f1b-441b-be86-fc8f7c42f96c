package com.cowell.pricecenter.service.dto.request;

import com.alibaba.fastjson.JSON;
import lombok.*;

import java.util.List;
import java.util.Optional;

/**
 * 预警消息数据JavaBean
 * 用于发送AlarmData预警消息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class AlarmData {

    private List<AlarmDataWrapper> data;

    public static AlarmData convertToBean(String json) {
        return JSON.parseObject(json, AlarmData.class);
    }

    /**
     * 外层包装类，包含data数组
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString
    public static class AlarmDataWrapper {

        private String namespace_code;

        private String tenant_code;

        private String aggregation_key;

        private Integer storm_send_flag;

        private String name;

        private String link_url;

        private String severity;

        private String cluster;

        private String group_name;

        private String rule_name;

        private Integer is_recovered;

        private String trigger_time;

        private String first_trigger_time;

        private Integer notify_cur_number;

        private String id;

        private Boolean api_channel;

        private List<TagData> tag_data;

        private AssignConfigs assign_configs;

        private TagData tags_map;

        /**
         * 标签数据内部类
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @ToString
        public static class TagData {

            private String __name__;

            private String single_rule_name;

            private String store_count;

            private String item_count;

            private String task_group_code;
        }

        /**
         * 分配配置内部类
         */
        @Data
        @Builder
        @NoArgsConstructor
        @AllArgsConstructor
        @ToString
        public static class AssignConfigs {

            private String send_channel;

            private String steps;

            private String email;

            private String userIds;

            private String wxIds;

            private Boolean has_assign_config;
        }
    }


    /**
     * 从任意前缀的字符串中提取末尾的数字后缀（支持_0,_1,_2,_3格式）
     * @param indexName 索引名称，如 xxx_0、abc_1、test_3 等
     * @return 提取到的数字（0-3），不符合格式则返回空
     */
    public static Optional<Integer> extractSuffix(String indexName) {
        // 校验字符串不为空且长度至少为2（如"a_0"）
        if (indexName == null || indexName.length() < 2) {
            return Optional.empty();
        }
        // 截取最后两位字符（格式应为"_X"）
        String lastTwoChars = indexName.substring(indexName.length() - 2);
        // 检查最后两位是否以"_"开头，且第二位是数字0-4
        if (lastTwoChars.startsWith("_")) {
            char numChar = lastTwoChars.charAt(1);
            if (numChar >= '0' && numChar <= '3') {
                return Optional.of(numChar - '0'); // 字符转数字
            }
        }

        return Optional.empty();
    }

    public static void main(String[] args) {

        System.out.println(extractSuffix("price_store_detail_0").orElse(0));
        System.out.println(extractSuffix("price_store_detail_1").orElse(0));
        System.out.println(extractSuffix("price_store_detail_2").orElse(0));
        System.out.println(extractSuffix("price_store_detail_3").orElse(0));
        System.out.println(extractSuffix("price_store_detail_4").orElse(0));



    }
    /**
     * // 使用Builder模式创建对象
     * AlarmData alarmData = AlarmData.builder()
     *     .namespaceCode("ZHGYL")
     *     .tenantCode("yjpt")
     *     .aggregationKey("价格中台-医保限价")
     *     .severity("1")
     *     .cluster("价格中台")
     *     .build();
     *
     * // 使用Builder创建内部类对象
     * AlarmData.TagData tagData = AlarmData.TagData.builder()
     *     .name("value")
     *     .singleRuleName("蓝十字-医保限价预警")
     *     .storeCount("10")
     *     .itemCount("24")
     *     .taskGroupCode("ybtj")
     *     .build();
     *
     * // 创建配置对象
     * AlarmData.AssignConfigs configs = AlarmData.AssignConfigs.builder()
     *     .sendChannel("qywx")
     *     .email("<EMAIL>,<EMAIL>")
     *     .userIds("193958859412502")
     *     .wxIds("2711273573910000,2228752386510000")
     *     .hasAssignConfig(true)
     *     .build();
     *
     * // 使用Stream API处理集合（Java 8特性）
     * List<AlarmData.TagData> tagList = Arrays.asList(tagData);
     * alarmData.setTagData(tagList);
     */

}

