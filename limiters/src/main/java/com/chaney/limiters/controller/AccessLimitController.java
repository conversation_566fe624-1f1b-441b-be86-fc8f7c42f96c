package com.chaney.limiters.controller;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import com.chaney.limiters.dto.PromotionExcel;
import com.chaney.limiters.enums.LimiterEnum;
import com.chaney.limiters.limiters.AccessLimit;
import com.chaney.limiters.result.CodeMsg;
import com.chaney.limiters.result.Result;
import com.chaney.limiters.service.AccessLimitService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.lang.reflect.Field;
import java.util.List;

@Controller
public class AccessLimitController {

    @Autowired
    private AccessLimitService accessLimitService;

    @RequestMapping("/counter")
    @ResponseBody
    @AccessLimit(qps = 30, limiterEnum = LimiterEnum.COUNT_LIMITER)
    public Result counter() {
        if (accessLimitService.countAcquire()) {
            // （业务逻辑）
            return new Result(CodeMsg.ACQUIRE_SUCCESS);
        }
        return new Result(CodeMsg.ACQUIRE_LIMITED);
    }

    @RequestMapping("/bucket")
    @ResponseBody
    @AccessLimit(qps = 30, limiterEnum = LimiterEnum.LEAKY_BUCKET_LIMITER)
    public Result bucket() {
        if (accessLimitService.budgetLimiterAcquire()) {
            // (业务逻辑)
            return new Result(CodeMsg.ACQUIRE_SUCCESS);
        }
        return new Result(CodeMsg.ACQUIRE_LIMITED);
    }

    @RequestMapping("/rateLimiter")
    @ResponseBody
    @AccessLimit(qps = 30, limiterEnum = LimiterEnum.MYRATE_LIMITER)
    public Result rateLimiter() {
        if (accessLimitService.rateLimiterAcquire()) {
            // （业务逻辑）
            return new Result(CodeMsg.ACQUIRE_SUCCESS);
        }
        return new Result(CodeMsg.ACQUIRE_LIMITED);
    }

    @RequestMapping("/tomas")
    @ResponseBody
    public Result tomas(MultipartFile file,int size) {
        //Assert.isTrue(GOODS_IMPORT_MAX_SIZE > detailDOList.size() ,ErrorEnum.GOODS_IMPORT_MAX_SIZE.getMsg());

        Assert.isTrue(100 >= size,"大于 100");
        try{
            ImportParams params = new ImportParams();
            params.setTitleRows(1);
            params.setReadRows(10000);
            params.setKeyIndex(0);
            //解析excel
            List<PromotionExcel> detailDOList = ExcelImportUtil.importExcel(file.getInputStream(), PromotionExcel.class,params);
            if (CollectionUtils.isNotEmpty(detailDOList)) {
                System.out.println("商品规则-商品信息导入，导入条数："+ detailDOList.size());
                detailDOList.stream().forEach(v->convertStringTrim(v));
                //System.out.println("商品规则-商品信息导入，导入条数："+ detailDOList.size());
            }
            //Assert.isTrue(detailDOList.size() > GOODS_IMPORT_MAX_SIZE,ErrorEnum.GOODS_IMPORT_MAX_SIZE.getMsg());
           // goodsConfig.handleGooodsImport(promotionId,detailDOList,userDTO,authentication,type,uuid);
        }catch (Exception e){
           // log.warn("解析excel文件失败",e);
           // throw new BusinessErrorException("解析excel文件失败");
        }
        return new Result(CodeMsg.SUCCESS);
    }

    /**
     * BigDecimal Integer 为<=0时，置为null
     * @param obj
     * @return
     */
    public static Object convertStringTrim(Object obj) {
        Class<?> classz = obj.getClass();
        Field[] fields = classz.getDeclaredFields();
        for(Field field : fields){
            String name = field.getName();
            if(name.equalsIgnoreCase("serialVersionUID")){
                continue;
            }
            String fieldClassName = field.getType().getSimpleName();
            if(!fieldClassName.equalsIgnoreCase("String")){
                continue;
            }
            String getMethodName = "get"+toFirstLetterUpperCase(name);
            String setMethodName = "set"+toFirstLetterUpperCase(name);
            try {
                Object value = classz.getMethod(getMethodName).invoke(obj);
                if(null != value && fieldClassName.equalsIgnoreCase("String")){
                    classz.getMethod(setMethodName,String.class).invoke(obj,((String)value).trim());
                }
            } catch (Exception e) {
                //logger.warn("转换异常", e);
                return obj;
            }
        }
        return obj;
    }
    private static String toFirstLetterUpperCase(String str) {
        if(str == null || str.length() < 2){
            return str;
        }
        String firstLetter = str.substring(0, 1).toUpperCase();
        return firstLetter + str.substring(1, str.length());
    }
}
