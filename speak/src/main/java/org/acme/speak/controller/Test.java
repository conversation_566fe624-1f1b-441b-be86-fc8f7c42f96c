package org.acme.speak.controller;

public class Test {

    public static void main(String[] args) {
//        String subscriptionKey = "a5ee36be6dd744afbb0833ffe3a784d2";
//        String subscriptionRegion = "eastasia";
//        SpeechConfig speechConfig = SpeechConfig.fromSubscription(subscriptionKey, subscriptionRegion);
//        // Note: the voice setting will not overwrite the voice element in input SSML.
//        speechConfig.setSpeechSynthesisVoiceName("zh-CN-XiaoxiaoMultilingualNeural");
//        AudioConfig audioConfig = AudioConfig.fromWavFileOutput("/tmp/tmp1.wav");
//        SpeechSynthesizer speechSynthesizer = new SpeechSynthesizer(speechConfig, audioConfig);
//        speechSynthesizer.SpeakText("这是一个测试问题");
//        System.out.println(".wav is ok");
//        speechSynthesizer.close();


     for(int i=1;i<128;i++) {
      String a=  "ALTER TABLE `cowell_cart`.`promotion_detail_"+i+"` MODIFY COLUMN `whether_promotion` tinyint(2) NOT NULL DEFAULT '-1' COMMENT '是否做促销 -1:空 0:不做 1:做' AFTER `promotion_reason`;";
         System.out.println(a);
     }
    }
}
