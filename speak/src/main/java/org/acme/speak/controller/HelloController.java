package org.acme.speak.controller;

import com.microsoft.cognitiveservices.speech.SpeechConfig;
import com.microsoft.cognitiveservices.speech.SpeechSynthesizer;
import com.microsoft.cognitiveservices.speech.audio.AudioConfig;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
@RestController
@RequestMapping("/speak")
public class HelloController {

    private static final int CHUNK_SIZE = 500; // 每个分割块的大小(以字符为单位)
    private  String OUTPUT_FILE_PATH = "/tmp/";
    private  String TEMP_FILE_PREFIX = "/tmp/chunk_";

    @PostMapping("/txt/import")
    public ResponseEntity importExcel(MultipartFile file) throws Exception {
        String text = new String(file.getBytes(), StandardCharsets.UTF_8);
        TEMP_FILE_PREFIX =  "/tmp/"+ Objects.requireNonNull(file.getOriginalFilename()).replace(".txt","");
        OUTPUT_FILE_PATH =  "/tmp/"+file.getOriginalFilename().replace(".txt","")+"_all.wav";
        processText(text,OUTPUT_FILE_PATH,TEMP_FILE_PREFIX);
        // 返回合成后的语音文件或其他响应
        System.out.println("import end ");
        return ResponseEntity.ok("Audio file generated: ");
    }

    private File processText(String text,String outFileName,String PREFIX) throws Exception {
        // 分割文本
        List<String> textChunks = splitTextIntoChunks(text, CHUNK_SIZE);
        System.out.println("count: "+textChunks.size());
        ExecutorService executor = Executors.newFixedThreadPool(3);
        // 调用微软语音API将每个块转换为语音文件
        List<Callable<File>> tasks = new ArrayList<>();
        for (int i = 0; i < textChunks.size(); i++) {
            final int index = i;
             tasks.add(() -> callMicrosoftTTS(textChunks.get(index), index,PREFIX).call());
        }
        List<File> wavFiles = executor.invokeAll(tasks).stream()
                .map(future -> {
                    try {
                        return future.get();
                    } catch (Exception e) {
                        throw new RuntimeException("Failed to generate WAV file", e);
                    }
                })
                .collect(Collectors.toList());
        executor.shutdown();
        try {
            executor.awaitTermination(1, TimeUnit.HOURS);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            // log.error("Thread interrupted during executor shutdown", e);
        }
        // 合并所有语音文件
        System.out.println("outFileName="+outFileName);
        File  outputFile=  new File(outFileName);
        AudioMerger.mergeWavFiles(wavFiles, outputFile);
        return outputFile;
    }
    private static List<String> splitTextIntoChunks(String text, int chunkSize) {
        List<String> chunks = new ArrayList<>();
        int start = 0;
        while (start < text.length()) {
            int end = Math.min(start + chunkSize, text.length());
            chunks.add(text.substring(start, end));
            start = end;
        }
        return chunks;
    }

    private static Callable<File> callMicrosoftTTS(final String text, final int index, String PREFIX) {
        return () -> {
            // Creates an instance of a speech config with specified subscription key and service region.
            String subscriptionKey = "a5ee36be6dd744afbb0833ffe3a784d2";
            String subscriptionRegion = "eastasia";
            SpeechConfig speechConfig = SpeechConfig.fromSubscription(subscriptionKey, subscriptionRegion);
            // Note: the voice setting will not overwrite the voice element in input SSML.
            speechConfig.setSpeechSynthesisVoiceName("zh-CN-XiaoxiaoMultilingualNeural");
            AudioConfig audioConfig = AudioConfig.fromWavFileOutput(PREFIX + index + ".wav");
            SpeechSynthesizer speechSynthesizer = new SpeechSynthesizer(speechConfig, audioConfig);
            speechSynthesizer.SpeakText(text);
            System.out.println(PREFIX+index+ ".wav is ok");
            speechSynthesizer.close();
            return new File(PREFIX + index + ".wav");
        };
    }

}
