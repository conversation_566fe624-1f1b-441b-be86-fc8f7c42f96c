package org.acme.speak.controller;

import javax.sound.sampled.AudioFileFormat;
import javax.sound.sampled.AudioFormat;
import javax.sound.sampled.AudioInputStream;
import javax.sound.sampled.AudioSystem;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.List;

public class AudioMerger {

    public static void mergeWavFiles(List<File> wavFiles, File outputFile) throws Exception {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        AudioFormat format = null;

        for (File wavFile : wavFiles) {
            try (AudioInputStream audioInputStream = AudioSystem.getAudioInputStream(wavFile)) {
                if (format == null) {
                    format = audioInputStream.getFormat();
                } else {
                    AudioFormat wavFormat = audioInputStream.getFormat();
                    if (!format.matches(wavFormat)) {
                        throw new IllegalArgumentException("Audio formats are not the same");
                    }
                }

                byte[] buffer = new byte[4096];
                int bytesRead;
                while ((bytesRead = audioInputStream.read(buffer)) != -1) {
                    out.write(buffer, 0, bytesRead);
                }
            }
        }

        byte[] bytes = out.toByteArray();
        try (AudioInputStream mergedStream = new AudioInputStream(new ByteArrayInputStream(bytes), format, bytes.length / format.getFrameSize())) {
            AudioSystem.write(mergedStream, AudioFileFormat.Type.WAVE, outputFile);
        }
    }
}