package com.tomas.juc.markword;

import java.util.ArrayList;
import java.util.List;

/**
 * ygc测试
 * -Xms10m -Xmx10m -Xmn5m -XX:+UseParallelGC -XX:+PrintGCDetails -XX:+PrintGCTimeStamps -Xloggc:gc.log
       设置10m堆大小，年轻代和老年代各分5m，年轻代里伊甸区4m、两个幸存者区都是0.5m
 * */
public class TestYoungGC2 {
    private static int _1MB = 1024*1024;
    public static void main(String[] args) throws InterruptedException {
        List cache = new ArrayList<byte[]>();
        //只能循环三次新增3M对象、到第4次就会发生ygc，因为eden本身不是完全4M可用的
        for (int i = 0; i < 400; i++){
            System.out.println("循环" + (i+1) + "开始");
            cache.add(new byte[_1MB]);
            cache.remove(0);
            System.out.println("循环" + (i+1) + "结束");
            Thread.sleep(100L);
        }
        System.out.println("此时ygc回收了3M垃圾剩余1M对象、但这1M对象也失去了引用，下一次ygc将被回收");
        cache.add(new byte[2*_1MB]);
        System.out.println("此时又新分配2M对象到eden, 最后新生代3M+, 老年代接近0M");
    }
}
