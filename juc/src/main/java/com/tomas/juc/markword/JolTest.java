package com.tomas.juc.markword;

import lombok.extern.slf4j.Slf4j;
import org.openjdk.jol.info.ClassLayout;


//如果无锁直接升级轻量级锁  sleep 4s 或者加JVM参数 -XX:BiasedLockingStartupDelay=0
@Slf4j
public class JolTest {

    public static void main(String[] args) throws InterruptedException {

        Object o = new Object();  //101
        System.out.println("无锁"+ClassLayout.parseInstance(o).toPrintable());
        System.out.println("101 匿名偏向锁"+ClassLayout.parseInstance(o).toPrintable());


        synchronized (o){
            System.out.println("主线程加锁偏向锁  "+Thread.currentThread().hashCode());
            System.out.println("主线程加锁偏向锁  "+ClassLayout.parseInstance(o).toPrintable());
        };

        new Thread(()->{
            synchronized (o){
                System.out.println("轻量级"+ClassLayout.parseInstance(o).toPrintable());
            };
        }).start();
        Thread.sleep(2000L);

        int i = 0;
        while (i<2){
            new Thread(()->{
                synchronized (o){
                    System.out.println("重量级锁"+ClassLayout.parseInstance(o).toPrintable());
                };
            }).start();
            i++;
        }

        Thread.sleep(2000L);
        System.out.println("无锁 ok"+ClassLayout.parseInstance(o).toPrintable());

    }
}
