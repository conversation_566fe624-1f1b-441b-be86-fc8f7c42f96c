package com.tomas.juc.volatilelean;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * VolatileLean 描述
 *
 * <AUTHOR>
 * @create 2021/1/12
 **/
public class VolatileLean {

   public static volatile int visibilityNumber = 0;
    public static   int parkNumber = 0;
    public static void main(String[] args) {
       // volatileVisibilityDemo();
       // unVisibilityDemo();

        //casDemo();
        atomicDemo();
    }

    //volatile可以保证可见性，及时通知其它线程主物理内存的值已被修改
    public static void volatileVisibilityDemo() {
        System.out.println("visibilityNumber可见性测试");
        MyData myData = new MyData();//资源类
        //启动一个线程操作共享数据
        new Thread(() -> {
            System.out.println(Thread.currentThread().getName() + "\t 执行");
            try {
                TimeUnit.SECONDS.sleep(3);
                myData.setTo60();
                System.out.println(Thread.currentThread().getName() + "\t 更新visibilityNumber值: " + myData.visibilityNumber);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }, "ThreadA").start();
        while (myData.visibilityNumber == 0) {
            //main线程持有共享数据的拷贝，一直为0
        }
        System.out.println(Thread.currentThread().getName() + "\t main获取visibilityNumber值: " + myData.visibilityNumber);
    }



    //非 volatile 变量 不保证可见性
    public static void unVisibilityDemo() {
        System.out.println("UnVisibilityDemo 可见性测试");
        MyData myData = new MyData();//资源类
        //启动一个线程操作共享数据
        new Thread(() -> {
            System.out.println(Thread.currentThread().getName() + "\t 执行");
            try {
                TimeUnit.SECONDS.sleep(3);
                myData.setTo60();
                System.out.println(Thread.currentThread().getName() + "\t 更新unVisibilityNumber值: " + myData.unVisibilityNumber);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }, "ThreadB").start();
        while (myData.unVisibilityNumber == 0) {
            //main线程持有共享数据的拷贝，一直为0
        }
        System.out.println(Thread.currentThread().getName() + "\t main获取unVisibilityNumber值: " + myData.unVisibilityNumber);
    }


    //cas原子性
    public static void casDemo() {
        System.out.println("casDemo 原子性");
        MyData myData = new MyData();//资源类
        //启动一个线程操作共享数据
        for (int i = 0; i < 120; i++) {
            new Thread(() -> {
                for (int j = 0; j < 1000; j++) {
                    myData.addPlus();
                    myData.casPlus();
                }
            }, "ThreadD-"+i).start();
        }
        while (Thread.activeCount()>=2){
            //System.out.println("2.0："+Thread.activeCount());
            Thread.yield();
        }
        System.out.println(Thread.currentThread().getName() + "\t main获取atomicInteger值: " + myData.atomicInteger);
        System.out.println(Thread.currentThread().getName() + "\t main获取volatile值: " + myData.visibilityNumber);
    }

    private static void atomicDemo() {
        System.out.println("原子性测试 开始");
        long time = System.currentTimeMillis();
        MyData myData=new MyData();

        for (int i = 1; i <= 2; i++) {
            new Thread(()->{
                for (int j = 0; j <100000000 ; j++) {
                    myData.casPlus();
                }
            },String.valueOf(i)).start();
        }
        while (Thread.activeCount()>=2){
            Thread.yield();
        }
        long hs=System.currentTimeMillis()-time;
        System.out.println(Thread.currentThread().getName()+"\t int类型最终number值: "+myData.atomicInteger+"耗时:"+hs);
        long time2 = System.currentTimeMillis();
        for (long j = 0; j <2000000000L ; j++) {
            myData.addPlusPark();
        }
        long hs2=System.currentTimeMillis()-time2;
        System.out.println(Thread.currentThread().getName()+"\t int类型最终unVisibilityNumber值: "+myData.unVisibilityNumber+"耗时:"+hs2);

        //System.out.println(Thread.currentThread().getName()+"\t AtomicInteger类型最终number值: "+myData.atomicInteger);
    }


}

class MyData{

    /**
     * 依次验证
     * 1.保证可见性
     * 2.不保证原子性
     * 3.禁止指令重排序
     */

    int unVisibilityNumber = 0;


    volatile int visibilityNumber = 0;


    AtomicInteger  atomicInteger=new AtomicInteger();


    public void addPlus(){

        visibilityNumber++;

    }
    public void addPlusPark(){

        unVisibilityNumber++;

    }


    public void casPlus(){

        atomicInteger.getAndIncrement();

    }

    public void setTo60() {

        visibilityNumber=60;

        unVisibilityNumber=60;
    }
}
