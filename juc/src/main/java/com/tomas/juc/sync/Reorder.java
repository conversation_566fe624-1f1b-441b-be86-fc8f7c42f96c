package com.tomas.juc.sync;

import org.openjdk.jol.info.ClassLayout;

public class Reorder {

    private Object e=new Object();
    
    public static void main(String[] args) throws Exception{
        Reorder reorder =  new Reorder ();
        reorder.testSync();
    }

    public  void testSync() throws Exception{

        synchronized (e) {
            System.out.println("轻量级"+ ClassLayout.parseInstance(e).toPrintable());
        }

    }



}
