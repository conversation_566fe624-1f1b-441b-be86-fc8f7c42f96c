package com.tomas.juc.lock;

import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

/**
 * MyLock 描述
 *
 * <AUTHOR>
 * @create 2021/1/28
 **/
public class MySynchronizePrint {


    public static void main(String[] args) {
        Data data=new Data();
        for (int i = 1; i < 10; i++) {
            new Thread(()->{
                data.printA();
            },String.valueOf(i)).start();
        }
        for (int i = 1; i < 10; i++) {
            new Thread(()->{
                data.printB();
            },String.valueOf(i)).start();
        }

        for (int i = 1; i < 10; i++) {
            new Thread(()->{
                data.printC();
            },String.valueOf(i)).start();
        }
    }


}


class SynchronizeData{

    ReentrantLock lock=new ReentrantLock();

    Condition conditionA= lock.newCondition();
    Condition conditionB= lock.newCondition();
    Condition conditionC= lock.newCondition();

    int number = 1;


    public void printA(){
        lock.lock();
        try {
            while (number!=1){
                conditionA.await();
            }
            System.out.println("执行了 A");
            number=2;
            conditionB.signal();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.unlock();
        }

    }
    public void printB(){
        lock.lock();
        try {
            while (number!=2){
                conditionB.await();
            }
            System.out.println("执行了 B");
            number=3;
            conditionC.signal();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.unlock();
        }

    }

    public void printC(){
        lock.lock();
        try {
            while (number!=3){
                conditionC.await();
            }
            System.out.println("执行了 C");
            number=1;
            conditionA.signal();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            lock.unlock();
        }

    }


}
