package com.tomas.leecode;

/**
 * LuckyString 描述
 *
 * <AUTHOR>
 * @create 2021/1/15
 **/
public class LuckyString {

    public static void main(String[] args) {
       // System.out.println(findFirstRepeat("jkdsfalhkfoweqirwe"));

        System.out.println(getMaxDis(new int[]{1, 4, 3,5,0,9}));
    }

    /**
     * 首个重复字符
     * @param A
     * @return
     */
    public static char findFirstRepeat(String A) {
        boolean[] hasAppear = new boolean[256];
        for (int i = 0; i < A.length(); i++) {
            char c = A.charAt(i);
            if(hasAppear[c]) {
                return c;
            }
            hasAppear[c] = true;
        }
        return ' ';
    }

    /**
     * 最大差值
     * 贪心算法
     * @param A
     * @return
     */
    public static int getMaxDis(int[] A) {
        int max = 0;
        int soFarMin = A[0];
        for (int i = 1; i < A.length; i++) {
            if(soFarMin > A[i]) {
                soFarMin = A[i];
            }else{
                max = Math.max(max, A[i]- soFarMin);
            }
        }
        return max;
    }
}
