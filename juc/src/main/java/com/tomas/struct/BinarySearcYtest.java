package com.tomas.struct;

/**
 * BinarySearcYtest 描述
 *
 * <AUTHOR>
 * @create 2021/1/25
 **/
public class BinarySearcYtest {


    /**
     * 二分查找算法 依赖有序数组
     * @param arr
     * @param data
     * @return
     */
    public static int binarySearche(int[] arr,  int  data){
        /**
         * 1. 找到数组的 中间下标 mid =  low+ (height-low)/2
         * 2. 如果中间下标对应数据 等于 data 直接返回下标
         * 3. 如果 大于 data    height=mid-1;
         * 4. 如果 小于 data    low=mid+1
         */
        int low=0;
        int height = arr.length-1;
        while (low < height){
            int mid = low + (height-low)/2;
            if(arr[mid]<data){
                low= mid+1;
            }else if(arr[mid]==data){
                return mid;
            }else {
                height= mid -1;
            }
        }

        return -1;
    }
    // AVL 树  平衡二叉树 节点的

    //红黑树 是一个特殊的平衡二叉树


}
