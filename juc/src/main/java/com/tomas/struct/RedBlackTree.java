package com.tomas.struct;

/**
 * RedBlackTree 描述
 *   特殊的二叉排序树 黑色完美平衡
 *
 *  特有性质:
 *  1. 每个节点 要么红 要么黑
 *  2. 根节点必须是黑色
 *  3. 每个叶子节点必须是黑色
 *  4. 不能有个红色节点相连(每个红色节点必须有两个黑色叶子节点)
 *  5. 黑高(任意一节点到每个叶子节点的路径都包含数量相同的 黑节点 )
 *
 * <AUTHOR>
 * @create 2021/1/25
 **/
public class RedBlackTree<K  extends  Comparable<K>> {




    /**
     *  https://www.bilibili.com/video/av83540396?p=3    10:48 秒
     * //左旋
     *  root 节点 的 右子节点 变成  new root 根节点
     *
     *  old root 节点 变成 原来右子节点的 左子节点
     *
     *  原来右子节点的左子节点 变成 old root 的 右子节点
     *
     */
    class  RBNode {

        RBNode parent;
        RBNode left;
        RBNode right;

    }

}


